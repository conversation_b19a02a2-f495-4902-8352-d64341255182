// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalPayRefundDao is internal type for wrapping internal DAO implements.
type internalPayRefundDao = *internal.PayRefundDao

// payRefundDao is the data access object for table hg_pay_refund.
// You can define custom methods on it to extend its functionality as you wish.
type payRefundDao struct {
	internalPayRefundDao
}

var (
	// PayRefund is globally public accessible object for table hg_pay_refund operations.
	PayRefund = payRefundDao{
		internal.NewPayRefundDao(),
	}
)

// Fill with you ideas below.
