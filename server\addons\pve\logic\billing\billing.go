// Package billing
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package billing

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// BillingType 计费类型
type BillingType string

const (
	BillingTypeHourly  BillingType = "hourly"  // 按小时计费
	BillingTypeDaily   BillingType = "daily"   // 按天计费
	BillingTypeMonthly BillingType = "monthly" // 按月计费
	BillingTypeYearly  BillingType = "yearly"  // 按年计费
)

// InstanceBillingInfo 实例计费信息
type InstanceBillingInfo struct {
	InstanceID   uint64      `json:"instanceId"`
	CPU          int         `json:"cpu"`
	Memory       int         `json:"memory"`
	Disk         int         `json:"disk"`
	BillingType  BillingType `json:"billingType"`
	UnitPrice    float64     `json:"unitPrice"`    // 单位价格
	UsageHours   float64     `json:"usageHours"`   // 使用小时数
	TotalAmount  float64     `json:"totalAmount"`  // 总费用
	CreatedAt    *gtime.Time `json:"createdAt"`
	ExpiredAt    *gtime.Time `json:"expiredAt"`
}

// CalculateInstanceCost 计算实例费用
func CalculateInstanceCost(ctx context.Context, instanceID uint64, billingType BillingType, duration int) (*InstanceBillingInfo, error) {
	// 获取实例配置信息
	instance, err := g.DB().Model("hg_pve_instances").Where("id", instanceID).One()
	if err != nil {
		return nil, fmt.Errorf("获取实例信息失败: %v", err)
	}
	if instance == nil {
		return nil, fmt.Errorf("实例 %d 不存在", instanceID)
	}

	cpu := gconv.Int(instance["cpu"])
	memory := gconv.Int(instance["memory"])
	disk := gconv.Int(instance["disk"])
	
	// 获取计费配置
	config, err := getBillingConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取计费配置失败: %v", err)
	}

	// 计算基础价格（CPU + 内存 + 存储）
	cpuPrice := float64(cpu) * config.CPUUnitPrice
	memoryPrice := float64(memory) * config.MemoryUnitPrice / 1024 // 内存按GB计费
	diskPrice := float64(disk) * config.DiskUnitPrice
	basePrice := cpuPrice + memoryPrice + diskPrice

	// 根据计费类型计算总价
	var totalAmount float64
	var usageHours float64
	
	switch billingType {
	case BillingTypeHourly:
		totalAmount = basePrice * float64(duration)
		usageHours = float64(duration)
	case BillingTypeDaily:
		totalAmount = basePrice * 24 * float64(duration) * config.DailyDiscount
		usageHours = float64(duration * 24)
	case BillingTypeMonthly:
		totalAmount = basePrice * 24 * 30 * float64(duration) * config.MonthlyDiscount
		usageHours = float64(duration * 24 * 30)
	case BillingTypeYearly:
		totalAmount = basePrice * 24 * 365 * float64(duration) * config.YearlyDiscount
		usageHours = float64(duration * 24 * 365)
	default:
		return nil, fmt.Errorf("不支持的计费类型: %s", billingType)
	}

	// 计算到期时间
	var expiredAt *gtime.Time
	now := gtime.Now()
	switch billingType {
	case BillingTypeHourly:
		expiredAt = gtime.New(now.Add(time.Hour * time.Duration(duration)))
	case BillingTypeDaily:
		expiredAt = gtime.New(now.Add(time.Hour * 24 * time.Duration(duration)))
	case BillingTypeMonthly:
		expiredAt = gtime.New(now.AddDate(0, duration, 0))
	case BillingTypeYearly:
		expiredAt = gtime.New(now.AddDate(duration, 0, 0))
	}

	return &InstanceBillingInfo{
		InstanceID:  instanceID,
		CPU:         cpu,
		Memory:      memory,
		Disk:        disk,
		BillingType: billingType,
		UnitPrice:   basePrice,
		UsageHours:  usageHours,
		TotalAmount: totalAmount,
		CreatedAt:   now,
		ExpiredAt:   expiredAt,
	}, nil
}

// CreateRenewalOrder 创建续费订单
func CreateRenewalOrder(ctx context.Context, instanceID uint64, billingType BillingType, duration int, userID uint64) (uint64, error) {
	// 计算费用
	billing, err := CalculateInstanceCost(ctx, instanceID, billingType, duration)
	if err != nil {
		return 0, err
	}

	// 获取实例名称
	instance, err := g.DB().Model("hg_pve_instances").Where("id", instanceID).One()
	if err != nil {
		return 0, err
	}
	instanceName := gconv.String(instance["name"])

	// 创建订单记录
	orderData := g.Map{
		"order_no":       generateOrderNo(),
		"instance_id":    instanceID,
		"instance_name":  instanceName,
		"user_id":        userID,
		"billing_type":   string(billingType),
		"duration":       duration,
		"unit_price":     billing.UnitPrice,
		"total_amount":   billing.TotalAmount,
		"status":         "pending", // pending, paid, cancelled
		"expired_at":     billing.ExpiredAt,
		"created_at":     gtime.Now(),
		"updated_at":     gtime.Now(),
	}

	orderID, err := g.DB().Model("hg_pve_billing_orders").Data(orderData).InsertAndGetId()
	if err != nil {
		return 0, fmt.Errorf("创建续费订单失败: %v", err)
	}

	g.Log().Info(ctx, "创建PVE实例续费订单成功", g.Map{
		"orderId":    orderID,
		"instanceId": instanceID,
		"amount":     billing.TotalAmount,
		"userId":     userID,
	})

	return gconv.Uint64(orderID), nil
}

// ProcessOrderPayment 处理订单支付
func ProcessOrderPayment(ctx context.Context, orderID uint64, paymentMethod string) error {
	// 获取订单信息
	order, err := g.DB().Model("hg_pve_billing_orders").Where("id", orderID).One()
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单 %d 不存在", orderID)
	}

	if gconv.String(order["status"]) != "pending" {
		return fmt.Errorf("订单状态无效")
	}

	// 更新订单状态为已支付
	_, err = g.DB().Model("hg_pve_billing_orders").
		Data(g.Map{
			"status":         "paid",
			"payment_method": paymentMethod,
			"paid_at":        gtime.Now(),
			"updated_at":     gtime.Now(),
		}).
		Where("id", orderID).
		Update()
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	// 延长实例有效期
	instanceID := gconv.Uint64(order["instance_id"])
	newExpiredAt := gconv.Time(order["expired_at"])
	
	_, err = g.DB().Model("hg_pve_instances").
		Data(g.Map{
			"expired_at":  newExpiredAt,
			"updated_at":  gtime.Now(),
		}).
		Where("id", instanceID).
		Update()
	if err != nil {
		return fmt.Errorf("更新实例到期时间失败: %v", err)
	}

	g.Log().Info(ctx, "PVE实例续费成功", g.Map{
		"orderId":      orderID,
		"instanceId":   instanceID,
		"newExpiredAt": newExpiredAt,
	})

	return nil
}

// BillingConfig 计费配置
type BillingConfig struct {
	CPUUnitPrice     float64 `json:"cpuUnitPrice"`     // CPU单价（每核心/小时）
	MemoryUnitPrice  float64 `json:"memoryUnitPrice"`  // 内存单价（每MB/小时）
	DiskUnitPrice    float64 `json:"diskUnitPrice"`    // 磁盘单价（每GB/小时）
	DailyDiscount    float64 `json:"dailyDiscount"`    // 按天计费折扣
	MonthlyDiscount  float64 `json:"monthlyDiscount"`  // 按月计费折扣
	YearlyDiscount   float64 `json:"yearlyDiscount"`   // 按年计费折扣
}

// getBillingConfig 获取计费配置
func getBillingConfig(ctx context.Context) (*BillingConfig, error) {
	// 从数据库或配置文件获取计费配置
	// 这里使用默认配置，实际应用中应该从配置表获取
	return &BillingConfig{
		CPUUnitPrice:    0.05,  // 每核心每小时0.05元
		MemoryUnitPrice: 0.01,  // 每MB每小时0.01元  
		DiskUnitPrice:   0.001, // 每GB每小时0.001元
		DailyDiscount:   0.9,   // 按天计费9折
		MonthlyDiscount: 0.8,   // 按月计费8折
		YearlyDiscount:  0.7,   // 按年计费7折
	}, nil
}

// generateOrderNo 生成订单号
func generateOrderNo() string {
	return fmt.Sprintf("PVE%s%d", gtime.Now().Format("20060102150405"), gtime.Now().Nanosecond()/1000000)
}