// Package logic
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"fmt"
	"time"

	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sPveNodes struct{}

func init() {
	service.RegisterPveNodes(NewPveNodes())
}

func NewPveNodes() service.IPveNodes {
	return &sPveNodes{}
}

// Create 创建节点
func (s *sPveNodes) Create(ctx context.Context, in *input.NodeCreateInp) (out *input.NodeCreateOut, err error) {
	// 验证节点名称唯一性
	count, err := g.DB().Model("hg_pve_nodes").Where("name", in.Name).Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, fmt.Errorf("节点名称已存在")
	}

	// 验证地址唯一性
	count, err = g.DB().Model("hg_pve_nodes").Where("host = ? AND port = ?", in.Host, in.Port).Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, fmt.Errorf("节点地址已存在")
	}

	// 插入数据
	result, err := g.DB().Model("hg_pve_nodes").Data(g.Map{
		"name":         in.Name,
		"host":         in.Host,
		"port":         in.Port,
		"username":     in.Username,
		"password":     in.Password,
		"token_id":     in.TokenID,
		"token_secret": in.TokenSecret,
		"status":       in.Status,
		"created_at":   gtime.Now(),
		"updated_at":   gtime.Now(),
	}).Insert()

	if err != nil {
		return nil, err
	}

	id, _ := result.LastInsertId()
	return &input.NodeCreateOut{
		ID: uint64(id),
	}, nil
}

// Edit 编辑节点
func (s *sPveNodes) Edit(ctx context.Context, in *input.NodeEditInp) (err error) {
	// 检查节点是否存在
	count, err := g.DB().Model("hg_pve_nodes").Where("id", in.ID).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return fmt.Errorf("节点不存在")
	}

	// 验证节点名称唯一性（排除自己）
	count, err = g.DB().Model("hg_pve_nodes").Where("name = ? AND id != ?", in.Name, in.ID).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("节点名称已存在")
	}

	// 验证地址唯一性（排除自己）
	count, err = g.DB().Model("hg_pve_nodes").Where("host = ? AND port = ? AND id != ?", in.Host, in.Port, in.ID).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("节点地址已存在")
	}

	// 更新数据
	_, err = g.DB().Model("hg_pve_nodes").Data(g.Map{
		"name":         in.Name,
		"host":         in.Host,
		"port":         in.Port,
		"username":     in.Username,
		"password":     in.Password,
		"token_id":     in.TokenID,
		"token_secret": in.TokenSecret,
		"status":       in.Status,
		"updated_at":   gtime.Now(),
	}).Where("id", in.ID).Update()

	return err
}

// Delete 删除节点
func (s *sPveNodes) Delete(ctx context.Context, in *input.NodeDeleteInp) (err error) {
	// 检查是否有关联的实例
	count, err := g.DB().Model("hg_pve_instances").Where("node_id", in.ID).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("该节点下还有虚拟机实例，无法删除")
	}

	// 检查是否有关联的模板
	count, err = g.DB().Model("hg_pve_templates").Where("node_id", in.ID).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("该节点下还有系统模板，无法删除")
	}

	// 删除节点
	_, err = g.DB().Model("hg_pve_nodes").Where("id", in.ID).Delete()
	return err
}

// View 查看节点详情
func (s *sPveNodes) View(ctx context.Context, in *input.NodeViewInp) (out *input.NodeViewOut, err error) {
	var node *input.NodeViewOut
	err = g.DB().Model("hg_pve_nodes").Where("id", in.ID).Scan(&node)
	if err != nil {
		return nil, err
	}
	if node == nil {
		return nil, fmt.Errorf("节点不存在")
	}

	return node, nil
}

// List 获取节点列表
func (s *sPveNodes) List(ctx context.Context, in *input.NodeListInp) (out *input.NodeListOut, err error) {
	m := g.DB().Model("hg_pve_nodes n")

	// 条件过滤
	if in.Name != "" {
		m = m.WhereLike("n.name", "%"+in.Name+"%")
	}
	if in.Host != "" {
		m = m.WhereLike("n.host", "%"+in.Host+"%")
	}
	if in.Status > 0 {
		m = m.Where("n.status", in.Status)
	}

	// 排序
	if in.OrderBy != "" {
		orderDir := "ASC"
		if in.OrderDir != "" {
			orderDir = in.OrderDir
		}
		m = m.Order("n." + in.OrderBy + " " + orderDir)
	} else {
		m = m.Order("n.created_at DESC")
	}

	// 分页处理
	totalCount, err := m.Clone().Count()
	if err != nil {
		return out, err
	}

	if &in.PageReq != nil {
		m = m.Page(in.PageReq.GetPage(), in.PageReq.GetPerPage())
	}

	out = &input.NodeListOut{}
	err = m.Scan(&out.List)
	if err != nil {
		return out, err
	}

	if &in.PageReq != nil {
		out.PageRes.Pack(&in.PageReq, totalCount)
	}

	return out, nil
}

// TestConnection 测试节点连接
func (s *sPveNodes) TestConnection(ctx context.Context, in *input.NodeTestInp) (out *input.NodeTestOut, err error) {
	startTime := time.Now()

	// 创建PVE客户端配置
	config := &pveclient.Config{
		Host:        in.Host,
		Port:        in.Port,
		Username:    in.Username,
		Password:    in.Password,
		TokenID:     in.TokenID,
		TokenSecret: in.TokenSecret,
		Insecure:    true, // 测试时跳过SSL验证
		Timeout:     10 * time.Second,
	}

	// 创建客户端
	client, err := pveclient.NewClient(config)
	if err != nil {
		return &input.NodeTestOut{
			Success:     false,
			Message:     fmt.Sprintf("创建客户端失败: %s", err.Error()),
			ConnectTime: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 测试连接 - 获取版本信息
	version, err := client.GetVersion(ctx)
	if err != nil {
		return &input.NodeTestOut{
			Success:     false,
			Message:     fmt.Sprintf("连接测试失败: %s", err.Error()),
			ConnectTime: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 获取节点信息
	nodes, err := client.GetNodes(ctx)
	if err != nil {
		return &input.NodeTestOut{
			Success:     false,
			Message:     fmt.Sprintf("获取节点信息失败: %s", err.Error()),
			ConnectTime: time.Since(startTime).Milliseconds(),
		}, nil
	}

	var nodeName string
	if len(nodes) > 0 {
		nodeName = nodes[0].Node
	}

	return &input.NodeTestOut{
		Success:     true,
		Message:     "连接测试成功",
		PveVersion:  version.Version,
		NodeName:    nodeName,
		ConnectTime: time.Since(startTime).Milliseconds(),
	}, nil
}

// SyncStatus 同步节点状态
func (s *sPveNodes) SyncStatus(ctx context.Context, in *input.NodeSyncInp) (out *input.NodeSyncOut, err error) {
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id", in.ID).Scan(&nodeData)
	if err != nil {
		return nil, err
	}
	if nodeData == nil {
		return &input.NodeSyncOut{
			Success: false,
			Message: "节点不存在",
		}, nil
	}

	// 创建PVE客户端
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	client, err := pveclient.NewClient(config)
	if err != nil {
		// 更新状态为异常
		g.DB().Model("hg_pve_nodes").Data(g.Map{
			"status":     2,
			"updated_at": gtime.Now(),
		}).Where("id", in.ID).Update()

		return &input.NodeSyncOut{
			Success: false,
			Message: fmt.Sprintf("创建客户端失败: %s", err.Error()),
		}, nil
	}

	// 获取节点状态
	nodes, err := client.GetNodes(ctx)
	if err != nil {
		// 更新状态为异常
		g.DB().Model("hg_pve_nodes").Data(g.Map{
			"status":     2,
			"updated_at": gtime.Now(),
		}).Where("id", in.ID).Update()

		return &input.NodeSyncOut{
			Success: false,
			Message: fmt.Sprintf("获取节点状态失败: %s", err.Error()),
		}, nil
	}

	if len(nodes) == 0 {
		return &input.NodeSyncOut{
			Success: false,
			Message: "未找到节点信息",
		}, nil
	}

	node := nodes[0]

	// 获取节点详细状态
	nodeStatus, err := client.GetNodeStatus(ctx, node.Node)
	if err != nil {
		g.Log().Warning(ctx, "获取节点详细状态失败:", err)
	}

	// 更新节点状态信息
	updateData := g.Map{
		"status":       1,
		"cpu_usage":    node.CPU * 100,
		"memory_usage": node.MemUsage * 100,
		"disk_usage":   node.DiskUsage * 100,
		"uptime":       node.Uptime,
		"updated_at":   gtime.Now(),
	}

	if nodeStatus != nil {
		updateData["pve_version"] = nodeStatus.PVEVersion
		updateData["kernel_version"] = nodeStatus.KVersion
	}

	_, err = g.DB().Model("hg_pve_nodes").Data(updateData).Where("id", in.ID).Update()
	if err != nil {
		return &input.NodeSyncOut{
			Success: false,
			Message: fmt.Sprintf("更新节点状态失败: %s", err.Error()),
		}, nil
	}

	return &input.NodeSyncOut{
		Success: true,
		Message: "节点状态同步成功",
	}, nil
}

// GetMonitorData 获取节点监控数据
func (s *sPveNodes) GetMonitorData(ctx context.Context, in *input.NodeMonitorInp) (out *input.NodeMonitorOut, err error) {
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return nil, fmt.Errorf("没有可用的PVE节点")
	}

	// 暂时直接使用模拟数据，因为GetNodeRRDData方法未实现
	return s.getMockMonitorData(in)
}

// getMockMonitorData 生成模拟监控数据（备用方法）
func (s *sPveNodes) getMockMonitorData(in *input.NodeMonitorInp) (*input.NodeMonitorOut, error) {
	now := time.Now()
	var cpuData, memoryData, diskData, networkData []*input.MonitorPoint

	// 根据时间周期生成不同密度的数据点
	var interval time.Duration
	var points int
	switch in.Period {
	case "1h":
		interval = 1 * time.Minute
		points = 60
	case "24h":
		interval = 24 * time.Minute
		points = 60
	case "7d":
		interval = 168 * time.Minute
		points = 60
	case "30d":
		interval = 720 * time.Minute
		points = 60
	default:
		interval = 1 * time.Minute
		points = 60
	}

	for i := points; i >= 0; i-- {
		timestamp := now.Add(-time.Duration(i) * interval).Unix()

		// 生成模拟的监控数据
		cpuData = append(cpuData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(30 + i%40), // CPU使用率 30-70%
		})

		memoryData = append(memoryData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(50 + i%30), // 内存使用率 50-80%
		})

		diskData = append(diskData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(20 + i%20), // 磁盘使用率 20-40%
		})

		networkData = append(networkData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(i % 100 * 1024 * 1024), // 网络流量
		})
	}

	return &input.NodeMonitorOut{
		CPUData:     cpuData,
		MemoryData:  memoryData,
		DiskData:    diskData,
		NetworkData: networkData,
	}, nil
}

// GetServices 获取节点服务列表
func (s *sPveNodes) GetServices(ctx context.Context, in *input.NodeServicesInp) (out *input.NodeServicesOut, err error) {
	// TODO: 实现节点服务列表获取逻辑
	return &input.NodeServicesOut{
		Services: []*input.NodeService{},
	}, nil
}

// ControlService 控制节点服务
func (s *sPveNodes) ControlService(ctx context.Context, in *input.NodeServiceControlInp) (out *input.NodeServiceControlOut, err error) {
	// TODO: 实现节点服务控制逻辑
	return &input.NodeServiceControlOut{
		TaskID: "tmp-service-control",
	}, nil
}

// RebootNode 重启节点 - TODO: 需要实现相关API定义
func (s *sPveNodes) RebootNode(ctx context.Context, in *input.NodeRebootInp) (out *input.NodeRebootOut, err error) {
	// TODO: 实现节点重启逻辑 - 需要先定义API结构
	return &input.NodeRebootOut{
		TaskID: "tmp-reboot-task",
	}, nil
}

// ShutdownNode 关闭节点 - TODO: 需要实现相关API定义
func (s *sPveNodes) ShutdownNode(ctx context.Context, in *input.NodeShutdownInp) (out *input.NodeShutdownOut, err error) {
	// TODO: 实现节点关闭逻辑 - 需要先定义API结构
	return &input.NodeShutdownOut{
		TaskID: "tmp-shutdown-task",
	}, nil
}

// createPVEClient 创建PVE客户端
func (s *sPveNodes) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	// 检查是否有API Token
	tokenID := gconv.String(nodeData["token_id"])
	tokenSecret := gconv.String(nodeData["token_secret"])
	if tokenID != "" && tokenSecret != "" {
		config.TokenID = tokenID
		config.TokenSecret = tokenSecret
	}

	client, err := pveclient.NewClient(config)
	if err != nil {
		return nil, err
	}

	return client, nil
}
