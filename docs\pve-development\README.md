# PVE 开发文档

> Proxmox VE 云平台开发完整指南

## 📖 文档概览

本文档集提供了 PVE (Proxmox VE) 云平台开发的完整指南，包括 API 集成、虚拟机管理、认证权限系统等核心功能的详细实现说明。

**基于官方 API**: https://pve.proxmox.com/pve-docs/api-viewer/

## 📚 文档目录

### 🏗️ [HotGo 框架集成开发](./hotgo-integration.md)
基于 HotGo v2 框架的 PVE 插件开发完整指南：

- **插件架构设计** - 插件化优势和技术架构
- **开发环境搭建** - 从环境配置到项目启动
- **插件目录结构** - 标准的插件文件组织
- **数据库表设计** - 完整的表结构和关系
- **API接口开发** - 控制器和路由实现
- **前端组件开发** - Vue3 + TypeScript 界面
- **配置管理** - 插件配置和部署发布

**适用场景**: 需要在 HotGo 框架基础上开发 PVE 插件的团队

### 🔐 [认证和权限系统](./authentication-permissions.md)
详细介绍 PVE API 的认证机制和权限管理系统：

- **API Token 认证** - 推荐的自动化认证方式
- **票据认证** - 基于用户名/密码的传统认证
- **权限路径结构** - 细粒度的权限控制
- **自定义角色管理** - 应用级权限配置
- **安全最佳实践** - 权限管理指导原则

**适用场景**: 需要集成 PVE 认证或实现权限控制的开发者

### 🌐 [PVE 客户端实现](./client-implementation.md)
完整的 Go 语言 PVE API 客户端实现：

- **客户端架构** - 核心结构和配置
- **HTTP 请求处理** - 请求封装和错误处理
- **数据结构定义** - 完整的 API 响应模型
- **API 方法实现** - 系统信息、虚拟机管理等
- **连接池优化** - 性能优化和重试机制

**适用场景**: 需要在 Go 应用中调用 PVE API 的开发者

### 🔄 [虚拟机生命周期管理](./vm-lifecycle-management.md)
基于 PVE API 的完整虚拟机生命周期管理服务：

- **虚拟机创建** - 从模板创建和参数配置
- **电源管理** - 启动、停止、重启操作
- **快照管理** - 创建、恢复、删除快照
- **虚拟机迁移** - 在线和离线迁移
- **配置调整** - CPU 和内存动态调整
- **状态管理** - 完整的状态跟踪和验证

**适用场景**: 需要实现虚拟机管理功能的云平台开发者

### 📊 [开发进度跟踪](./development-progress.md)
记录 PVE 云平台开发的各个阶段和任务完成情况：

- **项目概览** - 总体进度和阶段规划
- **详细任务进度** - 各模块开发状态
- **质量指标** - 代码质量和性能指标
- **已知问题和风险** - 问题跟踪和解决计划
- **里程碑规划** - 版本发布计划

**适用场景**: 项目管理者和开发团队进度跟踪

## 🚀 快速开始

### 环境要求

- **Go**: 1.19+
- **Proxmox VE**: 7.0+
- **数据库**: MySQL 8.0+ / PostgreSQL 13+
- **框架**: GoFrame v2.0+

### 基础配置

1. **创建 PVE API Token**:
```bash
# 登录 PVE Web 界面
# 数据中心 -> 权限 -> API Token -> 添加
```

2. **配置客户端**:
```go
config := &Config{
    Host:        "pve.example.com",
    Port:        8006,
    TokenID:     "root@pam!myapp",
    TokenSecret: "your-token-secret",
    Insecure:    true, // 测试环境
    Timeout:     30 * time.Second,
}

client, err := NewPVEClient(config)
```

3. **基本操作**:
```go
// 获取版本信息
version, err := client.GetVersion(ctx)

// 获取节点列表
nodes, err := client.GetNodes(ctx)

// 获取虚拟机列表
vms, err := client.GetVMs(ctx, "node1")
```

### 项目结构

```
docs/pve-development/
├── README.md                          # 文档主页
├── authentication-permissions.md      # 认证权限系统
├── client-implementation.md          # PVE 客户端实现
├── vm-lifecycle-management.md        # 虚拟机生命周期管理
└── development-progress.md           # 开发进度跟踪
```

## 🔧 核心特性

### ✨ 完整的 API 集成
- 基于官方 PVE REST API 实现
- 支持所有主要的虚拟机操作
- 完整的错误处理和重试机制
- 异步任务状态跟踪

### 🔐 安全的认证系统  
- 支持 API Token 和票据认证
- 细粒度的权限控制
- 自定义角色和权限管理
- 完整的审计日志记录

### ⚡ 高性能设计
- HTTP 连接池优化
- 权限缓存机制
- 异步操作支持
- 数据库查询优化

### 🛡️ 稳定性保证
- 完整的状态检查和验证
- 操作锁定机制防止冲突
- 详细的日志记录和监控
- 异常恢复和回滚机制

## 📋 开发指南

### 代码规范
- 遵循 Go 官方编码规范
- 使用 gofmt 格式化代码
- 添加详细的注释和文档
- 编写完整的单元测试

### 错误处理
```go
// 统一错误处理模式
func HandleOperation(ctx context.Context) error {
    if err := ValidateInput(); err != nil {
        return gerror.Wrap(err, "输入验证失败")
    }
    
    if err := ExecuteOperation(); err != nil {
        return gerror.Wrap(err, "操作执行失败")
    }
    
    return nil
}
```

### 日志记录
```go
// 记录关键操作
g.Log().Info(ctx, "虚拟机操作", g.Map{
    "operation": "start",
    "vmid":      vmid,
    "node":      nodeName,
    "result":    "success",
})
```

## 🔍 故障排查

### 常见问题

1. **认证失败**
   - 检查 API Token 是否正确
   - 验证用户权限是否足够
   - 确认 PVE 版本兼容性

2. **操作超时**
   - 检查网络连接状态
   - 调整超时时间配置
   - 查看 PVE 服务器负载

3. **状态不同步**
   - 检查数据库连接状态
   - 验证状态更新逻辑
   - 查看同步任务日志

### 调试技巧

```go
// 启用详细日志
g.Log().SetDebug(true)

// 记录 HTTP 请求详情
client.SetDebugMode(true)

// 检查 API 响应
response, _ := client.GetVMStatus(ctx, node, vmid)
g.Log().Debug(ctx, "VM Status", response)
```

## 🤝 贡献指南

### 提交代码
1. Fork 项目仓库
2. 创建特性分支: `git checkout -b feature/your-feature`
3. 提交更改: `git commit -am 'Add some feature'`
4. 推送分支: `git push origin feature/your-feature`
5. 创建 Pull Request

### 报告问题
- 使用 Issue 模板提交问题
- 提供详细的错误信息和日志
- 说明复现步骤和环境信息

### 文档改进
- 修复文档中的错误或不准确信息
- 添加更多使用示例和最佳实践
- 完善 API 参考文档

## 📞 支持和联系

- **项目仓库**: https://github.com/your-org/pve-platform
- **问题反馈**: https://github.com/your-org/pve-platform/issues
- **技术讨论**: https://github.com/your-org/pve-platform/discussions

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 [LICENSE](../LICENSE) 文件。

**最后更新**: 2024-08-28  
**文档版本**: v1.0.0  
**维护团队**: HotGo 开发组