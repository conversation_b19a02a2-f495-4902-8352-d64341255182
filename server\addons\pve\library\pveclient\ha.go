// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

// ========== 高可用性(HA)管理 API ==========

// HAResource HA资源定义
// 高可用集群中的受保护资源，包括虚拟机和LXC容器
// HA系统会监控这些资源的状态，在节点故障时自动迁移到其他健康节点
type HAResource struct {
	SID         string `json:"sid"`         // 服务ID，格式为 vm:vmid 或 ct:ctid
	Type        string `json:"type"`        // 资源类型：vm（虚拟机）或 ct（LXC容器）
	VMID        int    `json:"vmid,omitempty"`        // 虚拟机/容器ID，从SID中提取
	Comment     string `json:"comment,omitempty"`     // 资源备注信息
	Group       string `json:"group,omitempty"`       // 所属HA组名称
	MaxRelocate int    `json:"max_relocate,omitempty"` // 每小时最大重定位次数，防止震荡
	MaxRestart  int    `json:"max_restart,omitempty"`  // 每小时最大重启次数，防止频繁重启
	State       string `json:"state,omitempty"`       // 当前状态：started/stopped/disabled/ignored
	Node        string `json:"node,omitempty"`        // 当前运行的节点名称
	Status      string `json:"status,omitempty"`      // 运行状态：started/stopped/error
}

// HAGroup HA组定义
// HA组定义了节点的优先级顺序和故障切换策略
// 资源会优先在高优先级节点上运行，故障时按优先级顺序迁移
type HAGroup struct {
	Group       string `json:"group"`       // HA组名称，集群内唯一标识
	Nodes       string `json:"nodes"`       // 节点优先级列表，格式：node1:pri1,node2:pri2
	Comment     string `json:"comment,omitempty"`     // 组备注信息
	Nofailback  int    `json:"nofailback,omitempty"`  // 禁用故障回切：0=启用，1=禁用
	Restricted  int    `json:"restricted,omitempty"`  // 限制运行节点：0=不限制，1=只能在组内节点运行
	Type        string `json:"type,omitempty"`        // 组类型，通常为"group"
}

// HAStatus HA集群整体状态
// 反映HA管理器和所有HA资源的运行状态
type HAStatus struct {
	Status      string               `json:"status"`      // 整体状态：active/inactive/error
	Manager     string               `json:"manager,omitempty"`     // 当前HA管理器节点
	Resources   []*HAResourceStatus  `json:"resources,omitempty"`   // HA资源状态列表
	Timestamp   int64                `json:"timestamp,omitempty"`   // 状态更新时间戳
	Quorate     int                  `json:"quorate,omitempty"`     // 集群是否达到法定人数
	ManagerStatus string             `json:"manager_status,omitempty"` // 管理器详细状态
}

// HAResourceStatus HA资源运行状态
// 包含资源的详细运行信息和故障历史
type HAResourceStatus struct {
	SID           string `json:"sid"`           // 服务ID
	Node          string `json:"node"`          // 当前节点
	State         string `json:"state"`         // 期望状态：started/stopped/disabled
	Status        string `json:"status"`        // 实际状态：started/stopped/error/unknown
	CRM_State     string `json:"crm_state,omitempty"`     // 集群资源管理器状态
	Request_State string `json:"request_state,omitempty"` // 请求状态
	Timestamp     int64  `json:"timestamp,omitempty"`     // 状态更新时间
	ErrorCount    int    `json:"error_count,omitempty"`   // 错误计数
	LastError     string `json:"last_error,omitempty"`    // 最后错误信息
}

// HAManagerStatus HA管理器状态
// 显示HA管理器的详细运行信息
type HAManagerStatus struct {
	Status        string                 `json:"status"`        // 管理器状态：active/inactive
	Lock          string                 `json:"lock,omitempty"`          // 管理器锁状态
	Manager       string                 `json:"manager,omitempty"`       // 当前管理器节点
	Timestamp     int64                  `json:"timestamp,omitempty"`     // 状态时间戳
	Resources     map[string]interface{} `json:"resources,omitempty"`     // 资源详细信息
	ServiceStates map[string]interface{} `json:"service_states,omitempty"` // 服务状态详情
}

// HAResourceManageRequest HA资源管理请求
// 用于启动、停止、迁移HA资源的操作请求
type HAResourceManageRequest struct {
	Command string `json:"command"`         // 操作命令：start/stop/relocate/disable/enable
	Node    string `json:"node,omitempty"` // 目标节点（仅relocate时使用）
}

// GetHAResources 获取HA资源列表
// 功能说明：
// - 获取集群中所有已配置的HA资源信息
// - 包括虚拟机和LXC容器的HA配置
// - 显示资源的当前状态、所属组、重启策略等信息
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   []*HAResource: HA资源配置列表
//   error: 获取失败时返回错误信息
// 使用示例：
//   resources, err := client.GetHAResources(ctx)
//   for _, resource := range resources {
//       fmt.Printf("资源 %s 状态: %s, 节点: %s\n", resource.SID, resource.State, resource.Node)
//   }
func (c *Client) GetHAResources(ctx context.Context) ([]*HAResource, error) {
	body, err := c.request(ctx, "GET", "/cluster/ha/resources", nil)
	if err != nil {
		return nil, fmt.Errorf("获取HA资源列表失败: %w", err)
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析HA资源响应失败: %w", err)
	}

	var resources []*HAResource
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &resources); err != nil {
		return nil, fmt.Errorf("解析HA资源数据失败: %w", err)
	}

	// 从SID中提取VMID和资源类型
	for _, resource := range resources {
		if resource.SID != "" {
			parts := strings.Split(resource.SID, ":")
			if len(parts) == 2 {
				resource.Type = parts[0]
				if vmid := parseInt(parts[1]); vmid > 0 {
					resource.VMID = vmid
				}
			}
		}
	}

	return resources, nil
}

// GetHAResource 获取单个HA资源详情
// 功能说明：
// - 获取指定HA资源的详细配置信息
// - 包含资源的完整配置参数和当前状态
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID，格式为 vm:vmid 或 ct:ctid
// 返回：
//   *HAResource: HA资源详细信息
//   error: 获取失败时返回错误信息
func (c *Client) GetHAResource(ctx context.Context, sid string) (*HAResource, error) {
	if sid == "" {
		return nil, fmt.Errorf("服务ID不能为空")
	}

	path := fmt.Sprintf("/cluster/ha/resources/%s", sid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取HA资源详情失败: %w", err)
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析HA资源响应失败: %w", err)
	}

	var resource HAResource
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &resource); err != nil {
		return nil, fmt.Errorf("解析HA资源数据失败: %w", err)
	}

	// 从SID中提取类型和VMID
	if resource.SID != "" {
		parts := strings.Split(resource.SID, ":")
		if len(parts) == 2 {
			resource.Type = parts[0]
			if vmid := parseInt(parts[1]); vmid > 0 {
				resource.VMID = vmid
			}
		}
	}

	return &resource, nil
}

// CreateHAResource 创建HA资源
// 功能说明：
// - 将虚拟机或LXC容器添加到HA管理
// - 配置故障切换策略和重启限制
// - 可以指定HA组和节点优先级
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   resource: HA资源配置信息
// 返回：
//   error: 创建失败时返回错误信息
// 注意事项：
//   - 资源必须已经存在于集群中
//   - SID格式必须正确：vm:vmid 或 ct:ctid
//   - 如果指定了组，该组必须已经创建
func (c *Client) CreateHAResource(ctx context.Context, resource *HAResource) error {
	if resource.SID == "" {
		return fmt.Errorf("服务ID(SID)不能为空")
	}

	// 验证SID格式
	parts := strings.Split(resource.SID, ":")
	if len(parts) != 2 || (parts[0] != "vm" && parts[0] != "ct") {
		return fmt.Errorf("SID格式错误，应为 vm:vmid 或 ct:ctid")
	}

	params := map[string]interface{}{
		"sid": resource.SID,
	}

	if resource.Comment != "" {
		params["comment"] = resource.Comment
	}
	if resource.Group != "" {
		params["group"] = resource.Group
	}
	if resource.MaxRelocate > 0 {
		params["max_relocate"] = resource.MaxRelocate
	}
	if resource.MaxRestart > 0 {
		params["max_restart"] = resource.MaxRestart
	}
	if resource.State != "" {
		params["state"] = resource.State
	}

	_, err := c.request(ctx, "POST", "/cluster/ha/resources", params)
	if err != nil {
		return fmt.Errorf("创建HA资源失败: %w", err)
	}

	return nil
}

// UpdateHAResource 更新HA资源配置
// 功能说明：
// - 修改现有HA资源的配置参数
// - 可以更改组分配、重启限制、备注信息等
// - 配置更改后立即生效
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID，标识要更新的资源
//   resource: 新的资源配置信息
// 返回：
//   error: 更新失败时返回错误信息
func (c *Client) UpdateHAResource(ctx context.Context, sid string, resource *HAResource) error {
	if sid == "" {
		return fmt.Errorf("服务ID不能为空")
	}

	path := fmt.Sprintf("/cluster/ha/resources/%s", sid)
	params := map[string]interface{}{}

	if resource.Comment != "" {
		params["comment"] = resource.Comment
	}
	if resource.Group != "" {
		params["group"] = resource.Group
	}
	if resource.MaxRelocate > 0 {
		params["max_relocate"] = resource.MaxRelocate
	}
	if resource.MaxRestart > 0 {
		params["max_restart"] = resource.MaxRestart
	}
	if resource.State != "" {
		params["state"] = resource.State
	}

	_, err := c.request(ctx, "PUT", path, params)
	if err != nil {
		return fmt.Errorf("更新HA资源失败: %w", err)
	}

	return nil
}

// DeleteHAResource 删除HA资源
// 功能说明：
// - 将资源从HA管理中移除
// - 资源本身不会被删除，只是不再受HA保护
// - 删除后资源的故障切换将停止工作
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 要删除的服务ID
// 返回：
//   error: 删除失败时返回错误信息
// 注意事项：
//   - 删除HA资源前建议先停止资源
//   - 删除操作不可逆，请谨慎操作
func (c *Client) DeleteHAResource(ctx context.Context, sid string) error {
	if sid == "" {
		return fmt.Errorf("服务ID不能为空")
	}

	path := fmt.Sprintf("/cluster/ha/resources/%s", sid)
	_, err := c.request(ctx, "DELETE", path, nil)
	if err != nil {
		return fmt.Errorf("删除HA资源失败: %w", err)
	}

	return nil
}

// GetHAGroups 获取HA组列表
// 功能说明：
// - 获取集群中配置的所有HA组信息
// - HA组定义了节点优先级和故障切换策略
// - 组内资源会优先在高优先级节点上运行
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   []*HAGroup: HA组配置列表
//   error: 获取失败时返回错误信息
func (c *Client) GetHAGroups(ctx context.Context) ([]*HAGroup, error) {
	body, err := c.request(ctx, "GET", "/cluster/ha/groups", nil)
	if err != nil {
		return nil, fmt.Errorf("获取HA组列表失败: %w", err)
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析HA组响应失败: %w", err)
	}

	var groups []*HAGroup
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &groups); err != nil {
		return nil, fmt.Errorf("解析HA组数据失败: %w", err)
	}

	return groups, nil
}

// GetHAGroup 获取单个HA组详情
// 功能说明：
// - 获取指定HA组的详细配置信息
// - 包含节点优先级列表和策略设置
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   groupName: HA组名称
// 返回：
//   *HAGroup: HA组详细信息
//   error: 获取失败时返回错误信息
func (c *Client) GetHAGroup(ctx context.Context, groupName string) (*HAGroup, error) {
	if groupName == "" {
		return nil, fmt.Errorf("HA组名称不能为空")
	}

	path := fmt.Sprintf("/cluster/ha/groups/%s", groupName)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取HA组详情失败: %w", err)
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析HA组响应失败: %w", err)
	}

	var group HAGroup
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &group); err != nil {
		return nil, fmt.Errorf("解析HA组数据失败: %w", err)
	}

	return &group, nil
}

// CreateHAGroup 创建HA组
// 功能说明：
// - 创建新的HA组，定义节点优先级
// - 设置故障回切策略和运行限制
// - 组内资源将按照节点优先级进行调度
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   group: HA组配置信息
// 返回：
//   error: 创建失败时返回错误信息
// 节点格式说明：
//   - 格式：node1:priority1,node2:priority2
//   - 优先级数字越小，优先级越高
//   - 例如：pve1:100,pve2:200,pve3:300
func (c *Client) CreateHAGroup(ctx context.Context, group *HAGroup) error {
	if group.Group == "" {
		return fmt.Errorf("HA组名称不能为空")
	}
	if group.Nodes == "" {
		return fmt.Errorf("节点列表不能为空")
	}

	// 验证节点格式
	if err := c.validateNodesFormat(group.Nodes); err != nil {
		return fmt.Errorf("节点格式错误: %w", err)
	}

	params := map[string]interface{}{
		"group": group.Group,
		"nodes": group.Nodes,
	}

	if group.Comment != "" {
		params["comment"] = group.Comment
	}
	if group.Nofailback > 0 {
		params["nofailback"] = group.Nofailback
	}
	if group.Restricted > 0 {
		params["restricted"] = group.Restricted
	}

	_, err := c.request(ctx, "POST", "/cluster/ha/groups", params)
	if err != nil {
		return fmt.Errorf("创建HA组失败: %w", err)
	}

	return nil
}

// UpdateHAGroup 更新HA组配置
// 功能说明：
// - 修改现有HA组的节点优先级和策略
// - 可以添加或删除节点，调整优先级
// - 配置更改会影响组内所有资源的调度
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   groupName: 要更新的HA组名称
//   group: 新的组配置信息
// 返回：
//   error: 更新失败时返回错误信息
func (c *Client) UpdateHAGroup(ctx context.Context, groupName string, group *HAGroup) error {
	if groupName == "" {
		return fmt.Errorf("HA组名称不能为空")
	}

	path := fmt.Sprintf("/cluster/ha/groups/%s", groupName)
	params := map[string]interface{}{}

	if group.Nodes != "" {
		if err := c.validateNodesFormat(group.Nodes); err != nil {
			return fmt.Errorf("节点格式错误: %w", err)
		}
		params["nodes"] = group.Nodes
	}
	if group.Comment != "" {
		params["comment"] = group.Comment
	}
	if group.Nofailback >= 0 {
		params["nofailback"] = group.Nofailback
	}
	if group.Restricted >= 0 {
		params["restricted"] = group.Restricted
	}

	_, err := c.request(ctx, "PUT", path, params)
	if err != nil {
		return fmt.Errorf("更新HA组失败: %w", err)
	}

	return nil
}

// DeleteHAGroup 删除HA组
// 功能说明：
// - 删除指定的HA组
// - 删除前需要确保没有资源使用该组
// - 组内资源需要先移除或分配到其他组
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   groupName: 要删除的HA组名称
// 返回：
//   error: 删除失败时返回错误信息
// 注意事项：
//   - 删除前请确保组内无资源
//   - 删除操作不可逆，请谨慎操作
func (c *Client) DeleteHAGroup(ctx context.Context, groupName string) error {
	if groupName == "" {
		return fmt.Errorf("HA组名称不能为空")
	}

	path := fmt.Sprintf("/cluster/ha/groups/%s", groupName)
	_, err := c.request(ctx, "DELETE", path, nil)
	if err != nil {
		return fmt.Errorf("删除HA组失败: %w", err)
	}

	return nil
}

// GetHAStatus 获取HA集群整体状态
// 功能说明：
// - 获取HA系统的整体运行状态
// - 显示HA管理器状态和资源运行情况
// - 包含集群仲裁状态和错误信息
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   *HAStatus: HA集群状态信息
//   error: 获取失败时返回错误信息
func (c *Client) GetHAStatus(ctx context.Context) (*HAStatus, error) {
	body, err := c.request(ctx, "GET", "/cluster/ha/status/current", nil)
	if err != nil {
		return nil, fmt.Errorf("获取HA状态失败: %w", err)
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析HA状态响应失败: %w", err)
	}

	var status HAStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, fmt.Errorf("解析HA状态数据失败: %w", err)
	}

	return &status, nil
}

// GetHAManagerStatus 获取HA管理器状态
// 功能说明：
// - 获取HA管理器的详细运行状态
// - 显示管理器锁状态和资源管理信息
// - 用于诊断HA系统运行问题
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   *HAManagerStatus: HA管理器状态详情
//   error: 获取失败时返回错误信息
func (c *Client) GetHAManagerStatus(ctx context.Context) (*HAManagerStatus, error) {
	body, err := c.request(ctx, "GET", "/cluster/ha/status/manager_status", nil)
	if err != nil {
		return nil, fmt.Errorf("获取HA管理器状态失败: %w", err)
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析HA管理器状态响应失败: %w", err)
	}

	var status HAManagerStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, fmt.Errorf("解析HA管理器状态数据失败: %w", err)
	}

	return &status, nil
}

// ManageHAResource 管理HA资源状态
// 功能说明：
// - 对HA资源执行启动、停止、迁移等操作
// - 支持的操作：start（启动）、stop（停止）、relocate（迁移）、disable（禁用）、enable（启用）
// - 迁移操作可以指定目标节点
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID，标识要操作的资源
//   request: 管理操作请求
// 返回：
//   error: 操作失败时返回错误信息
// 支持的命令：
//   - start: 启动资源
//   - stop: 停止资源  
//   - relocate: 迁移资源到指定节点
//   - disable: 禁用HA保护
//   - enable: 启用HA保护
func (c *Client) ManageHAResource(ctx context.Context, sid string, request *HAResourceManageRequest) error {
	if sid == "" {
		return fmt.Errorf("服务ID不能为空")
	}
	if request.Command == "" {
		return fmt.Errorf("操作命令不能为空")
	}

	// 验证命令类型
	validCommands := []string{"start", "stop", "relocate", "disable", "enable"}
	isValidCommand := false
	for _, cmd := range validCommands {
		if request.Command == cmd {
			isValidCommand = true
			break
		}
	}
	if !isValidCommand {
		return fmt.Errorf("不支持的操作命令: %s, 支持的命令: %v", request.Command, validCommands)
	}

	// relocate命令需要指定目标节点
	if request.Command == "relocate" && request.Node == "" {
		return fmt.Errorf("迁移操作需要指定目标节点")
	}

	path := fmt.Sprintf("/cluster/ha/resources/%s", sid)
	params := map[string]interface{}{
		"state": request.Command,
	}

	if request.Node != "" {
		params["node"] = request.Node
	}

	_, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return fmt.Errorf("管理HA资源失败: %w", err)
	}

	return nil
}

// StartHAResource 启动HA资源
// 功能说明：
// - 启动指定的HA资源
// - 资源将在合适的节点上启动
// - 这是ManageHAResource的便捷方法
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID
// 返回：
//   error: 启动失败时返回错误信息
func (c *Client) StartHAResource(ctx context.Context, sid string) error {
	return c.ManageHAResource(ctx, sid, &HAResourceManageRequest{
		Command: "start",
	})
}

// StopHAResource 停止HA资源
// 功能说明：
// - 停止指定的HA资源
// - 资源将在当前节点上停止
// - 停止后资源仍受HA保护，可以重新启动
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID
// 返回：
//   error: 停止失败时返回错误信息
func (c *Client) StopHAResource(ctx context.Context, sid string) error {
	return c.ManageHAResource(ctx, sid, &HAResourceManageRequest{
		Command: "stop",
	})
}

// RelocateHAResource 迁移HA资源
// 功能说明：
// - 将HA资源迁移到指定节点
// - 资源会先在当前节点停止，然后在目标节点启动
// - 迁移过程中服务会短暂中断
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID
//   targetNode: 目标节点名称
// 返回：
//   error: 迁移失败时返回错误信息
func (c *Client) RelocateHAResource(ctx context.Context, sid, targetNode string) error {
	return c.ManageHAResource(ctx, sid, &HAResourceManageRequest{
		Command: "relocate",
		Node:    targetNode,
	})
}

// DisableHAResource 禁用HA资源保护
// 功能说明：
// - 禁用指定资源的HA保护
// - 资源将继续运行，但不再受HA监控
// - 节点故障时不会自动迁移
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID
// 返回：
//   error: 禁用失败时返回错误信息
func (c *Client) DisableHAResource(ctx context.Context, sid string) error {
	return c.ManageHAResource(ctx, sid, &HAResourceManageRequest{
		Command: "disable",
	})
}

// EnableHAResource 启用HA资源保护
// 功能说明：
// - 重新启用指定资源的HA保护
// - 资源将重新受HA系统监控
// - 支持自动故障切换和迁移
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   sid: 服务ID
// 返回：
//   error: 启用失败时返回错误信息
func (c *Client) EnableHAResource(ctx context.Context, sid string) error {
	return c.ManageHAResource(ctx, sid, &HAResourceManageRequest{
		Command: "enable",
	})
}

// validateNodesFormat 验证节点格式
// 内部辅助函数，验证HA组节点配置格式是否正确
// 参数：
//   nodes: 节点配置字符串，格式：node1:pri1,node2:pri2
// 返回：
//   error: 格式错误时返回错误信息
func (c *Client) validateNodesFormat(nodes string) error {
	if nodes == "" {
		return fmt.Errorf("节点列表不能为空")
	}

	// 分割节点配置
	nodeConfigs := strings.Split(nodes, ",")
	for _, config := range nodeConfigs {
		config = strings.TrimSpace(config)
		if config == "" {
			continue
		}

		// 验证节点:优先级格式
		parts := strings.Split(config, ":")
		if len(parts) != 2 {
			return fmt.Errorf("节点配置格式错误: %s, 应为 node:priority", config)
		}

		nodeName := strings.TrimSpace(parts[0])
		priority := strings.TrimSpace(parts[1])

		if nodeName == "" {
			return fmt.Errorf("节点名称不能为空: %s", config)
		}

		if priority == "" {
			return fmt.Errorf("节点优先级不能为空: %s", config)
		}

		// 验证优先级是否为数字
		if parseInt(priority) <= 0 {
			return fmt.Errorf("节点优先级必须为正整数: %s", config)
		}
	}

	return nil
}

// parseInt 解析整数
// 内部辅助函数，安全地将字符串转换为整数
func parseInt(s string) int {
	var result int
	for _, c := range s {
		if c >= '0' && c <= '9' {
			result = result*10 + int(c-'0')
		} else {
			return 0
		}
	}
	return result
}