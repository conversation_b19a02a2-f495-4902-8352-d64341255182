// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

// DashboardStatsInp 仪表盘统计输入
type DashboardStatsInp struct{}

type DashboardStatsOut struct {
	TotalNodes       int         `json:"totalNodes" dc:"节点总数"`
	RunningNodes     int         `json:"runningNodes" dc:"运行中节点数"`
	TotalInstances   int         `json:"totalInstances" dc:"实例总数"`
	RunningInstances int         `json:"runningInstances" dc:"运行中实例数"`
	TotalTemplates   int         `json:"totalTemplates" dc:"模板总数"`
	EnabledTemplates int         `json:"enabledTemplates" dc:"已启用模板数"`
	TotalUsers       int         `json:"totalUsers" dc:"用户总数"`
	RecentActivities []*Activity `json:"recentActivities" dc:"最近活动"`
}

// Activity 活动记录
type Activity struct {
	ID        int    `json:"id" dc:"活动ID"`
	Type      string `json:"type" dc:"活动类型：node,instance,template"`
	Action    string `json:"action" dc:"操作动作"`
	Target    string `json:"target" dc:"操作目标"`
	User      string `json:"user" dc:"操作用户"`
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	Status    string `json:"status" dc:"状态：success,failed,pending"`
}

// DashboardResourceInp 资源使用情况输入
type DashboardResourceInp struct{}

type DashboardResourceOut struct {
	List []*ResourceUsage `json:"list" dc:"资源使用列表"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	NodeID        uint64  `json:"nodeId" dc:"节点ID"`
	NodeName      string  `json:"nodeName" dc:"节点名称"`
	CPUUsage      float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage   float64 `json:"memoryUsage" dc:"内存使用率"`
	DiskUsage     float64 `json:"diskUsage" dc:"磁盘使用率"`
	InstanceCount int     `json:"instanceCount" dc:"实例数量"`
}

// DashboardAlertsInp 系统告警输入
type DashboardAlertsInp struct {
	Resolved *bool `json:"resolved" dc:"是否已解决，不传获取全部"`
}

type DashboardAlertsOut struct {
	List []*SystemAlert `json:"list" dc:"告警列表"`
}

// SystemAlert 系统告警
type SystemAlert struct {
	ID        int    `json:"id" dc:"告警ID"`
	Type      string `json:"type" dc:"告警类型：warning,error,info"`
	Title     string `json:"title" dc:"告警标题"`
	Message   string `json:"message" dc:"告警消息"`
	Source    string `json:"source" dc:"告警来源"`
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	Resolved  bool   `json:"resolved" dc:"是否已解决"`
}

// DashboardResolveAlertInp 解决告警输入
type DashboardResolveAlertInp struct {
	ID int `json:"id" dc:"告警ID"`
}