// Package global
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package global

import (
	"context"
	"hotgo/internal/library/addons"
	"hotgo/utility/tree"

	"github.com/gogf/gf/v2/frame/g"
)

var (
	Skeleton *addons.Skeleton
)

// GetSkeleton 获取模块骨架
func GetSkeleton() *addons.Skeleton {
	if Skeleton == nil {
		panic("PVE addon skeleton not initialized.")
	}
	return Skeleton
}

// Init 初始化
func Init(ctx context.Context, skeleton *addons.Skeleton) {
	Skeleton = skeleton
}

// Install 安装插件
func Install(ctx context.Context) error {
	g.Log().Info(ctx, "正在安装PVE插件...")

	// 执行数据库迁移
	if err := installTables(ctx); err != nil {
		return err
	}

	// 初始化默认数据
	if err := initDefaultData(ctx); err != nil {
		return err
	}

	g.Log().Info(ctx, "PVE插件安装完成")
	return nil
}

// Upgrade 升级插件
func Upgrade(ctx context.Context) error {
	g.Log().Info(ctx, "正在升级PVE插件...")
	// 升级逻辑
	g.Log().Info(ctx, "PVE插件升级完成")
	return nil
}

// UnInstall 卸载插件
func UnInstall(ctx context.Context) error {
	g.Log().Info(ctx, "正在卸载PVE插件...")

	// 删除菜单
	if err := removeMenus(ctx); err != nil {
		g.Log().Errorf(ctx, "删除PVE插件菜单失败: %v", err)
		return err
	}

	// 清理数据表
	if err := uninstallTables(ctx); err != nil {
		return err
	}

	g.Log().Info(ctx, "PVE插件卸载完成")
	return nil
}

// installTables 安装数据表
func installTables(ctx context.Context) error {
	// PVE节点表
	nodeTableSQL := `
CREATE TABLE IF NOT EXISTS hg_pve_nodes (
  id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  name varchar(100) NOT NULL COMMENT '节点名称',
  host varchar(255) NOT NULL COMMENT '节点地址',
  port int NOT NULL DEFAULT 8006 COMMENT '端口',
  username varchar(100) DEFAULT NULL COMMENT '用户名',
  password varchar(255) DEFAULT NULL COMMENT '密码',
  token_id varchar(100) DEFAULT NULL COMMENT 'API Token ID',
  token_secret varchar(255) DEFAULT NULL COMMENT 'API Token Secret',
  status tinyint NOT NULL DEFAULT 1 COMMENT '状态：1=正常 2=异常',
  cpu_usage decimal(5,2) DEFAULT 0.00 COMMENT 'CPU使用率',
  memory_usage decimal(5,2) DEFAULT 0.00 COMMENT '内存使用率',
  disk_usage decimal(5,2) DEFAULT 0.00 COMMENT '磁盘使用率',
  uptime bigint DEFAULT 0 COMMENT '运行时间',
  pve_version varchar(50) DEFAULT NULL COMMENT 'PVE版本',
  kernel_version varchar(100) DEFAULT NULL COMMENT '内核版本',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_name (name),
  KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PVE节点管理表';`

	// PVE虚拟机实例表
	instanceTableSQL := `
CREATE TABLE IF NOT EXISTS hg_pve_instances (
  id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  vmid int NOT NULL COMMENT '虚拟机ID',
  node_id bigint unsigned NOT NULL COMMENT '节点ID',
  user_id bigint unsigned NOT NULL COMMENT '用户ID',
  name varchar(100) NOT NULL COMMENT '实例名称',
  description text COMMENT '描述',
  os_template varchar(100) DEFAULT NULL COMMENT '操作系统模板',
  cpu_cores int NOT NULL DEFAULT 1 COMMENT 'CPU核心数',
  memory_mb int NOT NULL DEFAULT 512 COMMENT '内存大小(MB)',
  disk_gb int NOT NULL DEFAULT 10 COMMENT '磁盘大小(GB)',
  ip_address varchar(45) DEFAULT NULL COMMENT 'IP地址',
  mac_address varchar(17) DEFAULT NULL COMMENT 'MAC地址',
  status varchar(20) NOT NULL DEFAULT 'stopped' COMMENT '状态',
  cpu_usage decimal(5,2) DEFAULT 0.00 COMMENT 'CPU使用率',
  memory_usage decimal(5,2) DEFAULT 0.00 COMMENT '内存使用率',
  network_in bigint DEFAULT 0 COMMENT '网络入流量',
  network_out bigint DEFAULT 0 COMMENT '网络出流量',
  disk_read bigint DEFAULT 0 COMMENT '磁盘读取',
  disk_write bigint DEFAULT 0 COMMENT '磁盘写入',
  expired_at timestamp NULL COMMENT '到期时间',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_vmid_node (vmid, node_id),
  KEY idx_user_id (user_id),
  KEY idx_status (status),
  KEY idx_expired (expired_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PVE虚拟机实例表';`

	// PVE模板表
	templateTableSQL := `
CREATE TABLE IF NOT EXISTS hg_pve_templates (
  id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  name varchar(100) NOT NULL COMMENT '模板名称',
  description text COMMENT '描述',
  os_type varchar(50) NOT NULL COMMENT '操作系统类型',
  os_version varchar(50) DEFAULT NULL COMMENT '系统版本',
  template_id int DEFAULT NULL COMMENT 'PVE模板ID',
  node_id bigint unsigned NOT NULL COMMENT '节点ID',
  cpu_cores int NOT NULL DEFAULT 1 COMMENT '默认CPU核心数',
  memory_mb int NOT NULL DEFAULT 512 COMMENT '默认内存大小(MB)',
  disk_gb int NOT NULL DEFAULT 10 COMMENT '默认磁盘大小(GB)',
  price decimal(10,2) DEFAULT 0.00 COMMENT '价格',
  status tinyint NOT NULL DEFAULT 1 COMMENT '状态：1=启用 2=禁用',
  sort int NOT NULL DEFAULT 100 COMMENT '排序',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_node_id (node_id),
  KEY idx_status (status),
  KEY idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PVE系统模板表';`

	// 执行SQL
	tables := []string{nodeTableSQL, instanceTableSQL, templateTableSQL}
	for _, sql := range tables {
		if _, err := g.DB().Exec(ctx, sql); err != nil {
			return err
		}
	}

	return nil
}

// uninstallTables 卸载数据表
func uninstallTables(ctx context.Context) error {
	tables := []string{
		"DROP TABLE IF EXISTS hg_pve_instances",
		"DROP TABLE IF EXISTS hg_pve_templates",
		"DROP TABLE IF EXISTS hg_pve_nodes",
	}

	for _, sql := range tables {
		if _, err := g.DB().Exec(ctx, sql); err != nil {
			return err
		}
	}

	return nil
}

// initDefaultData 初始化默认数据
func initDefaultData(ctx context.Context) error {
	// 创建菜单权限
	return createMenus(ctx)
}

// createMenus 创建菜单
func createMenus(ctx context.Context) error {
	// 检查主菜单是否已存在
	count, err := g.DB().Model("hg_admin_menu").Where("name", "pve").Count()
	if err != nil {
		return err
	}
	if count > 0 {
		g.Log().Info(ctx, "PVE插件菜单已存在，跳过创建")
		return nil
	}

	// 主菜单
	mainMenu := g.Map{
		"pid":       0,
		"level":     1,
		"tree":      "",
		"title":     "PVE云平台",
		"name":      "pve",
		"path":      "/pve",
		"icon":      "ant-design:cloud-server-outlined",
		"component": "ParentLayout",
		"sort":      90,
		"status":    1,
		"type":      1,
	}

	result, err := g.DB().Model("hg_admin_menu").Data(mainMenu).Insert()
	if err != nil {
		return err
	}

	mainMenuId, _ := result.LastInsertId()
	mainTree := tree.GenLabel("", mainMenuId)

	// 更新主菜单的tree字段
	_, err = g.DB().Model("hg_admin_menu").Where("id", mainMenuId).Data(g.Map{"tree": mainTree}).Update()
	if err != nil {
		return err
	}

	// 子菜单
	subMenus := []g.Map{
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "仪表盘",
			"name":      "pve_dashboard",
			"path":      "/dashboard",
			"icon":      "ant-design:dashboard-outlined",
			"component": "/addons/pve/dashboard/index",
			"sort":      1,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "节点管理",
			"name":      "pve_nodes",
			"path":      "/nodes",
			"icon":      "ant-design:cluster-outlined",
			"component": "/addons/pve/nodes/index",
			"sort":      2,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "实例管理",
			"name":      "pve_instances",
			"path":      "/instances",
			"icon":      "ant-design:desktop-outlined",
			"component": "/addons/pve/instances/index",
			"sort":      3,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "容器管理",
			"name":      "pve_containers",
			"path":      "/containers",
			"icon":      "ant-design:container-outlined",
			"component": "/addons/pve/containers/index",
			"sort":      4,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "系统模板",
			"name":      "pve_templates",
			"path":      "/templates",
			"icon":      "ant-design:file-image-outlined",
			"component": "/addons/pve/templates/index",
			"sort":      5,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "存储管理",
			"name":      "pve_storage",
			"path":      "/storage",
			"icon":      "ant-design:hdd-outlined",
			"component": "/addons/pve/storage/index",
			"sort":      6,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "备份管理",
			"name":      "pve_backup",
			"path":      "/backup",
			"icon":      "ant-design:save-outlined",
			"component": "/addons/pve/backup/index",
			"sort":      7,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "网络管理",
			"name":      "pve_network",
			"path":      "/network",
			"icon":      "ant-design:global-outlined",
			"component": "/addons/pve/network/index",
			"sort":      8,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "防火墙管理",
			"name":      "pve_firewall",
			"path":      "/firewall",
			"icon":      "ant-design:safety-outlined",
			"component": "/addons/pve/firewall/index",
			"sort":      9,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "高可用管理",
			"name":      "pve_ha",
			"path":      "/ha",
			"icon":      "ant-design:cluster-outlined",
			"component": "/addons/pve/ha/index",
			"sort":      10,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "集群管理",
			"name":      "pve_cluster",
			"path":      "/cluster",
			"icon":      "ant-design:cluster-outlined",
			"component": "/addons/pve/cluster/index",
			"sort":      11,
			"status":    1,
			"type":      1,
		},
		{
			"pid":       mainMenuId,
			"level":     2,
			"tree":      "", // 稍后设置
			"title":     "用户权限管理",
			"name":      "pve_pveusers",
			"path":      "/pveusers",
			"icon":      "ant-design:user-outlined",
			"component": "/addons/pve/pveusers/index",
			"sort":      12,
			"status":    1,
			"type":      1,
		},
	}

	for _, menu := range subMenus {
		// 检查子菜单是否已存在
		count, err := g.DB().Model("hg_admin_menu").Where("name", menu["name"]).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			g.Log().Infof(ctx, "菜单 %s 已存在，跳过创建", menu["name"])
			continue
		}

		// 插入子菜单
		result, err := g.DB().Model("hg_admin_menu").Data(menu).Insert()
		if err != nil {
			return err
		}

		// 生成并更新子菜单的tree字段
		subMenuId, _ := result.LastInsertId()
		subTree := tree.GenLabel(mainTree, subMenuId)
		_, err = g.DB().Model("hg_admin_menu").Where("id", subMenuId).Data(g.Map{"tree": subTree}).Update()
		if err != nil {
			return err
		}
	}

	return nil
}

// removeMenus 删除菜单
func removeMenus(ctx context.Context) error {
	// 删除所有PVE相关的菜单（包括子菜单）
	// 通过主菜单的name为"pve"来删除整个菜单树
	count, err := g.DB().Model("hg_admin_menu").Where("name", "pve").Count()
	if err != nil {
		g.Log().Errorf(ctx, "查询PVE菜单数量失败: %v", err)
		return err
	}
	if count == 0 {
		g.Log().Info(ctx, "PVE插件菜单不存在，跳过删除")
		return nil
	}

	// 获取主菜单ID
	var mainMenuId int64
	value, err := g.DB().Model("hg_admin_menu").Fields("id").Where("name", "pve").Value()
	if err != nil {
		g.Log().Errorf(ctx, "获取PVE主菜单ID失败: %v", err)
		return err
	}
	mainMenuId = value.Int64()

	// 先删除所有子菜单（通过pid关联）
	subResult, err := g.DB().Model("hg_admin_menu").Where("pid", mainMenuId).Delete()
	if err != nil {
		g.Log().Errorf(ctx, "删除PVE子菜单失败: %v", err)
		return err
	}
	subRows, _ := subResult.RowsAffected()
	g.Log().Infof(ctx, "删除了 %d 个子菜单", subRows)

	// 删除主菜单
	mainResult, err := g.DB().Model("hg_admin_menu").Where("id", mainMenuId).Delete()
	if err != nil {
		g.Log().Errorf(ctx, "删除PVE主菜单失败: %v", err)
		return err
	}
	mainRows, _ := mainResult.RowsAffected()
	g.Log().Infof(ctx, "删除了 %d 个主菜单", mainRows)

	g.Log().Info(ctx, "PVE插件菜单删除成功")
	return nil
}
