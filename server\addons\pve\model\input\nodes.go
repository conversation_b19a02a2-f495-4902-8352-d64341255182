// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// NodeCreateInp 创建节点输入
type NodeCreateInp struct {
	Name        string `json:"name" dc:"节点名称"`
	Host        string `json:"host" dc:"节点地址"`
	Port        int    `json:"port" dc:"端口"`
	Username    string `json:"username" dc:"用户名"`
	Password    string `json:"password" dc:"密码"`
	TokenID     string `json:"tokenId" dc:"API Token ID"`
	TokenSecret string `json:"tokenSecret" dc:"API Token Secret"`
	Status      int    `json:"status" dc:"状态：1=正常 2=异常"`
}

type NodeCreateOut struct {
	ID uint64 `json:"id" dc:"节点ID"`
}

// NodeEditInp 编辑节点输入
type NodeEditInp struct {
	ID          uint64 `json:"id" dc:"节点ID"`
	Name        string `json:"name" dc:"节点名称"`
	Host        string `json:"host" dc:"节点地址"`
	Port        int    `json:"port" dc:"端口"`
	Username    string `json:"username" dc:"用户名"`
	Password    string `json:"password" dc:"密码"`
	TokenID     string `json:"tokenId" dc:"API Token ID"`
	TokenSecret string `json:"tokenSecret" dc:"API Token Secret"`
	Status      int    `json:"status" dc:"状态：1=正常 2=异常"`
}

// NodeDeleteInp 删除节点输入
type NodeDeleteInp struct {
	ID uint64 `json:"id" dc:"节点ID"`
}

// NodeViewInp 查看节点输入
type NodeViewInp struct {
	ID uint64 `json:"id" dc:"节点ID"`
}

type NodeViewOut struct {
	ID            uint64  `json:"id" dc:"节点ID"`
	Name          string  `json:"name" dc:"节点名称"`
	Host          string  `json:"host" dc:"节点地址"`
	Port          int     `json:"port" dc:"端口"`
	Username      string  `json:"username" dc:"用户名"`
	Password      string  `json:"password" dc:"密码"`
	TokenID       string  `json:"tokenId" dc:"API Token ID"`
	TokenSecret   string  `json:"tokenSecret" dc:"API Token Secret"`
	Status        int     `json:"status" dc:"状态：1=正常 2=异常"`
	CpuUsage      float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage   float64 `json:"memoryUsage" dc:"内存使用率"`
	DiskUsage     float64 `json:"diskUsage" dc:"磁盘使用率"`
	Uptime        int64   `json:"uptime" dc:"运行时间"`
	PveVersion    string  `json:"pveVersion" dc:"PVE版本"`
	KernelVersion string  `json:"kernelVersion" dc:"内核版本"`
	CreatedAt     string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string  `json:"updatedAt" dc:"更新时间"`
}

// NodeListInp 节点列表输入
type NodeListInp struct {
	form.PageReq
	Name     string `json:"name" dc:"节点名称"`
	Host     string `json:"host" dc:"节点地址"`
	Status   int    `json:"status" dc:"状态"`
	OrderBy  string `json:"orderBy" dc:"排序字段"`
	OrderDir string `json:"orderDir" dc:"排序方向"`
}

type NodeListOut struct {
	form.PageRes
	List []*NodeListModel `json:"list" dc:"节点列表"`
}

type NodeListModel struct {
	ID            uint64  `json:"id" dc:"节点ID"`
	Name          string  `json:"name" dc:"节点名称"`
	Host          string  `json:"host" dc:"节点地址"`
	Port          int     `json:"port" dc:"端口"`
	Status        int     `json:"status" dc:"状态：1=正常 2=异常"`
	CpuUsage      float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage   float64 `json:"memoryUsage" dc:"内存使用率"`
	DiskUsage     float64 `json:"diskUsage" dc:"磁盘使用率"`
	Uptime        int64   `json:"uptime" dc:"运行时间"`
	PveVersion    string  `json:"pveVersion" dc:"PVE版本"`
	KernelVersion string  `json:"kernelVersion" dc:"内核版本"`
	CreatedAt     string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string  `json:"updatedAt" dc:"更新时间"`
}

// NodeTestInp 测试节点连接输入
type NodeTestInp struct {
	Host        string `json:"host" dc:"节点地址"`
	Port        int    `json:"port" dc:"端口"`
	Username    string `json:"username" dc:"用户名"`
	Password    string `json:"password" dc:"密码"`
	TokenID     string `json:"tokenId" dc:"API Token ID"`
	TokenSecret string `json:"tokenSecret" dc:"API Token Secret"`
}

type NodeTestOut struct {
	Success     bool   `json:"success" dc:"连接是否成功"`
	Message     string `json:"message" dc:"测试结果信息"`
	PveVersion  string `json:"pveVersion" dc:"PVE版本"`
	NodeName    string `json:"nodeName" dc:"节点名称"`
	ConnectTime int64  `json:"connectTime" dc:"连接耗时(毫秒)"`
}

// NodeSyncInp 同步节点状态输入
type NodeSyncInp struct {
	ID uint64 `json:"id" dc:"节点ID"`
}

type NodeSyncOut struct {
	Success bool   `json:"success" dc:"同步是否成功"`
	Message string `json:"message" dc:"同步结果信息"`
}

// NodeMonitorInp 节点监控数据输入
type NodeMonitorInp struct {
	ID     uint64 `json:"id" dc:"节点ID"`
	Period string `json:"period" dc:"时间周期：1h,24h,7d,30d"`
}

type NodeMonitorOut struct {
	CPUData     []*MonitorPoint `json:"cpuData" dc:"CPU使用率数据"`
	MemoryData  []*MonitorPoint `json:"memoryData" dc:"内存使用率数据"`
	DiskData    []*MonitorPoint `json:"diskData" dc:"磁盘使用率数据"`
	NetworkData []*MonitorPoint `json:"networkData" dc:"网络流量数据"`
}

type MonitorPoint struct {
	Timestamp int64   `json:"timestamp" dc:"时间戳"`
	Value     float64 `json:"value" dc:"数值"`
}

// NodeServicesInp 获取节点服务输入
type NodeServicesInp struct {
	NodeID int `json:"nodeId" v:"required#请指定节点ID"`
}

// NodeService 节点服务
type NodeService struct {
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description"`
	Enabled     bool   `json:"enabled"`
}

// NodeServicesOut 获取节点服务输出
type NodeServicesOut struct {
	Services []*NodeService `json:"services"`
}

// NodeServiceControlInp 控制节点服务输入
type NodeServiceControlInp struct {
	NodeID  int    `json:"nodeId" v:"required#请指定节点ID"`
	Service string `json:"service" v:"required#请指定服务名称"`
	Action  string `json:"action" v:"required#请指定操作类型"`
}

// NodeServiceControlOut 控制节点服务输出
type NodeServiceControlOut struct {
	TaskID string `json:"taskId"`
}

// NodeRebootInp 重启节点输入
type NodeRebootInp struct {
	NodeID int `json:"nodeId" v:"required#请指定节点ID"`
}

// NodeRebootOut 重启节点输出
type NodeRebootOut struct {
	TaskID string `json:"taskId"`
}

// NodeShutdownInp 关闭节点输入
type NodeShutdownInp struct {
	NodeID int `json:"nodeId" v:"required#请指定节点ID"`
}

// NodeShutdownOut 关闭节点输出
type NodeShutdownOut struct {
	TaskID string `json:"taskId"`
}