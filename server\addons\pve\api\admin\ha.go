// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/internal/model/input/form"
)

// HAResourcesReq 获取高可用资源请求
type HAResourcesReq struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// HAResourcesRes 获取高可用资源响应
type HAResourcesRes struct {
	List []*HAResourceViewModel `json:"list" dc:"高可用资源列表"`
	form.PageRes
}

// HAResourceViewModel 高可用资源视图模型
type HAResourceViewModel struct {
	ID          uint64 `json:"id" dc:"资源ID"`
	NodeID      int    `json:"nodeId" dc:"节点ID"`
	NodeName    string `json:"nodeName" dc:"节点名称"`
	Name        string `json:"name" dc:"资源名称"`
	Type        string `json:"type" dc:"资源类型"`
	Status      string `json:"status" dc:"资源状态"`
	Group       string `json:"group" dc:"所属组"`
	Priority    int    `json:"priority" dc:"优先级"`
	Restart     int    `json:"restart" dc:"重启次数"`
	MaxRestarts int    `json:"maxRestarts" dc:"最大重启次数"`
	CreatedAt   string `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string `json:"updatedAt" dc:"更新时间"`
}

// HACreateResourceReq 创建高可用资源请求
type HACreateResourceReq struct {
	NodeID      int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Name        string `json:"name" v:"required#资源名称不能为空" dc:"资源名称"`
	Type        string `json:"type" v:"required#资源类型不能为空" dc:"资源类型"`
	Group       string `json:"group" dc:"所属组"`
	Priority    int    `json:"priority" dc:"优先级"`
	MaxRestarts int    `json:"maxRestarts" dc:"最大重启次数"`
}

// HACreateResourceRes 创建高可用资源响应
type HACreateResourceRes struct {
	ID uint64 `json:"id" dc:"资源ID"`
}

// HAEditResourceReq 编辑高可用资源请求
type HAEditResourceReq struct {
	ID          uint64 `json:"id" v:"required#资源ID不能为空" dc:"资源ID"`
	Name        string `json:"name" dc:"资源名称"`
	Type        string `json:"type" dc:"资源类型"`
	Group       string `json:"group" dc:"所属组"`
	Priority    int    `json:"priority" dc:"优先级"`
	MaxRestarts int    `json:"maxRestarts" dc:"最大重启次数"`
}

// HAEditResourceRes 编辑高可用资源响应
type HAEditResourceRes struct{}

// HADeleteResourceReq 删除高可用资源请求
type HADeleteResourceReq struct {
	ID uint64 `json:"id" v:"required#资源ID不能为空" dc:"资源ID"`
}

// HADeleteResourceRes 删除高可用资源响应
type HADeleteResourceRes struct{}

// HAGroupsReq 获取高可用组请求
type HAGroupsReq struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// HAGroupsRes 获取高可用组响应
type HAGroupsRes struct {
	List []*HAGroupViewModel `json:"list" dc:"高可用组列表"`
	form.PageRes
}

// HAGroupViewModel 高可用组视图模型
type HAGroupViewModel struct {
	ID        uint64 `json:"id" dc:"组ID"`
	NodeID    int    `json:"nodeId" dc:"节点ID"`
	NodeName  string `json:"nodeName" dc:"节点名称"`
	Name      string `json:"name" dc:"组名称"`
	Status    string `json:"status" dc:"组状态"`
	Resources int    `json:"resources" dc:"资源数量"`
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	UpdatedAt string `json:"updatedAt" dc:"更新时间"`
}

// HACreateGroupReq 创建高可用组请求
type HACreateGroupReq struct {
	NodeID int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Name   string `json:"name" v:"required#组名称不能为空" dc:"组名称"`
}

// HACreateGroupRes 创建高可用组响应
type HACreateGroupRes struct {
	ID uint64 `json:"id" dc:"组ID"`
}

// HAEditGroupReq 编辑高可用组请求
type HAEditGroupReq struct {
	ID   uint64 `json:"id" v:"required#组ID不能为空" dc:"组ID"`
	Name string `json:"name" dc:"组名称"`
}

// HAEditGroupRes 编辑高可用组响应
type HAEditGroupRes struct{}

// HADeleteGroupReq 删除高可用组请求
type HADeleteGroupReq struct {
	ID uint64 `json:"id" v:"required#组ID不能为空" dc:"组ID"`
}

// HADeleteGroupRes 删除高可用组响应
type HADeleteGroupRes struct{}
