// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/api/admin"
	"hotgo/addons/pve/model/input"
)

var Dashboard = cDashboard{}

type cDashboard struct{}

// GetStats 获取仪表盘统计数据
func (c *cDashboard) GetStats(ctx context.Context, req *admin.DashboardGetStatsReq) (res *admin.DashboardGetStatsRes, err error) {
	// 统计节点数据
	totalNodes, err := g.DB().Model("hg_pve_nodes").Count()
	if err != nil {
		return nil, err
	}
	runningNodes, err := g.DB().Model("hg_pve_nodes").Where("status", 1).Count()
	if err != nil {
		return nil, err
	}

	// 统计实例数据
	totalInstances, err := g.DB().Model("hg_pve_instances").Count()
	if err != nil {
		return nil, err
	}
	runningInstances, err := g.DB().Model("hg_pve_instances").Where("status", "running").Count()
	if err != nil {
		return nil, err
	}

	// 统计模板数据
	totalTemplates, err := g.DB().Model("hg_pve_templates").Count()
	if err != nil {
		return nil, err
	}
	enabledTemplates, err := g.DB().Model("hg_pve_templates").Where("status", 1).Count()
	if err != nil {
		return nil, err
	}

	// 统计用户数据
	totalUsers, err := g.DB().Model("admin_member").Count()
	if err != nil {
		return nil, err
	}

	// 获取最近活动（模拟数据）
	var activities []*input.Activity
	activities = append(activities, &input.Activity{
		ID:        1,
		Type:      "instance",
		Action:    "创建实例",
		Target:    "test-vm-001",
		User:      "admin",
		CreatedAt: time.Now().Add(-10 * time.Minute).Format("2006-01-02 15:04:05"),
		Status:    "success",
	})
	activities = append(activities, &input.Activity{
		ID:        2,
		Type:      "node",
		Action:    "同步节点状态",
		Target:    "pve-node-01",
		User:      "system",
		CreatedAt: time.Now().Add(-30 * time.Minute).Format("2006-01-02 15:04:05"),
		Status:    "success",
	})

	res = &admin.DashboardGetStatsRes{
		DashboardStatsOut: &input.DashboardStatsOut{
			TotalNodes:       int(totalNodes),
			RunningNodes:     int(runningNodes),
			TotalInstances:   int(totalInstances),
			RunningInstances: int(runningInstances),
			TotalTemplates:   int(totalTemplates),
			EnabledTemplates: int(enabledTemplates),
			TotalUsers:       int(totalUsers),
			RecentActivities: activities,
		},
	}

	return
}

// GetResourceUsage 获取资源使用情况
func (c *cDashboard) GetResourceUsage(ctx context.Context, req *admin.DashboardGetResourceUsageReq) (res *admin.DashboardGetResourceUsageRes, err error) {
	var resources []*input.ResourceUsage

	// 从节点表获取数据
	nodes, err := g.DB().Model("hg_pve_nodes n").
		LeftJoin("hg_pve_instances i", "n.id = i.node_id AND i.status = 'running'").
		Fields("n.id, n.name, n.cpu_usage, n.memory_usage, n.disk_usage, COUNT(i.id) as instance_count").
		Where("n.status = ?", 1).
		Group("n.id").
		All()

	if err != nil {
		return nil, err
	}

	for _, node := range nodes {
		resources = append(resources, &input.ResourceUsage{
			NodeID:        gconv.Uint64(node["id"]),
			NodeName:      gconv.String(node["name"]),
			CPUUsage:      gconv.Float64(node["cpu_usage"]),
			MemoryUsage:   gconv.Float64(node["memory_usage"]),
			DiskUsage:     gconv.Float64(node["disk_usage"]),
			InstanceCount: gconv.Int(node["instance_count"]),
		})
	}

	res = &admin.DashboardGetResourceUsageRes{
		DashboardResourceOut: &input.DashboardResourceOut{
			List: resources,
		},
	}

	return
}

// GetSystemAlerts 获取系统告警
func (c *cDashboard) GetSystemAlerts(ctx context.Context, req *admin.DashboardGetSystemAlertsReq) (res *admin.DashboardGetSystemAlertsRes, err error) {
	var alerts []*input.SystemAlert

	// 模拟告警数据（实际应该从告警表获取）
	alerts = append(alerts, &input.SystemAlert{
		ID:        1,
		Type:      "warning",
		Title:     "节点CPU使用率过高",
		Message:   "pve-node-01 CPU使用率达到85%",
		Source:    "node monitor",
		CreatedAt: time.Now().Add(-1 * time.Hour).Format("2006-01-02 15:04:05"),
		Resolved:  false,
	})

	alerts = append(alerts, &input.SystemAlert{
		ID:        2,
		Type:      "error",
		Title:     "实例创建失败",
		Message:   "创建实例 vm-002 时发生错误",
		Source:    "instance manager",
		CreatedAt: time.Now().Add(-2 * time.Hour).Format("2006-01-02 15:04:05"),
		Resolved:  false,
	})

	res = &admin.DashboardGetSystemAlertsRes{
		DashboardAlertsOut: &input.DashboardAlertsOut{
			List: alerts,
		},
	}

	return
}

// ResolveAlert 解决告警
func (c *cDashboard) ResolveAlert(ctx context.Context, req *admin.DashboardResolveAlertReq) (res *admin.DashboardResolveAlertRes, err error) {
	// TODO: 实现告警解决逻辑
	// 这里应该更新告警表中的resolved字段
	g.Log().Info(ctx, "告警已解决:", g.Map{"alertId": req.ID})
	res = &admin.DashboardResolveAlertRes{}
	return
}