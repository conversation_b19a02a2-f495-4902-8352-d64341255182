// Package core
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"fmt"
	"time"

	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sPveNetwork struct{}

func init() {
	service.RegisterPveNetwork(NewPveNetwork())
}

func NewPveNetwork() service.INetwork {
	return &sPveNetwork{}
}

// Create 创建网络
func (s *sPveNetwork) Create(ctx context.Context, in *input.NetworkCreateInp) (out *input.NetworkCreateOut, err error) {
	// 验证网络名称是否已存在
	var existingNetwork g.Map
	err = g.DB().Model("hg_pve_networks").Where("name = ?", in.Name).Scan(&existingNetwork)
	if err != nil {
		return nil, fmt.Errorf("检查网络名称失败: %v", err)
	}
	if existingNetwork != nil {
		return nil, fmt.Errorf("网络名称 '%s' 已存在", in.Name)
	}

	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id = ?", in.NodeID).Scan(&nodeData)
	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return nil, fmt.Errorf("节点ID %d 不存在", in.NodeID)
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	nodeName := gconv.String(nodeData["name"])

	// 构建网络配置参数
	config := map[string]interface{}{
		"type":    in.Type,
		"bridge":  in.Bridge,
		"vlan":    in.VLAN,
		"gateway": in.Gateway,
		"netmask": in.Netmask,
	}

	// 调用PVE API创建网络
	err = client.CreateNetwork(ctx, nodeName, in.Name, config)
	if err != nil {
		return nil, fmt.Errorf("在PVE中创建网络失败: %v", err)
	}

	// 保存到数据库
	_, err = g.DB().Model("hg_pve_networks").Data(g.Map{
		"node_id":    in.NodeID,
		"name":       in.Name,
		"type":       in.Type,
		"bridge":     in.Bridge,
		"vlan":       in.VLAN,
		"gateway":    in.Gateway,
		"netmask":    in.Netmask,
		"status":     1,
		"created_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}).Insert()

	if err != nil {
		// 如果数据库插入失败，尝试删除已创建的PVE网络
		_ = client.DeleteNetwork(ctx, nodeName, in.Name)
		return nil, fmt.Errorf("保存网络配置到数据库失败: %v", err)
	}

	g.Log().Infof(ctx, "成功创建网络: %s (节点: %s)", in.Name, nodeName)

	return &input.NetworkCreateOut{
		NetworkCreateModel: input.NetworkCreateModel{
			ID: in.Name,
		},
	}, nil
}

// Edit 编辑网络
func (s *sPveNetwork) Edit(ctx context.Context, in *input.NetworkEditInp) (err error) {
	// 检查网络是否存在
	var networkData g.Map
	err = g.DB().Model("hg_pve_networks").Where("name = ?", in.ID).Scan(&networkData)
	if err != nil {
		return fmt.Errorf("查询网络信息失败: %v", err)
	}
	if networkData == nil {
		return fmt.Errorf("网络 '%s' 不存在", in.ID)
	}

	// 获取节点信息
	nodeID := gconv.Int(networkData["node_id"])
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id = ?", nodeID).Scan(&nodeData)
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return fmt.Errorf("关联节点不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	nodeName := gconv.String(nodeData["name"])

	// 构建更新参数
	updateData := g.Map{
		"updated_at": gtime.Now(),
	}

	config := make(map[string]interface{})

	if in.Name != "" {
		updateData["name"] = in.Name
	}
	if in.Type != "" {
		updateData["type"] = in.Type
		config["type"] = in.Type
	}
	if in.Bridge != "" {
		updateData["bridge"] = in.Bridge
		config["bridge"] = in.Bridge
	}
	if in.VLAN > 0 {
		updateData["vlan"] = in.VLAN
		config["vlan"] = in.VLAN
	}
	if in.Gateway != "" {
		updateData["gateway"] = in.Gateway
		config["gateway"] = in.Gateway
	}
	if in.Netmask != "" {
		updateData["netmask"] = in.Netmask
		config["netmask"] = in.Netmask
	}

	// 更新PVE网络配置
	err = client.UpdateNetwork(ctx, nodeName, in.ID, config)
	if err != nil {
		return fmt.Errorf("更新PVE网络配置失败: %v", err)
	}

	// 更新数据库记录
	_, err = g.DB().Model("hg_pve_networks").Data(updateData).Where("name = ?", in.ID).Update()
	if err != nil {
		return fmt.Errorf("更新网络数据库记录失败: %v", err)
	}

	g.Log().Infof(ctx, "成功编辑网络: %s", in.ID)
	return nil
}

// Delete 删除网络
func (s *sPveNetwork) Delete(ctx context.Context, in *input.NetworkDeleteInp) (err error) {
	// 检查网络是否存在
	var networkData g.Map
	err = g.DB().Model("hg_pve_networks").Where("name = ?", in.ID).Scan(&networkData)
	if err != nil {
		return fmt.Errorf("查询网络信息失败: %v", err)
	}
	if networkData == nil {
		return fmt.Errorf("网络 '%s' 不存在", in.ID)
	}

	// 检查是否有实例正在使用此网络
	var instanceCount int
	instanceCount, err = g.DB().Model("hg_pve_instances").Where("network = ?", in.ID).Count()
	if err != nil {
		return fmt.Errorf("检查网络使用情况失败: %v", err)
	}
	if instanceCount > 0 {
		return fmt.Errorf("网络 '%s' 正被 %d 个实例使用，无法删除", in.ID, instanceCount)
	}

	// 获取节点信息
	nodeID := gconv.Int(networkData["node_id"])
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id = ?", nodeID).Scan(&nodeData)
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return fmt.Errorf("关联节点不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	nodeName := gconv.String(nodeData["name"])

	// 删除PVE网络
	err = client.DeleteNetwork(ctx, nodeName, in.ID)
	if err != nil {
		return fmt.Errorf("删除PVE网络失败: %v", err)
	}

	// 删除数据库记录
	_, err = g.DB().Model("hg_pve_networks").Where("name = ?", in.ID).Delete()
	if err != nil {
		return fmt.Errorf("删除网络数据库记录失败: %v", err)
	}

	g.Log().Infof(ctx, "成功删除网络: %s", in.ID)
	return nil
}

// View 查看网络详情
func (s *sPveNetwork) View(ctx context.Context, in *input.NetworkViewInp) (out *input.NetworkViewOut, err error) {
	var network *input.NetworkModel
	err = g.DB().Model("hg_pve_networks").Where("name = ?", in.ID).Scan(&network)
	if err != nil {
		return nil, fmt.Errorf("查询网络详情失败: %v", err)
	}
	if network == nil {
		return nil, fmt.Errorf("网络 '%s' 不存在", in.ID)
	}

	return &input.NetworkViewOut{
		NetworkModel: *network,
	}, nil
}

// List 获取网络列表
func (s *sPveNetwork) List(ctx context.Context, in *input.NetworkListInp) (out *input.NetworkListModel, err error) {
	m := g.DB().Model("hg_pve_networks n").
		LeftJoin("hg_pve_nodes nd", "n.node_id = nd.id").
		Fields("n.*, nd.name as node_name")

	// 条件过滤
	if in.NodeID > 0 {
		m = m.Where("n.node_id = ?", in.NodeID)
	}
	if in.Type != "" {
		m = m.Where("n.type", in.Type)
	}
	if in.Keyword != "" {
		m = m.Where(g.Map{
			"n.name like ? OR n.bridge like ?": []interface{}{
				"%" + in.Keyword + "%", "%" + in.Keyword + "%",
			},
		})
	}

	// 排序
	m = m.Order("n.created_at DESC")

	var networks []*input.NetworkModel
	err = m.Scan(&networks)
	if err != nil {
		return nil, fmt.Errorf("查询网络列表失败: %v", err)
	}

	return &input.NetworkListModel{
		List: networks,
	}, nil
}

// createPVEClient 创建PVE客户端
func (s *sPveNetwork) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	config := &pveclient.Config{
		Host:     gconv.String(nodeData["host"]),
		Port:     gconv.Int(nodeData["port"]),
		Username: gconv.String(nodeData["username"]),
		Password: gconv.String(nodeData["password"]),
		Insecure: true, // 测试环境
		Timeout:  30 * time.Second,
	}

	// 检查是否有API Token
	tokenID := gconv.String(nodeData["token_id"])
	tokenSecret := gconv.String(nodeData["token_secret"])
	if tokenID != "" && tokenSecret != "" {
		config.TokenID = tokenID
		config.TokenSecret = tokenSecret
	}

	client, err := pveclient.NewClient(config)
	if err != nil {
		return nil, err
	}

	return client, nil
}
