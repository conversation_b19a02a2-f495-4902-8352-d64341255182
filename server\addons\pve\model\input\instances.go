// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// InstanceCreateInp 创建实例输入
type InstanceCreateInp struct {
	NodeID      uint64 `json:"nodeId" dc:"PVE节点ID"`
	UserID      uint64 `json:"userId" dc:"用户ID"`
	Name        string `json:"name" dc:"实例名称"`
	Description string `json:"description" dc:"实例描述"`
	TemplateID  uint64 `json:"templateId" dc:"系统模板ID"`
	CpuCores    int    `json:"cpuCores" dc:"CPU核心数"`
	MemoryMb    int    `json:"memoryMb" dc:"内存大小(MB)"`
	DiskGb      int    `json:"diskGb" dc:"磁盘大小(GB)"`
	Period      int    `json:"period" dc:"购买时长"`
	PeriodType  string `json:"periodType" dc:"计费周期"`
	AutoRenew   bool   `json:"autoRenew" dc:"是否自动续费"`
}

type InstanceCreateOut struct {
	InstanceID uint64 `json:"instanceId" dc:"实例ID"`
	VMID       int    `json:"vmid" dc:"PVE虚拟机ID"`
	TaskID     string `json:"taskId" dc:"创建任务ID"`
}

// InstanceEditInp 编辑实例输入
type InstanceEditInp struct {
	ID          uint64 `json:"id" dc:"实例ID"`
	Name        string `json:"name" dc:"实例名称"`
	Description string `json:"description" dc:"实例描述"`
	CpuCores    int    `json:"cpuCores" dc:"CPU核心数"`
	MemoryMb    int    `json:"memoryMb" dc:"内存大小(MB)"`
	DiskGb      int    `json:"diskGb" dc:"磁盘大小(GB)"`
}

// InstanceDeleteInp 删除实例输入
type InstanceDeleteInp struct {
	ID    uint64 `json:"id" dc:"实例ID"`
	Force bool   `json:"force" dc:"是否强制删除"`
}

type InstanceDeleteOut struct {
	TaskID string `json:"taskId" dc:"删除任务ID"`
}

// InstanceViewInp 查看实例输入
type InstanceViewInp struct {
	ID uint64 `json:"id" dc:"实例ID"`
}

type InstanceViewOut struct {
	ID           uint64  `json:"id" dc:"实例ID"`
	VMID         int     `json:"vmid" dc:"PVE虚拟机ID"`
	NodeID       uint64  `json:"nodeId" dc:"节点ID"`
	NodeName     string  `json:"nodeName" dc:"节点名称"`
	UserID       uint64  `json:"userId" dc:"用户ID"`
	Username     string  `json:"username" dc:"用户名"`
	Name         string  `json:"name" dc:"实例名称"`
	Description  string  `json:"description" dc:"实例描述"`
	OsTemplate   string  `json:"osTemplate" dc:"操作系统模板"`
	CpuCores     int     `json:"cpuCores" dc:"CPU核心数"`
	MemoryMb     int     `json:"memoryMb" dc:"内存大小(MB)"`
	DiskGb       int     `json:"diskGb" dc:"磁盘大小(GB)"`
	IpAddress    string  `json:"ipAddress" dc:"IP地址"`
	MacAddress   string  `json:"macAddress" dc:"MAC地址"`
	Status       string  `json:"status" dc:"实例状态"`
	CpuUsage     float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage  float64 `json:"memoryUsage" dc:"内存使用率"`
	NetworkIn    int64   `json:"networkIn" dc:"网络入流量"`
	NetworkOut   int64   `json:"networkOut" dc:"网络出流量"`
	DiskRead     int64   `json:"diskRead" dc:"磁盘读取"`
	DiskWrite    int64   `json:"diskWrite" dc:"磁盘写入"`
	ExpiredAt    string  `json:"expiredAt" dc:"到期时间"`
	CreatedAt    string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt    string  `json:"updatedAt" dc:"更新时间"`
}

// InstanceListInp 实例列表输入
type InstanceListInp struct {
	form.PageReq
	UserID   uint64 `json:"userId" dc:"用户ID"`
	NodeID   uint64 `json:"nodeId" dc:"节点ID"`
	Status   string `json:"status" dc:"实例状态"`
	Keyword  string `json:"keyword" dc:"搜索关键词"`
	OrderBy  string `json:"orderBy" dc:"排序字段"`
	OrderDir string `json:"orderDir" dc:"排序方向"`
}

type InstanceListOut struct {
	form.PageRes
	List []*InstanceListModel `json:"list" dc:"实例列表"`
}

type InstanceListModel struct {
	ID          uint64  `json:"id" dc:"实例ID"`
	VMID        int     `json:"vmid" dc:"PVE虚拟机ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	UserID      uint64  `json:"userId" dc:"用户ID"`
	Username    string  `json:"username" dc:"用户名"`
	Name        string  `json:"name" dc:"实例名称"`
	Description string  `json:"description" dc:"实例描述"`
	OsTemplate  string  `json:"osTemplate" dc:"操作系统模板"`
	CpuCores    int     `json:"cpuCores" dc:"CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"磁盘大小(GB)"`
	Status      string  `json:"status" dc:"实例状态"`
	IpAddress   string  `json:"ipAddress" dc:"IP地址"`
	CpuUsage    float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage float64 `json:"memoryUsage" dc:"内存使用率"`
	ExpiredAt   string  `json:"expiredAt" dc:"到期时间"`
	CreatedAt   string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string  `json:"updatedAt" dc:"更新时间"`
}

// InstanceActionInp 实例操作输入
type InstanceActionInp struct {
	ID     uint64 `json:"id" dc:"实例ID"`
	Action string `json:"action" dc:"操作类型"`
	Force  bool   `json:"force" dc:"是否强制执行"`
}

type InstanceActionOut struct {
	TaskID string `json:"taskId" dc:"操作任务ID"`
	Status string `json:"status" dc:"执行状态"`
}

// InstanceRenewInp 续费实例输入
type InstanceRenewInp struct {
	ID         uint64 `json:"id" dc:"实例ID"`
	Period     int    `json:"period" dc:"续费时长"`
	PeriodType string `json:"periodType" dc:"计费周期"`
}

type InstanceRenewOut struct {
	OrderID   uint64 `json:"orderId" dc:"订单ID"`
	ExpiredAt string `json:"expiredAt" dc:"新的到期时间"`
}

// InstanceConsoleInp 获取控制台访问信息输入
type InstanceConsoleInp struct {
	ID uint64 `json:"id" dc:"实例ID"`
}

type InstanceConsoleOut struct {
	ConsoleURL string `json:"consoleUrl" dc:"控制台访问URL"`
	VncPort    int    `json:"vncPort" dc:"VNC端口"`
	VncTicket  string `json:"vncTicket" dc:"VNC访问票据"`
}

// InstanceSnapshotInp 创建快照输入
type InstanceSnapshotInp struct {
	ID          uint64 `json:"id" dc:"实例ID"`
	SnapName    string `json:"snapName" dc:"快照名称"`
	Description string `json:"description" dc:"快照描述"`
}

type InstanceSnapshotOut struct {
	TaskID string `json:"taskId" dc:"快照任务ID"`
}

// InstanceSnapshotListInp 快照列表输入
type InstanceSnapshotListInp struct {
	ID uint64 `json:"id" dc:"实例ID"`
}

type InstanceSnapshotListOut struct {
	List []*SnapshotModel `json:"list" dc:"快照列表"`
}

type SnapshotModel struct {
	Name        string `json:"name" dc:"快照名称"`
	Description string `json:"description" dc:"快照描述"`
	SnapTime    int64  `json:"snapTime" dc:"创建时间"`
	Running     bool   `json:"running" dc:"创建时是否运行中"`
	Size        int64  `json:"size" dc:"快照大小"`
}

// InstanceMonitorInp 实例监控数据输入
type InstanceMonitorInp struct {
	ID     uint64 `json:"id" dc:"实例ID"`
	Period string `json:"period" dc:"时间周期：1h,24h,7d,30d"`
}

type InstanceMonitorOut struct {
	CPUData     []*MonitorPoint `json:"cpuData" dc:"CPU使用率数据"`
	MemoryData  []*MonitorPoint `json:"memoryData" dc:"内存使用率数据"`
	NetworkData []*MonitorPoint `json:"networkData" dc:"网络流量数据"`
	DiskIOData  []*MonitorPoint `json:"diskIOData" dc:"磁盘IO数据"`
}

// InstanceTaskInp 获取实例任务状态输入
type InstanceTaskInp struct {
	TaskID string `json:"taskId" dc:"任务ID"`
}

type InstanceTaskOut struct {
	TaskID     string `json:"taskId" dc:"任务ID"`
	Status     string `json:"status" dc:"任务状态"`
	ExitStatus string `json:"exitStatus" dc:"退出状态"`
	StartTime  int64  `json:"startTime" dc:"开始时间"`
	EndTime    int64  `json:"endTime" dc:"结束时间"`
	Log        string `json:"log" dc:"任务日志"`
}

// InstanceCloneInp 克隆实例输入
type InstanceCloneInp struct {
	ID          int    `json:"id" v:"required#请指定实例ID"`
	NewVMID     int    `json:"newVmid"`
	Name        string `json:"name" v:"required#请输入新实例名称"`
	Description string `json:"description"`
	Target      string `json:"target"`
	Storage     string `json:"storage"`
	Full        bool   `json:"full"`
}

// InstanceCloneOut 克隆实例输出
type InstanceCloneOut struct {
	TaskID string `json:"taskId"`
}

// InstanceMigrateInp 迁移实例输入
type InstanceMigrateInp struct {
	ID             int  `json:"id" v:"required#请指定实例ID"`
	Target         string `json:"target" v:"required#请指定目标节点"`
	Online         bool `json:"online"`
	WithLocalDisks bool `json:"withLocalDisks"`
}

// InstanceMigrateOut 迁移实例输出
type InstanceMigrateOut struct {
	TaskID string `json:"taskId"`
}

// InstanceRRDInp 获取RRD监控数据输入
type InstanceRRDInp struct {
	ID         int    `json:"id" v:"required#请指定实例ID"`
	DataSource string `json:"dataSource"`
	TimeFrame  string `json:"timeFrame"`
}

// RRDPoint RRD数据点
type RRDPoint struct {
	Timestamp int64   `json:"timestamp"`
	Value     float64 `json:"value"`
}

// InstanceRRDOut 获取RRD监控数据输出
type InstanceRRDOut struct {
	Data []*RRDPoint `json:"data"`
}

// InstanceOperationInp 实例操作输入（用于队列处理）
type InstanceOperationInp struct {
	ID uint64 `json:"id" dc:"实例ID"`
}

type InstanceOperationOut struct {
	TaskID string `json:"taskId" dc:"操作任务ID"`
}