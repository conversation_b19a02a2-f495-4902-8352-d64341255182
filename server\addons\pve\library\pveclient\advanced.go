// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== 高可用管理 API ==========
// HA相关方法已移至ha.go文件，避免重复定义

// ========== 复制管理 API ==========

// GetReplicationJobs 获取复制任务列表
func (c *Client) GetReplicationJobs(ctx context.Context) ([]*ReplicationJob, error) {
	body, err := c.request(ctx, "GET", "/cluster/replication", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var jobs []*ReplicationJob
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &jobs); err != nil {
		return nil, err
	}

	return jobs, nil
}

// CreateReplicationJob 创建复制任务
func (c *Client) CreateReplicationJob(ctx context.Context, job *ReplicationJob) error {
	params := map[string]interface{}{
		"id":      job.ID,
		"target":  job.Target,
		"type":    job.Type,
		"enabled": job.Enabled,
	}
	
	if job.Comment != "" {
		params["comment"] = job.Comment
	}
	if job.Rate > 0 {
		params["rate"] = job.Rate
	}
	if job.Schedule != "" {
		params["schedule"] = job.Schedule
	}
	if job.Source != "" {
		params["source"] = job.Source
	}
	if job.RemoveJob != 0 {
		params["remove_job"] = job.RemoveJob
	}

	_, err := c.request(ctx, "POST", "/cluster/replication", params)
	return err
}

// UpdateReplicationJob 更新复制任务
func (c *Client) UpdateReplicationJob(ctx context.Context, id string, job *ReplicationJob) error {
	path := fmt.Sprintf("/cluster/replication/%s", id)
	
	params := map[string]interface{}{
		"enabled": job.Enabled,
	}
	
	if job.Comment != "" {
		params["comment"] = job.Comment
	}
	if job.Rate > 0 {
		params["rate"] = job.Rate
	}
	if job.Schedule != "" {
		params["schedule"] = job.Schedule
	}
	if job.RemoveJob != 0 {
		params["remove_job"] = job.RemoveJob
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteReplicationJob 删除复制任务
func (c *Client) DeleteReplicationJob(ctx context.Context, id string) error {
	path := fmt.Sprintf("/cluster/replication/%s", id)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 指标配置 API ==========

// GetMetricsConfigs 获取指标配置列表
func (c *Client) GetMetricsConfigs(ctx context.Context) ([]*Metrics, error) {
	body, err := c.request(ctx, "GET", "/cluster/metrics/server", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var metrics []*Metrics
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &metrics); err != nil {
		return nil, err
	}

	return metrics, nil
}

// CreateMetricsConfig 创建指标配置
func (c *Client) CreateMetricsConfig(ctx context.Context, metric *Metrics) error {
	params := map[string]interface{}{
		"id":      metric.ID,
		"type":    metric.Type,
		"server":  metric.Server,
		"port":    metric.Port,
		"enabled": metric.Enabled,
	}
	
	if metric.MTU > 0 {
		params["mtu"] = metric.MTU
	}
	if metric.Timeout > 0 {
		params["timeout"] = metric.Timeout
	}
	if metric.Disable != 0 {
		params["disable"] = metric.Disable
	}

	_, err := c.request(ctx, "POST", "/cluster/metrics/server", params)
	return err
}

// UpdateMetricsConfig 更新指标配置
func (c *Client) UpdateMetricsConfig(ctx context.Context, id string, metric *Metrics) error {
	path := fmt.Sprintf("/cluster/metrics/server/%s", id)
	
	params := map[string]interface{}{
		"server":  metric.Server,
		"port":    metric.Port,
		"enabled": metric.Enabled,
	}
	
	if metric.MTU > 0 {
		params["mtu"] = metric.MTU
	}
	if metric.Timeout > 0 {
		params["timeout"] = metric.Timeout
	}
	if metric.Disable != 0 {
		params["disable"] = metric.Disable
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteMetricsConfig 删除指标配置
func (c *Client) DeleteMetricsConfig(ctx context.Context, id string) error {
	path := fmt.Sprintf("/cluster/metrics/server/%s", id)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 证书管理 API ==========

// GetCertificates 获取证书列表
func (c *Client) GetCertificates(ctx context.Context, node string) ([]*Certificate, error) {
	path := fmt.Sprintf("/nodes/%s/certificates/info", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var certificates []*Certificate
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &certificates); err != nil {
		return nil, err
	}

	return certificates, nil
}

// UploadCertificate 上传证书
func (c *Client) UploadCertificate(ctx context.Context, node, filename string, certificate, key string, force bool) error {
	path := fmt.Sprintf("/nodes/%s/certificates/custom", node)
	
	params := map[string]interface{}{
		"certificates": certificate,
	}
	
	if key != "" {
		params["key"] = key
	}
	if force {
		params["force"] = 1
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// DeleteCertificate 删除证书
func (c *Client) DeleteCertificate(ctx context.Context, node, filename string) error {
	path := fmt.Sprintf("/nodes/%s/certificates/custom/%s", node, filename)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 订阅管理 API ==========

// GetSubscription 获取订阅信息
func (c *Client) GetSubscription(ctx context.Context, node string) (*Subscription, error) {
	path := fmt.Sprintf("/nodes/%s/subscription", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var subscription Subscription
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &subscription); err != nil {
		return nil, err
	}

	return &subscription, nil
}

// UpdateSubscription 更新订阅
func (c *Client) UpdateSubscription(ctx context.Context, node, key string) error {
	path := fmt.Sprintf("/nodes/%s/subscription", node)
	
	params := map[string]interface{}{
		"key": key,
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// CheckSubscription 检查订阅状态
func (c *Client) CheckSubscription(ctx context.Context, node string) error {
	path := fmt.Sprintf("/nodes/%s/subscription", node)
	_, err := c.request(ctx, "PUT", path, nil)
	return err
}

// ========== 系统信息 API ==========

// GetClusterLog 获取集群日志
func (c *Client) GetClusterLog(ctx context.Context, max int) ([]map[string]interface{}, error) {
	params := map[string]interface{}{}
	if max > 0 {
		params["max"] = max
	}

	body, err := c.request(ctx, "GET", "/cluster/log", params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var logs []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &logs); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetNodeSyslog 获取节点系统日志
func (c *Client) GetNodeSyslog(ctx context.Context, node string, limit int, since, until string) ([]map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/syslog", node)
	
	params := map[string]interface{}{}
	if limit > 0 {
		params["limit"] = limit
	}
	if since != "" {
		params["since"] = since
	}
	if until != "" {
		params["until"] = until
	}

	body, err := c.request(ctx, "GET", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var logs []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &logs); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetSystemReport 获取系统报告
func (c *Client) GetSystemReport(ctx context.Context, node string) (string, error) {
	path := fmt.Sprintf("/nodes/%s/report", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return "", err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return "", err
	}

	if report, ok := resp.Data.(string); ok {
		return report, nil
	}

	return "", fmt.Errorf("无效的系统报告数据格式")
}