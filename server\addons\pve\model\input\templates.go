// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// TemplateCreateInp 创建模板输入
type TemplateCreateInp struct {
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
}

type TemplateCreateOut struct {
	ID uint64 `json:"id" dc:"模板ID"`
}

// TemplateEditInp 编辑模板输入
type TemplateEditInp struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
}

// TemplateDeleteInp 删除模板输入
type TemplateDeleteInp struct {
	ID uint64 `json:"id" dc:"模板ID"`
}

// TemplateViewInp 查看模板输入
type TemplateViewInp struct {
	ID uint64 `json:"id" dc:"模板ID"`
}

type TemplateViewOut struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
	CreatedAt   string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string  `json:"updatedAt" dc:"更新时间"`
}

// TemplateListInp 模板列表输入
type TemplateListInp struct {
	form.PageReq
	Name     string `json:"name" dc:"模板名称"`
	OsType   string `json:"osType" dc:"操作系统类型"`
	NodeID   uint64 `json:"nodeId" dc:"节点ID"`
	Status   int    `json:"status" dc:"状态"`
	OrderBy  string `json:"orderBy" dc:"排序字段"`
	OrderDir string `json:"orderDir" dc:"排序方向"`
}

type TemplateListOut struct {
	form.PageRes
	List []*TemplateListModel `json:"list" dc:"模板列表"`
}

type TemplateListModel struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
	CreatedAt   string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string  `json:"updatedAt" dc:"更新时间"`
}

// TemplateSyncInp 同步PVE模板输入
type TemplateSyncInp struct {
	NodeID uint64 `json:"nodeId" dc:"节点ID"`
}

type TemplateSyncOut struct {
	Success bool                  `json:"success" dc:"同步是否成功"`
	Message string                `json:"message" dc:"同步结果信息"`
	Count   int                   `json:"count" dc:"同步模板数量"`
	List    []*PVETemplateModel   `json:"list" dc:"PVE模板列表"`
}

type PVETemplateModel struct {
	VMID        int    `json:"vmid" dc:"虚拟机ID"`
	Name        string `json:"name" dc:"模板名称"`
	Description string `json:"description" dc:"描述"`
	OsType      string `json:"osType" dc:"操作系统类型"`
	Node        string `json:"node" dc:"节点名称"`
	IsTemplate  bool   `json:"isTemplate" dc:"是否为模板"`
	Status      string `json:"status" dc:"状态"`
}

// TemplateImportInp 导入PVE模板输入
type TemplateImportInp struct {
	NodeID    uint64                `json:"nodeId" dc:"节点ID"`
	Templates []*ImportTemplateData `json:"templates" dc:"要导入的模板列表"`
}

type ImportTemplateData struct {
	VMID        int     `json:"vmid" dc:"虚拟机ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
}

type TemplateImportOut struct {
	Success     bool     `json:"success" dc:"导入是否成功"`
	Message     string   `json:"message" dc:"导入结果信息"`
	ImportCount int      `json:"importCount" dc:"成功导入数量"`
	FailedCount int      `json:"failedCount" dc:"失败数量"`
	FailedList  []string `json:"failedList" dc:"失败的模板列表"`
}

// TemplateSelectInp 模板选择列表输入
type TemplateSelectInp struct {
	NodeID uint64 `json:"nodeId" dc:"节点ID"`
	Status int    `json:"status" dc:"状态，默认1启用"`
}

type TemplateSelectOut struct {
	List []*TemplateSelectModel `json:"list" dc:"模板选择列表"`
}

type TemplateSelectModel struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
}