// Package core
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

type sPveFirewall struct{}

func init() {
	service.RegisterFirewall(NewPveFirewall())
}

func NewPveFirewall() service.IFirewall {
	return &sPveFirewall{}
}

// GetRules 获取防火墙规则列表
func (s *sPveFirewall) GetRules(ctx context.Context, in *input.FirewallRulesInp) (out *input.FirewallRulesOut, err error) {
	// TODO: 实现防火墙规则获取逻辑
	// 当前返回空列表，等待PVE客户端实现防火墙API
	return &input.FirewallRulesOut{
		FirewallRulesModel: &input.FirewallRulesModel{
			List: []*input.FirewallRule{},
		},
	}, nil
}

// CreateRule 创建防火墙规则
func (s *sPveFirewall) CreateRule(ctx context.Context, in *input.FirewallCreateRuleInp) (out *input.FirewallCreateRuleModel, err error) {
	// TODO: 实现防火墙规则创建逻辑
	return &input.FirewallCreateRuleModel{
		Success: true,
	}, nil
}

// EditRule 编辑防火墙规则
func (s *sPveFirewall) EditRule(ctx context.Context, in *input.FirewallEditRuleInp) (out *input.FirewallEditRuleModel, err error) {
	// TODO: 实现防火墙规则编辑逻辑
	return &input.FirewallEditRuleModel{
		Success: true,
	}, nil
}

// DeleteRule 删除防火墙规则
func (s *sPveFirewall) DeleteRule(ctx context.Context, in *input.FirewallDeleteRuleInp) (err error) {
	// TODO: 实现防火墙规则删除逻辑
	return nil
}

// createPVEClient 创建PVE客户端
func (s *sPveFirewall) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	return pveclient.NewClient(config)
}