// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysSmsLogDao is the data access object for the table hg_sys_sms_log.
type SysSmsLogDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SysSmsLogColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SysSmsLogColumns defines and stores column names for the table hg_sys_sms_log.
type SysSmsLogColumns struct {
	Id        string // 主键
	Event     string // 事件
	Mobile    string // 手机号
	Code      string // 验证码或短信内容
	Times     string // 验证次数
	Ip        string // ip地址
	Status    string // 状态(1未验证,2已验证)
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// sysSmsLogColumns holds the columns for the table hg_sys_sms_log.
var sysSmsLogColumns = SysSmsLogColumns{
	Id:        "id",
	Event:     "event",
	Mobile:    "mobile",
	Code:      "code",
	Times:     "times",
	Ip:        "ip",
	Status:    "status",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewSysSmsLogDao creates and returns a new DAO object for table data access.
func NewSysSmsLogDao(handlers ...gdb.ModelHandler) *SysSmsLogDao {
	return &SysSmsLogDao{
		group:    "default",
		table:    "hg_sys_sms_log",
		columns:  sysSmsLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysSmsLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysSmsLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysSmsLogDao) Columns() SysSmsLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysSmsLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysSmsLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysSmsLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
