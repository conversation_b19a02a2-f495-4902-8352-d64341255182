// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalAdminCreditsLogDao is internal type for wrapping internal DAO implements.
type internalAdminCreditsLogDao = *internal.AdminCreditsLogDao

// adminCreditsLogDao is the data access object for table hg_admin_credits_log.
// You can define custom methods on it to extend its functionality as you wish.
type adminCreditsLogDao struct {
	internalAdminCreditsLogDao
}

var (
	// AdminCreditsLog is globally public accessible object for table hg_admin_credits_log operations.
	AdminCreditsLog = adminCreditsLogDao{
		internal.NewAdminCreditsLogDao(),
	}
)

// Fill with you ideas below.
