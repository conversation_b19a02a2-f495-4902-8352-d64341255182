// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

var Instances = cInstances{}

type cInstances struct{}

// Create 创建实例
func (c *cInstances) Create(ctx context.Context, req *admin.InstanceCreateReq) (res *admin.InstanceCreateRes, err error) {
	data := &input.InstanceCreateInp{
		NodeID:      req.NodeID,
		UserID:      req.UserID,
		Name:        req.Name,
		Description: req.Description,
		TemplateID:  req.TemplateID,
		CpuCores:    req.CpuCores,
		MemoryMb:    req.MemoryMb,
		DiskGb:      req.DiskGb,
		Period:      req.Period,
		PeriodType:  req.PeriodType,
		AutoRenew:   req.AutoRenew,
	}

	out, err := service.PveInstances().Create(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceCreateRes{
		InstanceID: out.InstanceID,
		VMID:       out.VMID,
		TaskID:     out.TaskID,
	}, nil
}

// Edit 编辑实例
func (c *cInstances) Edit(ctx context.Context, req *admin.InstanceEditReq) (res *admin.InstanceEditRes, err error) {
	data := &input.InstanceEditInp{
		ID:          req.ID,
		Name:        req.Name,
		Description: req.Description,
		CpuCores:    req.CpuCores,
		MemoryMb:    req.MemoryMb,
		DiskGb:      req.DiskGb,
	}

	err = service.PveInstances().Edit(ctx, data)
	return
}

// Delete 删除实例
func (c *cInstances) Delete(ctx context.Context, req *admin.InstanceDeleteReq) (res *admin.InstanceDeleteRes, err error) {
	data := &input.InstanceDeleteInp{
		ID:    req.ID,
		Force: req.Force,
	}

	out, err := service.PveInstances().Delete(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceDeleteRes{
		TaskID: out.TaskID,
	}, nil
}

// View 查看实例详情
func (c *cInstances) View(ctx context.Context, req *admin.InstanceViewReq) (res *admin.InstanceViewRes, err error) {
	data := &input.InstanceViewInp{
		ID: req.ID,
	}

	out, err := service.PveInstances().View(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceViewRes{
		InstanceViewModel: &admin.InstanceViewModel{
			ID:           out.ID,
			VMID:         out.VMID,
			NodeID:       out.NodeID,
			NodeName:     out.NodeName,
			UserID:       out.UserID,
			Username:     out.Username,
			Name:         out.Name,
			Description:  out.Description,
			OsTemplate:   out.OsTemplate,
			CpuCores:     out.CpuCores,
			MemoryMb:     out.MemoryMb,
			DiskGb:       out.DiskGb,
			IpAddress:    out.IpAddress,
			MacAddress:   out.MacAddress,
			Status:       out.Status,
			CpuUsage:     out.CpuUsage,
			MemoryUsage:  out.MemoryUsage,
			NetworkIn:    out.NetworkIn,
			NetworkOut:   out.NetworkOut,
			DiskRead:     out.DiskRead,
			DiskWrite:    out.DiskWrite,
			ExpiredAt:    out.ExpiredAt,
			CreatedAt:    out.CreatedAt,
			UpdatedAt:    out.UpdatedAt,
		},
	}, nil
}

// List 获取实例列表
func (c *cInstances) List(ctx context.Context, req *admin.InstanceListReq) (res *admin.InstanceListRes, err error) {
	data := &input.InstanceListInp{
		PageReq:  req.PageReq,
		UserID:   req.UserID,
		NodeID:   req.NodeID,
		Status:   req.Status,
		Keyword:  req.Keyword,
		OrderBy:  req.OrderBy,
		OrderDir: req.OrderDir,
	}

	out, err := service.PveInstances().List(ctx, data)
	if err != nil {
		return nil, err
	}

	var list []*admin.InstanceListModel
	for _, item := range out.List {
		list = append(list, &admin.InstanceListModel{
			ID:          item.ID,
			VMID:        item.VMID,
			NodeID:      item.NodeID,
			NodeName:    item.NodeName,
			UserID:      item.UserID,
			Username:    item.Username,
			Name:        item.Name,
			Description: item.Description,
			OsTemplate:  item.OsTemplate,
			CpuCores:    item.CpuCores,
			MemoryMb:    item.MemoryMb,
			DiskGb:      item.DiskGb,
			Status:      item.Status,
			IpAddress:   item.IpAddress,
			CpuUsage:    item.CpuUsage,
			MemoryUsage: item.MemoryUsage,
			ExpiredAt:   item.ExpiredAt,
			CreatedAt:   item.CreatedAt,
			UpdatedAt:   item.UpdatedAt,
		})
	}

	return &admin.InstanceListRes{
		PageRes: out.PageRes,
		List:    list,
	}, nil
}

// Action 执行实例操作
func (c *cInstances) Action(ctx context.Context, req *admin.InstanceActionReq) (res *admin.InstanceActionRes, err error) {
	data := &input.InstanceActionInp{
		ID:     req.ID,
		Action: req.Action,
		Force:  req.Force,
	}

	out, err := service.PveInstances().Action(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceActionRes{
		TaskID: out.TaskID,
		Status: out.Status,
	}, nil
}

// Renew 续费实例
func (c *cInstances) Renew(ctx context.Context, req *admin.InstanceRenewReq) (res *admin.InstanceRenewRes, err error) {
	data := &input.InstanceRenewInp{
		ID:         req.ID,
		Period:     req.Period,
		PeriodType: req.PeriodType,
	}

	out, err := service.PveInstances().Renew(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceRenewRes{
		OrderID:   out.OrderID,
		ExpiredAt: out.ExpiredAt,
	}, nil
}

// Console 获取控制台访问信息
func (c *cInstances) Console(ctx context.Context, req *admin.InstanceConsoleReq) (res *admin.InstanceConsoleRes, err error) {
	data := &input.InstanceConsoleInp{
		ID: req.ID,
	}

	out, err := service.PveInstances().GetConsole(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceConsoleRes{
		ConsoleURL: out.ConsoleURL,
		VncPort:    out.VncPort,
		VncTicket:  out.VncTicket,
	}, nil
}

// Snapshot 创建快照
func (c *cInstances) Snapshot(ctx context.Context, req *admin.InstanceSnapshotReq) (res *admin.InstanceSnapshotRes, err error) {
	data := &input.InstanceSnapshotInp{
		ID:          req.ID,
		SnapName:    req.SnapName,
		Description: req.Description,
	}

	out, err := service.PveInstances().CreateSnapshot(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceSnapshotRes{
		TaskID: out.TaskID,
	}, nil
}

// SnapshotList 获取快照列表
func (c *cInstances) SnapshotList(ctx context.Context, req *admin.InstanceSnapshotListReq) (res *admin.InstanceSnapshotListRes, err error) {
	data := &input.InstanceSnapshotListInp{
		ID: req.ID,
	}

	out, err := service.PveInstances().GetSnapshots(ctx, data)
	if err != nil {
		return nil, err
	}

	var list []*admin.SnapshotModel
	for _, item := range out.List {
		list = append(list, &admin.SnapshotModel{
			Name:        item.Name,
			Description: item.Description,
			SnapTime:    item.SnapTime,
			Running:     item.Running,
			Size:        item.Size,
		})
	}

	return &admin.InstanceSnapshotListRes{
		List: list,
	}, nil
}

// Monitor 获取实例监控数据
func (c *cInstances) Monitor(ctx context.Context, req *admin.InstanceMonitorReq) (res *admin.InstanceMonitorRes, err error) {
	data := &input.InstanceMonitorInp{
		ID:     req.ID,
		Period: req.Period,
	}

	out, err := service.PveInstances().GetMonitorData(ctx, data)
	if err != nil {
		return nil, err
	}

	var cpuData, memoryData, networkData, diskIOData []*admin.MonitorPoint
	for _, point := range out.CPUData {
		cpuData = append(cpuData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}
	for _, point := range out.MemoryData {
		memoryData = append(memoryData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}
	for _, point := range out.NetworkData {
		networkData = append(networkData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}
	for _, point := range out.DiskIOData {
		diskIOData = append(diskIOData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}

	return &admin.InstanceMonitorRes{
		CPUData:     cpuData,
		MemoryData:  memoryData,
		NetworkData: networkData,
		DiskIOData:  diskIOData,
	}, nil
}

// TaskStatus 获取任务状态
func (c *cInstances) TaskStatus(ctx context.Context, req *admin.InstanceTaskReq) (res *admin.InstanceTaskRes, err error) {
	data := &input.InstanceTaskInp{
		TaskID: req.TaskID,
	}

	out, err := service.PveInstances().GetTaskStatus(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.InstanceTaskRes{
		TaskID:     out.TaskID,
		Status:     out.Status,
		ExitStatus: out.ExitStatus,
		StartTime:  out.StartTime,
		EndTime:    out.EndTime,
		Log:        out.Log,
	}, nil
}

// Clone 克隆虚拟机
func (c *cInstances) Clone(ctx context.Context, req *admin.InstanceCloneReq) (res *admin.InstanceCloneRes, err error) {
	// TODO: 实现虚拟机克隆逻辑
	return &admin.InstanceCloneRes{
		TaskID: "tmp-clone-task",
	}, nil
}

// Migrate 迁移虚拟机  
func (c *cInstances) Migrate(ctx context.Context, req *admin.InstanceMigrateReq) (res *admin.InstanceMigrateRes, err error) {
	// TODO: 实现虚拟机迁移逻辑
	return &admin.InstanceMigrateRes{
		TaskID: "tmp-migrate-task",
	}, nil
}

// GetRRDData 获取监控数据
func (c *cInstances) GetRRDData(ctx context.Context, req *admin.InstanceRRDDataReq) (res *admin.InstanceRRDDataRes, err error) {
	// TODO: 实现监控数据获取逻辑
	return &admin.InstanceRRDDataRes{
		Data: map[string]interface{}{
			"cpu":    []float64{},
			"memory": []float64{},
			"disk":   []float64{},
		},
	}, nil
}