// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// sysAddonsInstallDao is the data access object for the table hg_sys_addons_install.
// You can define custom methods on it to extend its functionality as needed.
type sysAddonsInstallDao struct {
	*internal.SysAddonsInstallDao
}

var (
	// SysAddonsInstall is a globally accessible object for table hg_sys_addons_install operations.
	SysAddonsInstall = sysAddonsInstallDao{internal.NewSysAddonsInstallDao()}
)

// Add your custom methods and functionality below.
