// Package crons
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package crons

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
	"hotgo/internal/consts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/adminin"
	adminService "hotgo/internal/service"
)

func init() {
	// 注册定时任务
	registerCronJobs()
}

// registerCronJobs 注册定时任务
func registerCronJobs() {
	ctx := gctx.New()

	// 每5分钟同步一次节点状态
	gcron.Add(ctx, "*/5 * * * *", func(ctx context.Context) {
		g.Log().Debug(ctx, "PVE插件 - 开始同步节点状态")
		syncAllNodesStatus(ctx)
	}, "pve_sync_nodes")

	// 每10分钟同步一次实例状态
	gcron.Add(ctx, "*/10 * * * *", func(ctx context.Context) {
		g.Log().Debug(ctx, "PVE插件 - 开始同步实例状态")
		syncAllInstancesStatus(ctx)
	}, "pve_sync_instances")

	// 每天凌晨1点检查实例到期时间
	gcron.Add(ctx, "0 1 * * *", func(ctx context.Context) {
		g.Log().Debug(ctx, "PVE插件 - 开始检查实例到期时间")
		checkInstanceExpiration(ctx)
	}, "pve_check_expiration")

	g.Log().Info(ctx, "PVE插件定时任务注册完成")
}

// syncAllNodesStatus 同步所有节点状态
func syncAllNodesStatus(ctx context.Context) {
	defer func() {
		if err := recover(); err != nil {
			g.Log().Error(ctx, "同步节点状态失败:", err)
		}
	}()

	// 获取所有正常状态的节点
	nodes, err := g.DB().Model("hg_pve_nodes").Where("status", 1).All()
	if err != nil {
		g.Log().Error(ctx, "获取节点列表失败:", err)
		return
	}

	for _, node := range nodes {
		nodeID := gconv.Uint64(node["id"])
		
		// 调用节点同步服务
		_, err := service.PveNodes().SyncStatus(ctx, &input.NodeSyncInp{
			ID: nodeID,
		})
		
		if err != nil {
			g.Log().Error(ctx, "同步节点状态失败:", g.Map{
				"nodeId": nodeID,
				"error":  err.Error(),
			})
		} else {
			g.Log().Debug(ctx, "节点状态同步成功:", g.Map{
				"nodeId": nodeID,
			})
		}
	}

	g.Log().Info(ctx, "所有节点状态同步完成")
}

// syncAllInstancesStatus 同步所有实例状态
func syncAllInstancesStatus(ctx context.Context) {
	defer func() {
		if err := recover(); err != nil {
			g.Log().Error(ctx, "同步实例状态失败:", err)
		}
	}()

	// 获取所有实例
	instances, err := g.DB().Model("hg_pve_instances i").
		InnerJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.id, i.vmid, i.node_id, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("n.status = ?", 1).
		All()

	if err != nil {
		g.Log().Error(ctx, "获取实例列表失败:", err)
		return
	}

	syncCount := 0
	failedCount := 0
	
	for _, instance := range instances {
		instanceID := gconv.Uint64(instance["id"])
		vmid := gconv.Int(instance["vmid"])
		nodeName := gconv.String(instance["node_name"])

		// 创建PVE客户端配置
		nodeData := g.Map{
			"host":         instance["host"],
			"port":         instance["port"],
			"username":     instance["username"],
			"password":     instance["password"],
			"token_id":     instance["token_id"],
			"token_secret": instance["token_secret"],
		}
		config := createPVEClientConfig(nodeData)
		client, err := createPVEClient(config)
		if err != nil {
			g.Log().Error(ctx, "创建PVE客户端失败:", g.Map{
				"instanceId": instanceID,
				"error":      err.Error(),
			})
			failedCount++
			continue
		}

		// 获取虚拟机状态
		vmStatus, err := client.GetVMStatus(ctx, nodeName, vmid)
		if err != nil {
			g.Log().Warning(ctx, "获取虚拟机状态失败:", g.Map{
				"instanceId": instanceID,
				"vmid":       vmid,
				"node":       nodeName,
				"error":      err.Error(),
			})
			failedCount++
			continue
		}

		// 更新实例状态到数据库
		updateData := g.Map{
			"status":        vmStatus.Status,
			"cpu_usage":     vmStatus.CPU * 100,
			"memory_usage":  float64(vmStatus.Memory) / float64(vmStatus.MaxMem) * 100,
			"updated_at":    gtime.Now(),
		}

		// 如果有网络流量信息，也更新
		vmInfo, err := client.GetVMs(ctx, nodeName)
		if err == nil {
			for _, vm := range vmInfo {
				if vm.VMID == vmid {
					updateData["network_in"] = vm.NetIn
					updateData["network_out"] = vm.NetOut
					updateData["disk_read"] = vm.DiskRead
					updateData["disk_write"] = vm.DiskWrite
					break
				}
			}
		}

		_, err = g.DB().Model("hg_pve_instances").
			Data(updateData).
			Where("id", instanceID).
			Update()

		if err != nil {
			g.Log().Error(ctx, "更新实例状态失败:", g.Map{
				"instanceId": instanceID,
				"error":      err.Error(),
			})
			failedCount++
		} else {
			syncCount++
			g.Log().Debug(ctx, "实例状态同步成功:", g.Map{
				"instanceId": instanceID,
				"status":     vmStatus.Status,
			})
		}
	}

	g.Log().Info(ctx, "实例状态同步完成:", g.Map{
		"syncCount":   syncCount,
		"failedCount": failedCount,
		"totalCount":  len(instances),
	})
}

// checkInstanceExpiration 检查实例到期时间
func checkInstanceExpiration(ctx context.Context) {
	defer func() {
		if err := recover(); err != nil {
			g.Log().Error(ctx, "检查实例到期时间失败:", err)
		}
	}()

	// 获取即将到期的实例（7天内）
	expiredInstances, err := g.DB().Model("hg_pve_instances").
		Fields("id, name, expired_at").
		Where("expired_at IS NOT NULL AND expired_at <= DATE_ADD(NOW(), INTERVAL 7 DAY) AND status != 'expired'").
		All()

	if err != nil {
		g.Log().Error(ctx, "获取即将到期实例失败:", err)
		return
	}

	for _, instance := range expiredInstances {
		instanceID := gconv.Uint64(instance["id"])
		instanceName := gconv.String(instance["name"])
		expiredAt := gconv.Time(instance["expired_at"])
		
		// 如果已经过期，标记为过期状态
		if expiredAt.Before(gtime.Now().Time) {
			_, err := g.DB().Model("hg_pve_instances").
				Data(g.Map{"status": "expired"}).
				Where("id", instanceID).
				Update()
			
			if err != nil {
				g.Log().Error(ctx, "更新实例过期状态失败:", g.Map{
					"instanceId": instanceID,
					"error":      err.Error(),
				})
			} else {
				g.Log().Info(ctx, "实例已标记为过期:", g.Map{
					"instanceId": instanceID,
				})
			}
		} else {
			// 发送到期提醒通知
			g.Log().Info(ctx, "实例即将到期:", g.Map{
				"instanceId": instanceID,
				"expiredAt":  expiredAt,
			})
			// 发送到期提醒通知
			err := sendExpirationNotification(ctx, instanceName, instanceID, gtime.New(expiredAt))
			if err != nil {
				g.Log().Error(ctx, "发送到期通知失败:", err)
			}
		}
	}

	g.Log().Info(ctx, "实例到期检查完成")
}

// createPVEClientConfig 创建PVE客户端配置
func createPVEClientConfig(nodeData g.Map) *pveclient.Config {
	return &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}
}

// createPVEClient 创建PVE客户端
func createPVEClient(config *pveclient.Config) (*pveclient.Client, error) {
	return pveclient.NewClient(config)
}

// sendExpirationNotification 发送到期提醒通知
func sendExpirationNotification(ctx context.Context, instanceName string, instanceID uint64, expiredAt *gtime.Time) error {
	// 获取实例管理员用户ID
	instance, err := g.DB().Model("hg_pve_instances i").
		LeftJoin("admin_member am", "i.created_by = am.id").
		Fields("i.created_by, am.email, am.real_name").
		Where("i.id", instanceID).
		One()
	if err != nil {
		return err
	}

	if instance == nil {
		return fmt.Errorf("实例 %d 不存在", instanceID)
	}

	createdBy := gconv.Int64(instance["created_by"])
	if createdBy <= 0 {
		return fmt.Errorf("实例创建者信息不完整")
	}

	// 计算剩余天数
	remainingDays := expiredAt.Sub(gtime.Now()).Hours() / 24

	// 构建通知内容
	title := fmt.Sprintf("PVE实例到期提醒")
	content := fmt.Sprintf(`
<div>
	<h3>PVE实例即将到期提醒</h3>
	<p><strong>实例名称：</strong>%s</p>
	<p><strong>实例ID：</strong>%d</p>
	<p><strong>到期时间：</strong>%s</p>
	<p><strong>剩余天数：</strong>%.1f 天</p>
	<p style="color: red;"><strong>提醒：</strong>请及时续费，避免服务中断。</p>
</div>
`, instanceName, instanceID, expiredAt.Format("2006-01-02 15:04:05"), remainingDays)

	// 发送系统通知
	err = adminService.AdminNotice().Edit(ctx, &adminin.NoticeEditInp{
		AdminNotice: entity.AdminNotice{
			Title:   title,
			Content: content,
			Type:    consts.NoticeTypeLetter, // 私信类型
		},
		Receiver: []int64{createdBy}, // 发送给实例创建者
	})

	if err != nil {
		return fmt.Errorf("发送系统通知失败: %v", err)
	}

	g.Log().Info(ctx, "PVE实例到期提醒通知已发送", g.Map{
		"instanceId": instanceID,
		"userId":     createdBy,
		"expiredAt":  expiredAt,
	})

	return nil
}