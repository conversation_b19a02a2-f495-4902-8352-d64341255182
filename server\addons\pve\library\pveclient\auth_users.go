// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== 用户管理 API ==========

// GetUsers 获取用户列表
func (c *Client) GetUsers(ctx context.Context) ([]*User, error) {
	body, err := c.request(ctx, "GET", "/access/users", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var users []*User
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &users); err != nil {
		return nil, err
	}

	return users, nil
}

// GetUser 获取用户详情
func (c *Client) GetUser(ctx context.Context, userid string) (*User, error) {
	path := fmt.Sprintf("/access/users/%s", userid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var user User
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &user); err != nil {
		return nil, err
	}

	return &user, nil
}

// CreateUser 创建用户
func (c *Client) CreateUser(ctx context.Context, user *User, password string) error {
	params := map[string]interface{}{
		"userid": user.UserID,
	}
	
	if password != "" {
		params["password"] = password
	}
	if user.Comment != "" {
		params["comment"] = user.Comment
	}
	if user.Email != "" {
		params["email"] = user.Email
	}
	if user.Firstname != "" {
		params["firstname"] = user.Firstname
	}
	if user.Lastname != "" {
		params["lastname"] = user.Lastname
	}
	if len(user.Groups) > 0 {
		params["groups"] = user.Groups
	}
	if user.Keys != "" {
		params["keys"] = user.Keys
	}
	params["enable"] = user.Enable
	if user.Expire > 0 {
		params["expire"] = user.Expire
	}

	_, err := c.request(ctx, "POST", "/access/users", params)
	return err
}

// UpdateUser 更新用户
func (c *Client) UpdateUser(ctx context.Context, userid string, user *User, password string) error {
	path := fmt.Sprintf("/access/users/%s", userid)
	
	params := map[string]interface{}{}
	
	if password != "" {
		params["password"] = password
	}
	if user.Comment != "" {
		params["comment"] = user.Comment
	}
	if user.Email != "" {
		params["email"] = user.Email
	}
	if user.Firstname != "" {
		params["firstname"] = user.Firstname
	}
	if user.Lastname != "" {
		params["lastname"] = user.Lastname
	}
	if len(user.Groups) > 0 {
		params["groups"] = user.Groups
	}
	if user.Keys != "" {
		params["keys"] = user.Keys
	}
	params["enable"] = user.Enable
	if user.Expire > 0 {
		params["expire"] = user.Expire
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteUser 删除用户
func (c *Client) DeleteUser(ctx context.Context, userid string) error {
	path := fmt.Sprintf("/access/users/%s", userid)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 组管理 API ==========

// GetGroups 获取组列表
func (c *Client) GetGroups(ctx context.Context) ([]*Group, error) {
	body, err := c.request(ctx, "GET", "/access/groups", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var groups []*Group
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &groups); err != nil {
		return nil, err
	}

	return groups, nil
}

// CreateGroup 创建组
func (c *Client) CreateGroup(ctx context.Context, group *Group) error {
	params := map[string]interface{}{
		"groupid": group.GroupID,
	}
	
	if group.Comment != "" {
		params["comment"] = group.Comment
	}

	_, err := c.request(ctx, "POST", "/access/groups", params)
	return err
}

// UpdateGroup 更新组
func (c *Client) UpdateGroup(ctx context.Context, groupid string, group *Group) error {
	path := fmt.Sprintf("/access/groups/%s", groupid)
	
	params := map[string]interface{}{}
	
	if group.Comment != "" {
		params["comment"] = group.Comment
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteGroup 删除组
func (c *Client) DeleteGroup(ctx context.Context, groupid string) error {
	path := fmt.Sprintf("/access/groups/%s", groupid)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 角色管理 API ==========

// GetRoles 获取角色列表
func (c *Client) GetRoles(ctx context.Context) ([]*Role, error) {
	body, err := c.request(ctx, "GET", "/access/roles", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var roles []*Role
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &roles); err != nil {
		return nil, err
	}

	return roles, nil
}

// CreateRole 创建角色
func (c *Client) CreateRole(ctx context.Context, role *Role) error {
	params := map[string]interface{}{
		"roleid": role.RoleID,
	}
	
	if role.Privs != nil {
		for priv, granted := range role.Privs {
			if granted {
				params[priv] = 1
			}
		}
	}

	_, err := c.request(ctx, "POST", "/access/roles", params)
	return err
}

// UpdateRole 更新角色
func (c *Client) UpdateRole(ctx context.Context, roleid string, role *Role) error {
	path := fmt.Sprintf("/access/roles/%s", roleid)
	
	params := map[string]interface{}{}
	
	if role.Privs != nil {
		for priv, granted := range role.Privs {
			if granted {
				params[priv] = 1
			} else {
				params[priv] = 0
			}
		}
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteRole 删除角色
func (c *Client) DeleteRole(ctx context.Context, roleid string) error {
	path := fmt.Sprintf("/access/roles/%s", roleid)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== ACL管理 API ==========

// GetACL 获取访问控制列表
func (c *Client) GetACL(ctx context.Context) ([]*ACL, error) {
	body, err := c.request(ctx, "GET", "/access/acl", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var acl []*ACL
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &acl); err != nil {
		return nil, err
	}

	return acl, nil
}

// UpdateACL 更新访问控制列表
func (c *Client) UpdateACL(ctx context.Context, acl *ACL) error {
	params := map[string]interface{}{
		"path":   acl.Path,
		"roles":  acl.RoleID,
		acl.Type: acl.UGid,
	}
	
	if acl.Propagate != 0 {
		params["propagate"] = acl.Propagate
	}

	_, err := c.request(ctx, "PUT", "/access/acl", params)
	return err
}

// DeleteACL 删除访问控制列表条目
func (c *Client) DeleteACL(ctx context.Context, path, ugid, roleid string) error {
	params := map[string]interface{}{
		"path":   path,
		"roles":  roleid,
		"users":  ugid,
		"delete": 1,
	}

	_, err := c.request(ctx, "PUT", "/access/acl", params)
	return err
}

// ========== 令牌管理 API ==========

// GetUserTokens 获取用户令牌列表
func (c *Client) GetUserTokens(ctx context.Context, userid string) (map[string]*Token, error) {
	path := fmt.Sprintf("/access/users/%s/token", userid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var tokens map[string]*Token
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &tokens); err != nil {
		return nil, err
	}

	return tokens, nil
}

// CreateUserToken 创建用户令牌
func (c *Client) CreateUserToken(ctx context.Context, userid string, token *Token) (string, error) {
	path := fmt.Sprintf("/access/users/%s/token/%s", userid, token.TokenID)
	
	params := map[string]interface{}{
		"privsep": token.Privsep,
	}
	
	if token.Comment != "" {
		params["comment"] = token.Comment
	}
	if token.Expire > 0 {
		params["expire"] = token.Expire
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return "", err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return "", err
	}

	// 返回生成的令牌值
	if data, ok := resp.Data.(map[string]interface{}); ok {
		if value, exists := data["value"]; exists {
			if tokenValue, ok := value.(string); ok {
				return tokenValue, nil
			}
		}
	}

	return "", fmt.Errorf("无法获取令牌值")
}

// UpdateUserToken 更新用户令牌
func (c *Client) UpdateUserToken(ctx context.Context, userid, tokenid string, token *Token) error {
	path := fmt.Sprintf("/access/users/%s/token/%s", userid, tokenid)
	
	params := map[string]interface{}{}
	
	if token.Comment != "" {
		params["comment"] = token.Comment
	}
	if token.Expire > 0 {
		params["expire"] = token.Expire
	}
	params["privsep"] = token.Privsep

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteUserToken 删除用户令牌
func (c *Client) DeleteUserToken(ctx context.Context, userid, tokenid string) error {
	path := fmt.Sprintf("/access/users/%s/token/%s", userid, tokenid)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 认证域管理 API ==========

// GetRealms 获取认证域列表
func (c *Client) GetRealms(ctx context.Context) ([]*Realm, error) {
	body, err := c.request(ctx, "GET", "/access/domains", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var realms []*Realm
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &realms); err != nil {
		return nil, err
	}

	return realms, nil
}

// CreateRealm 创建认证域
func (c *Client) CreateRealm(ctx context.Context, realm *Realm) error {
	params := map[string]interface{}{
		"realm": realm.Realm,
		"type":  realm.Type,
	}
	
	if realm.Comment != "" {
		params["comment"] = realm.Comment
	}
	if realm.Default != 0 {
		params["default"] = realm.Default
	}
	if realm.Domain != "" {
		params["domain"] = realm.Domain
	}
	if realm.Port > 0 {
		params["port"] = realm.Port
	}
	if realm.Secure != 0 {
		params["secure"] = realm.Secure
	}
	if realm.Server1 != "" {
		params["server1"] = realm.Server1
	}
	if realm.Server2 != "" {
		params["server2"] = realm.Server2
	}
	if realm.TFA != "" {
		params["tfa"] = realm.TFA
	}

	_, err := c.request(ctx, "POST", "/access/domains", params)
	return err
}

// UpdateRealm 更新认证域
func (c *Client) UpdateRealm(ctx context.Context, realmid string, realm *Realm) error {
	path := fmt.Sprintf("/access/domains/%s", realmid)
	
	params := map[string]interface{}{}
	
	if realm.Comment != "" {
		params["comment"] = realm.Comment
	}
	if realm.Default != 0 {
		params["default"] = realm.Default
	}
	if realm.Domain != "" {
		params["domain"] = realm.Domain
	}
	if realm.Port > 0 {
		params["port"] = realm.Port
	}
	if realm.Secure != 0 {
		params["secure"] = realm.Secure
	}
	if realm.Server1 != "" {
		params["server1"] = realm.Server1
	}
	if realm.Server2 != "" {
		params["server2"] = realm.Server2
	}
	if realm.TFA != "" {
		params["tfa"] = realm.TFA
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteRealm 删除认证域
func (c *Client) DeleteRealm(ctx context.Context, realmid string) error {
	path := fmt.Sprintf("/access/domains/%s", realmid)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}