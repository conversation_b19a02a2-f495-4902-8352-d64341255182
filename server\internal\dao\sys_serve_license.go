// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysServeLicenseDao is internal type for wrapping internal DAO implements.
type internalSysServeLicenseDao = *internal.SysServeLicenseDao

// sysServeLicenseDao is the data access object for table hg_sys_serve_license.
// You can define custom methods on it to extend its functionality as you wish.
type sysServeLicenseDao struct {
	internalSysServeLicenseDao
}

var (
	// SysServeLicense is globally public accessible object for table hg_sys_serve_license operations.
	SysServeLicense = sysServeLicenseDao{
		internal.NewSysServeLicenseDao(),
	}
)

// Fill with you ideas below.
