## 更新历史

升级说明：  

> 创建一个Git版本库，并创建二个分支比如A、B，A分支默认为你的二开分支、B分支为你的版本库分支。  
> 等需要升级的时候，把版本库分支(B)升级为最新的版本，然后切换到A分支，把B分支合并过来，解决掉合并冲突即可。

注意：

> 各个小版本升级例如 2.1.x 升级到 2.2.x 以上不能完美升级最好重新安装  

> 如果升级(覆盖)代码后打开会出现 sql 报错, 请检查更新的数据库格式或自行调整


### v2.17.8
updated 2025.7.13

- 优化：表格排序处理器兼容关联表别名
- 优化：优化动态统计数字在0值时显示问题
- 优化：优化首页快捷菜单点击事件范围
- 优化：优化短信、邮件验证码相关验证类数据排序
- 优化：优化Nginx配置，支持流式请求和兼容部分版本无法匹配到websocket规则问题
- 优化：naive-ui版本升级到2.42.0
- 优化：vue3-json-viewer版本升级到2.4.1
- 修复：修复可选用户选项`Fields`生成错误问题


### v2.16.10
updated 2025.3.22

- 增加：增加组合下拉用户筛选组件
- 优化：文件上传类型限制改为统一由后台配置控制
- 优化：websocket重连时间调整为10秒，初始化数据库中的网站地址调整为`127.0.0.1`
- 优化：优化访问日志字段展示方式
- 优化：系统公告发送编辑模态框调整为抽屉
- 优化：gf版本升级到v2.9.0
- 优化：naive-ui版本升级到2.41.0
- 修复：修复vue路由`Redirect`命名重复问题
- 修复：优化代码生成在插件中`使用生成字典选项`时model包名重复问题


### v2.15.11
updated 2024.11.27

- 增加：增加配置管理子页面选项参数记忆
- 修复：修复定时任务中日志配置的读取并发读问题
- 优化：gf版本升级到v2.8.2
- 优化：优化数据卡片展示


### v2.15.7
updated 2024.7.21

- 增加：访问日志、服务日志增加关键词搜索
- 增加：web端增加字典状态管理，重构字典选项使用方式，大幅减少冗余代码
- 修复：修复生成代码选项式树表已知的一些小bug
- 优化：gf版本升级到v2.7.2
- 优化：naive-ui版本升级到2.39.0
- 优化：访问日志不再记录过大的请求头参数，减少日志大小


### v2.15.1
updated 2024.4.22

- 增加：生成代码增加树表生成、字段排序、字段重置、操作人维护，调整关联表生成方式
- 增加：角色菜单权限增加一键导入支持
- 增加：访问日志增加忽略请求方式
- 增加：插件生成增加`consts`目录维护
- 增加：IP归属地定位增加默认重试，增加新的缓存机制
- 增加：登录日志增加UA信息、IP归属信息，解藕访问日志
- 增加：消息队列`rocketmq`驱动更新版本问题，增加延迟队列，支持自动创建主题
- 增加：部门增加类型支持，通过部门类型实现多租户业务支持
- 增加：增加多租户业务开发演示
- 修复：修复部门列表搜索算法在部分情况下无效问题
- 修复：修复短信、邮件IP发送数量限制
- 修复：修复分布式锁内存泄漏问题
- 优化：gf版本升级到v2.7.0
- 优化：naive-ui版本升级到2.38.1
- 优化：优化`cmd`服务退出流程
- 优化：优化生成代码格式化和移除未使用的包
- 优化：服务端配置文件参数`hotgo`改为`system`
- 优化：web端表格/表单增加自适应宽度计算
- 优化：日期选择组件不再使用默认值
- 优化：优化菜单添加/编辑表单，使其操作更加简单方便


### v2.13.1
updated 2024.3.7

- 增加：增加内置数据字典类型：`枚举字典`和`自定义方法字典`，支持代码生成时关联选项使用
- 增加：增加大文件上传，支持分片上传、断点续传，存储驱动已适配`本地存储`
- 增加：插件模块增加停止服务回调接口，调整静态资源默认存放位置，创建插件选项增加可选扩展功能
- 增加：功能案例插件增加`30+`常用组件示例，增加`websocket`消息收发测试
- 增加：文档增`加功能扩展库`、`websocket服务器`、`websocket客户端`使用说明，当前版本文档已完善
- 修复：修复省市区无法添加地区问题
- 优化：gf版本升级到v2.6.4
- 优化：优化缓存组件依赖关系
- 优化：调整部分前端表格自适应宽度
- 优化：HTTP错误码接管统一改为由响应中间件处理

### v2.12.1
updated 2023.12.29

- 修复：修复访问日志权限过滤
- 优化：gf版本升级到v2.6.1
- 优化：naive-ui版本升级到2.36.0，vue版本升级到3.4.0，vite版本升级到4.2.7
- 优化：优化curd代码生成，编辑表单增加自适应，详情改为`抽屉`，修复菜单权限关系树，简化`State`
- 优化：优化字典编辑和查询
- 优化：优化角色列表数据加载缓慢问题

### v2.11.5
updated 2023.11.25

- 增加`minio`上传驱动支持
- 增加定时任务调度日志，增加同方法多任务支持
- 增加gf运行模式控制，生产环境时隐藏开发相关菜单
- 增加树形表格新增/修改自动维护上下级关系通用处理
- 增加树形表格使用例子
- 优化：gf版本升级到v2.5.7
- 优化`websocket`默认路径配置
- 优化预处理中间件错误码跟随
- 优化插件静态目录为可选项
- 修复升级gf版本后服务日志记录内容为空问题
- 修复角色权限选择数据重复问题
- 修复服务日志无日志内容问题
- 修复用户批量删除可以删除超管问题
- 修复代码生成时偶尔找不到主键索引问题
- 修复树形选项组件在`BasicForm`无法使用问题
- 修复表格批量删除重复传参问题
- 修复文件缓存`GetOrSet`和`GetOrSetFunc`缓存过期不刷新问题

### v2.9.8
updated 2023.10.21

- 优化：gf版本升级到v2.5.5
- 优化：优化代码生成表单滑动
- 优化：优化字典列表编辑生效时机
- 修复：修复日期组件默认值无效问题
### v2.9.3
updated 2023.10.08

- 优化：gf版本升级到v2.5.4
- 优化：优化上传文件资源复用策略
- 修复：修复登录角色的部门状态检查
- 修复：修复配置参数`exceptLogin`命名

### v2.8.9
updated 2023.09.06

- 优化：gf版本升级到v2.5.2
- 优化：优化代码生成，自定义模板时自动过滤前缀，菜单权限生成预检查
- 优化：服务日志内容长度超出最大限制截取
- 修复：修复tcp服务器定时任务执行异常
- 修复：修复文件选择器错误传参导致的显示异常问题

### v2.8.4
updated 2023.07.20

- 增加：增加输入预处理中间件
- 增加：增加文件选择器
- 增加：增加在线服务监控，服务许可证管理
- 增加：TCP服务器增加RPC路由消息注册、路由分发、拦截器注册，更方便的进行应用开发
- 优化：gf版本升级到v2.5.0
- 优化：优化CURD代码生成，简化控制器代码逻辑，升级列表数据查询方式
- 修复：修复角色菜单子权限取消导致父级权限失效问题
- 修复：修复上传驱动路径错误
- 修复：修复CURD代码生成时间类型字段默认值获取异常

### v2.7.6
updated 2023.06.19

- 优化：gf版本升级到v2.4.3
- 修复：部门管理查询空指针问题
- 优化：附件md5生成方式调整（与更新前已上传的文件md5不兼容，如果业务有影响请注意调整）

### v2.7.3
updated 2023.05.14

- 增加：增加注册、手机号登录、二维码邀请注册功能
- 优化：优化管理员业务模块验证唯一属性接口，将登录相关功能拆分到管理员基础模块中
- 移除：移除web端mock插件

### v2.6.10
updated 2023.05.12

- 增加：增加jwt+缓存驱动实现的令牌认证，支持自动续约、可控过期失效策略
- 移除：移除旧jwt功能库
- 移除：移除`decimal`、`mahonia` package

### v2.6.7
updated 2023.05.10

- 增加：增加支付网关：集成支付宝、微信支付、QQ支付，可通过后台直接配置参数
- 增加：增加资金管理模块，包含在线充值、申请提现、资金变动明细功能
- 增加：增加redis延迟队列
- 增加：tcp服务增加rpc协议通讯支持
- 增加：通过微信访问后台自动获取openid，后续微信相关业务使用
- 优化：gf版本升级到v2.4.1
- 优化：优化CURD代码生成单表列表
- 优化：优化菜单权限路由，解决偶尔修改权限不能实时生效的情况
- 优化：优化后台导出功能搜索参数无效问题
- 优化：将定时任务拆分成可独立部署的程序，通过tcp长连接方式与后台交互
- 修复：修复相同文件多次上传页面无响应问题
- 修复：修复新用户不更新活跃时间问题
- 修复：修复本地缓存当缓存不存在时的异常错误问题
- 修复：修复访问日志偶尔出现无法记录问题

### v2.5.3
updated 2023.04.10

- 优化：优化IP地理位置定位，增加地理位置缓存，避免相同IP并发操作
- 优化：优化日志管理中相关列表字段过多在小屏设备列表不显示滚动条情况
- 增加：增加短信驱动：腾讯云短信，可通过后台一键切换
- 修复：修复角色权限选择自定义部门时，部门选项无法显示问题

### v2.4.9
updated 2023.04.05

- 优化：优化全局日志数据加载和分页加载，大分页增加分批次加载
- 优化：优化TCP服务认证，超出多端登录上限时直接踢掉所有在线让其重连
- 增加：增加上传驱动：腾讯云cos、阿里云oss、七牛云对象存储
- 优化：优化代码生成文档，数据库文档增加多数据库生成配置
- 修复：修复系统监控在苹果m1芯片空指针以及显示问题

### v2.4.4
updated 2023.03.16

- 优化：优化代码生成多库生成时的菜单sql默认指向默认数据库分组
- 优化：优化TCP服务认证机制

### v2.4.2
updated 2023.03.11

- 修复：修复字典管理列表无法添加/编辑问题
- 优化：优化代码生成文档
- 优化：优化部门树形选项排序问题
- 优化：优化基础设置中的LOGO图片上传组件
- 优化：优化黑名单IP过滤策略
- 增加：增加基于gtcp实现的C/S服务器，为后续多服务通讯建立基础库
- 优化：gf版本升级到v2.3.3

### v2.3.5
updated 2023.02.26

- 优化: 调整消息队列消费初始化方式，支持在插件模块下注册消费者
- 修复：后台用户添加模态框无法正常显示问题
- 优化：创建新插件生成增加web页面的生成
- 增加：增加生产部署、开发规范、生成代码、表单组件等相关文档
- 修复：修复后台已知的一些小bug

### v2.2.10
updated 2023.02.23

- 增加: 增加插件管理、设计新插件等插件模块
- 增加: 增加插件应用：`功能案例`
- 增加: 增加使用文档
- 优化: 生成代码适配插件模块
- 优化：消息队列增加磁盘队列，默认队列从`redis`改为`disk`
- 优化：缓存驱动增加文件适配器，默认驱动从`redis`改为`file`，项目安装不再依赖`redis`
- 优化：优化系统监控加载流程
- 优化：gf版本升级到v2.3.2
- 修复：生成代码刷新后偶尔出现加载失败问题

### v2.2.1
updated 2023.01.12

- 优化: 缓存清理各个应用的缓存文件夹读写判断
- 修复: Linux 环境下创建插件报找不到模板文件
- 修复: Excel 导入找不到最后一列数据
- 增加: 无权限菜单不显示
- 优化: 省市区数据为最新的 2023.01.11 的国家统计局省市区数据
- 优化: 前台关于图片上传的 js 和 css 引入，避免资源依赖找不到
- 优化: 插件模块查询机制, 增加数据缓存依赖，依赖时间为 360 秒
- 优化: 文件上传的处理
- 修复: 网站配置的多图上传引入路径错误
- 修复: 定时任务协程问题导致执行失败
- 
### v2.1.1
updated 2022.9.26

- 优化: 多图上传的显示样式及功能
- 修复: 由于上传视频和语音开启了全域名返回导致的上传到服务器错误
- 增加: 由增加home页面入口，前台页面

### v2.0.8
updated 2022.9.21

- 优化: 优化菜单和角色数据权限，支持数据权限和按钮细分权限
- 优化: 优化附件上传，增加文件上传选项

### v2.0.6
updated 2022.9.18

- 增加: 增加阿里云短信配置和发送短信API
- 增加: 增加高德地图配置
- 优化: 优化数据字典，增加数据类型和标签支持
- 
### v2.0.3
updated 2022.9.10

- 2.0 全新上线

### v2.0.2
updated 2022.6.11

- 增加: 增加生成代码功能，目前已支持一键生成CURD和关联表
- 增加: 增加云存储UCloud配置，后台一键切换驱动

### v2.0.0
updated 2022.5.20
 
 - 初始化: 2.0 基础框架
