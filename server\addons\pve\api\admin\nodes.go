// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/model/input/form"
)

// NodeCreateReq 创建节点请求
type NodeCreateReq struct {
	g.Meta      `path:"/nodes" method:"post" summary:"创建PVE节点" tags:"PVE管理"`
	Name        string `json:"name" v:"required|length:1,100#节点名称不能为空|名称长度为1-100个字符" dc:"节点名称"`
	Host        string `json:"host" v:"required|length:1,255#节点地址不能为空|地址长度为1-255个字符" dc:"节点地址"`
	Port        int    `json:"port" v:"required|between:1,65535#端口不能为空|端口范围为1-65535" dc:"端口，默认8006"`
	Username    string `json:"username" dc:"用户名"`
	Password    string `json:"password" dc:"密码"`
	TokenID     string `json:"tokenId" dc:"API Token ID"`
	TokenSecret string `json:"tokenSecret" dc:"API Token Secret"`
	Status      int    `json:"status" v:"in:1,2#状态值错误" dc:"状态：1=正常 2=异常"`
}

type NodeCreateRes struct {
	ID uint64 `json:"id" dc:"节点ID"`
}

// NodeEditReq 编辑节点请求
type NodeEditReq struct {
	g.Meta      `path:"/nodes/{id}" method:"put" summary:"编辑PVE节点" tags:"PVE管理"`
	ID          uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
	Name        string `json:"name" v:"required|length:1,100#节点名称不能为空|名称长度为1-100个字符" dc:"节点名称"`
	Host        string `json:"host" v:"required|length:1,255#节点地址不能为空|地址长度为1-255个字符" dc:"节点地址"`
	Port        int    `json:"port" v:"required|between:1,65535#端口不能为空|端口范围为1-65535" dc:"端口"`
	Username    string `json:"username" dc:"用户名"`
	Password    string `json:"password" dc:"密码"`
	TokenID     string `json:"tokenId" dc:"API Token ID"`
	TokenSecret string `json:"tokenSecret" dc:"API Token Secret"`
	Status      int    `json:"status" v:"in:1,2#状态值错误" dc:"状态：1=正常 2=异常"`
}

type NodeEditRes struct{}

// NodeDeleteReq 删除节点请求
type NodeDeleteReq struct {
	g.Meta `path:"/nodes/{id}" method:"delete" summary:"删除PVE节点" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
}

type NodeDeleteRes struct{}

// NodeViewReq 查看节点请求
type NodeViewReq struct {
	g.Meta `path:"/nodes/{id}" method:"get" summary:"查看PVE节点" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
}

type NodeViewRes struct {
	*NodeViewModel
}

type NodeViewModel struct {
	ID            uint64  `json:"id" dc:"节点ID"`
	Name          string  `json:"name" dc:"节点名称"`
	Host          string  `json:"host" dc:"节点地址"`
	Port          int     `json:"port" dc:"端口"`
	Username      string  `json:"username" dc:"用户名"`
	Password      string  `json:"password" dc:"密码"`
	TokenID       string  `json:"tokenId" dc:"API Token ID"`
	TokenSecret   string  `json:"tokenSecret" dc:"API Token Secret"`
	Status        int     `json:"status" dc:"状态：1=正常 2=异常"`
	CpuUsage      float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage   float64 `json:"memoryUsage" dc:"内存使用率"`
	DiskUsage     float64 `json:"diskUsage" dc:"磁盘使用率"`
	Uptime        int64   `json:"uptime" dc:"运行时间"`
	PveVersion    string  `json:"pveVersion" dc:"PVE版本"`
	KernelVersion string  `json:"kernelVersion" dc:"内核版本"`
	CreatedAt     string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string  `json:"updatedAt" dc:"更新时间"`
}

// NodeListReq 节点列表请求
type NodeListReq struct {
	g.Meta `path:"/nodes" method:"get" summary:"获取PVE节点列表" tags:"PVE管理"`
	form.PageReq
	Name     string `json:"name" dc:"节点名称"`
	Host     string `json:"host" dc:"节点地址"`
	Status   int    `json:"status" dc:"状态"`
	OrderBy  string `json:"orderBy" dc:"排序字段"`
	OrderDir string `json:"orderDir" dc:"排序方向"`
}

type NodeListRes struct {
	form.PageRes
	List []*NodeListModel `json:"list" dc:"节点列表"`
}

type NodeListModel struct {
	ID            uint64  `json:"id" dc:"节点ID"`
	Name          string  `json:"name" dc:"节点名称"`
	Host          string  `json:"host" dc:"节点地址"`
	Port          int     `json:"port" dc:"端口"`
	Status        int     `json:"status" dc:"状态：1=正常 2=异常"`
	CpuUsage      float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage   float64 `json:"memoryUsage" dc:"内存使用率"`
	DiskUsage     float64 `json:"diskUsage" dc:"磁盘使用率"`
	Uptime        int64   `json:"uptime" dc:"运行时间"`
	PveVersion    string  `json:"pveVersion" dc:"PVE版本"`
	KernelVersion string  `json:"kernelVersion" dc:"内核版本"`
	CreatedAt     string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt     string  `json:"updatedAt" dc:"更新时间"`
}

// NodeTestReq 测试节点连接请求
type NodeTestReq struct {
	g.Meta      `path:"/nodes/test" method:"post" summary:"测试PVE节点连接" tags:"PVE管理"`
	Host        string `json:"host" v:"required#节点地址不能为空" dc:"节点地址"`
	Port        int    `json:"port" v:"required#端口不能为空" dc:"端口"`
	Username    string `json:"username" dc:"用户名"`
	Password    string `json:"password" dc:"密码"`
	TokenID     string `json:"tokenId" dc:"API Token ID"`
	TokenSecret string `json:"tokenSecret" dc:"API Token Secret"`
}

type NodeTestRes struct {
	Success     bool   `json:"success" dc:"连接是否成功"`
	Message     string `json:"message" dc:"测试结果信息"`
	PveVersion  string `json:"pveVersion" dc:"PVE版本"`
	NodeName    string `json:"nodeName" dc:"节点名称"`
	ConnectTime int64  `json:"connectTime" dc:"连接耗时(毫秒)"`
}

// NodeSyncReq 同步节点状态请求
type NodeSyncReq struct {
	g.Meta `path:"/nodes/{id}/sync" method:"post" summary:"同步PVE节点状态" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
}

type NodeSyncRes struct {
	Success bool   `json:"success" dc:"同步是否成功"`
	Message string `json:"message" dc:"同步结果信息"`
}

// NodeMonitorReq 节点监控数据请求
type NodeMonitorReq struct {
	g.Meta `path:"/nodes/{id}/monitor" method:"get" summary:"获取PVE节点监控数据" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
	Period string `json:"period" v:"in:1h,24h,7d,30d#时间周期错误" dc:"时间周期：1h,24h,7d,30d"`
}

type NodeMonitorRes struct {
	CPUData    []*MonitorPoint `json:"cpuData" dc:"CPU使用率数据"`
	MemoryData []*MonitorPoint `json:"memoryData" dc:"内存使用率数据"`
	DiskData   []*MonitorPoint `json:"diskData" dc:"磁盘使用率数据"`
	NetworkData []*MonitorPoint `json:"networkData" dc:"网络流量数据"`
}

type MonitorPoint struct {
	Timestamp int64   `json:"timestamp" dc:"时间戳"`
	Value     float64 `json:"value" dc:"数值"`
}

// NodeServicesReq 获取节点服务列表请求
type NodeServicesReq struct {
	g.Meta `path:"/nodes/{id}/services" method:"get" summary:"获取PVE节点服务列表" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
}

type NodeServicesRes struct {
	Services []*ServiceModel `json:"services" dc:"服务列表"`
}

type ServiceModel struct {
	Name        string `json:"name" dc:"服务名称"`
	Status      string `json:"status" dc:"服务状态"`
	Description string `json:"description" dc:"服务描述"`
	Enabled     bool   `json:"enabled" dc:"是否启用"`
}

// NodeServiceControlReq 控制节点服务请求
type NodeServiceControlReq struct {
	g.Meta  `path:"/nodes/{id}/services/{service}/{action}" method:"post" summary:"控制PVE节点服务" tags:"PVE管理"`
	ID      uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
	Service string `json:"service" v:"required#服务名称不能为空" dc:"服务名称"`
	Action  string `json:"action" v:"required|in:start,stop,restart,reload,enable,disable#操作类型不能为空|操作类型无效" dc:"操作类型"`
}

type NodeServiceControlRes struct {
	TaskID  string `json:"taskId" dc:"任务ID"`
	Success bool   `json:"success" dc:"操作是否成功"`
	Message string `json:"message" dc:"操作结果信息"`
}

// NodeRebootReq 重启节点请求
type NodeRebootReq struct {
	g.Meta `path:"/nodes/{id}/reboot" method:"post" summary:"重启PVE节点" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
}

type NodeRebootRes struct {
	TaskID  string `json:"taskId" dc:"任务ID"`
	Success bool   `json:"success" dc:"操作是否成功"`
	Message string `json:"message" dc:"操作结果信息"`
}

// NodeShutdownReq 关闭节点请求
type NodeShutdownReq struct {
	g.Meta `path:"/nodes/{id}/shutdown" method:"post" summary:"关闭PVE节点" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#节点ID不能为空" dc:"节点ID"`
}

type NodeShutdownRes struct {
	TaskID  string `json:"taskId" dc:"任务ID"`
	Success bool   `json:"success" dc:"操作是否成功"`
	Message string `json:"message" dc:"操作结果信息"`
}