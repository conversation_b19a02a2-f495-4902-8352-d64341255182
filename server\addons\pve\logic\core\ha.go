// Package core
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

type sPveHA struct{}

func init() {
	service.RegisterHA(NewPveHA())
}

func NewPveHA() service.IHA {
	return &sPveHA{}
}

// GetResources 获取HA资源列表
func (s *sPveHA) GetResources(ctx context.Context, in *input.HAResourcesInp) (out *input.HAResourcesOut, err error) {
	// TODO: 实现HA资源获取逻辑
	return &input.HAResourcesOut{
		HAResourcesModel: &input.HAResourcesModel{
			List: []*input.HAResource{},
		},
	}, nil
}

// GetGroups 获取HA组列表
func (s *sPveHA) GetGroups(ctx context.Context, in *input.HAGroupsInp) (out *input.HAGroupsOut, err error) {
	// TODO: 实现HA组获取逻辑
	return &input.HAGroupsOut{
		HAGroupsModel: &input.HAGroupsModel{
			List: []*input.HAGroup{},
		},
	}, nil
}

// CreateGroup 创建HA组
func (s *sPveHA) CreateGroup(ctx context.Context, in *input.HACreateGroupInp) (out *input.HAGroupCreateOut, err error) {
	// TODO: 实现HA组创建逻辑
	return &input.HAGroupCreateOut{
		HACreateGroupModel: &input.HACreateGroupModel{
			Success: true,
		},
	}, nil
}

// EditGroup 编辑HA组
func (s *sPveHA) EditGroup(ctx context.Context, in *input.HAEditGroupInp) (out *input.HAEditGroupModel, err error) {
	// TODO: 实现HA组编辑逻辑
	return &input.HAEditGroupModel{
		Success: true,
	}, nil
}

// DeleteGroup 删除HA组
func (s *sPveHA) DeleteGroup(ctx context.Context, in *input.HADeleteGroupInp) (err error) {
	// TODO: 实现HA组删除逻辑
	return nil
}

// createPVEClient 创建PVE客户端
func (s *sPveHA) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	return pveclient.NewClient(config)
}