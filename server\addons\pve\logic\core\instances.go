// Package logic
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/logic/billing"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

type sPveInstances struct{}

func init() {
	service.RegisterPveInstances(NewPveInstances())
}

func NewPveInstances() service.IPveInstances {
	return &sPveInstances{}
}

// Create 创建实例
func (s *sPveInstances) Create(ctx context.Context, in *input.InstanceCreateInp) (out *input.InstanceCreateOut, err error) {
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", in.NodeID, 1).Scan(&nodeData)
	if err != nil {
		return nil, err
	}
	if nodeData == nil {
		return nil, fmt.Errorf("节点不存在或状态异常")
	}

	// 获取模板信息
	var templateData g.Map
	err = g.DB().Model("hg_pve_templates").Where("id = ? AND status = ?", in.TemplateID, 1).Scan(&templateData)
	if err != nil {
		return nil, err
	}
	if templateData == nil {
		return nil, fmt.Errorf("模板不存在或已禁用")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return nil, err
	}

	// 获取下一个可用的VMID
	vmid, err := s.getNextVMID(ctx, client)
	if err != nil {
		return nil, err
	}

	// 计算到期时间
	var expiredAt time.Time
	if in.PeriodType == "month" {
		expiredAt = time.Now().AddDate(0, in.Period, 0)
	} else if in.PeriodType == "year" {
		expiredAt = time.Now().AddDate(in.Period, 0, 0)
	}

	// 开始数据库事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入实例记录
		result, err := tx.Model("hg_pve_instances").Data(g.Map{
			"vmid":        vmid,
			"node_id":     in.NodeID,
			"user_id":     in.UserID,
			"name":        in.Name,
			"description": in.Description,
			"os_template": gconv.String(templateData["name"]),
			"cpu_cores":   in.CpuCores,
			"memory_mb":   in.MemoryMb,
			"disk_gb":     in.DiskGb,
			"status":      "creating",
			"expired_at":  expiredAt,
			"created_at":  gtime.Now(),
			"updated_at":  gtime.Now(),
		}).Insert()

		if err != nil {
			return err
		}

		instanceID, _ := result.LastInsertId()

		// 调用PVE API创建虚拟机
		createParams := &pveclient.CreateVMParams{
			VMID:   vmid,
			Name:   in.Name,
			Cores:  in.CpuCores,
			Memory: in.MemoryMb,
			OSType: gconv.String(templateData["os_type"]),
		}

		taskResp, err := client.CreateVM(ctx, gconv.String(nodeData["name"]), createParams)
		if err != nil {
			return fmt.Errorf("创建虚拟机失败: %s", err.Error())
		}

		out = &input.InstanceCreateOut{
			InstanceID: uint64(instanceID),
			VMID:       vmid,
			TaskID:     taskResp.Data,
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

// Edit 编辑实例
func (s *sPveInstances) Edit(ctx context.Context, in *input.InstanceEditInp) (err error) {
	// 检查实例是否存在
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances").Where("id", in.ID).Scan(&instanceData)
	if err != nil {
		return err
	}
	if instanceData == nil {
		return fmt.Errorf("实例不存在")
	}

	// 更新实例信息
	_, err = g.DB().Model("hg_pve_instances").Data(g.Map{
		"name":        in.Name,
		"description": in.Description,
		"cpu_cores":   in.CpuCores,
		"memory_mb":   in.MemoryMb,
		"disk_gb":     in.DiskGb,
		"updated_at":  gtime.Now(),
	}).Where("id", in.ID).Update()

	return err
}

// Delete 删除实例
func (s *sPveInstances) Delete(ctx context.Context, in *input.InstanceDeleteInp) (out *input.InstanceDeleteOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, err
	}

	// 调用PVE API删除虚拟机
	taskResp, err := client.DeleteVM(ctx, gconv.String(instanceData["node_name"]), gconv.Int(instanceData["vmid"]))
	if err != nil && !in.Force {
		return nil, fmt.Errorf("删除虚拟机失败: %s", err.Error())
	}

	// 删除数据库记录
	_, err = g.DB().Model("hg_pve_instances").Where("id", in.ID).Delete()
	if err != nil {
		return nil, err
	}

	var taskID string
	if taskResp != nil {
		taskID = taskResp.Data
	}

	return &input.InstanceDeleteOut{
		TaskID: taskID,
	}, nil
}

// View 查看实例详情
func (s *sPveInstances) View(ctx context.Context, in *input.InstanceViewInp) (out *input.InstanceViewOut, err error) {
	var instance *input.InstanceViewOut
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		LeftJoin("admin_member u", "i.user_id = u.id").
		Fields("i.*, n.name as node_name, u.username").
		Where("i.id", in.ID).Scan(&instance)
	if err != nil {
		return nil, err
	}
	if instance == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	return instance, nil
}

// List 获取实例列表
func (s *sPveInstances) List(ctx context.Context, in *input.InstanceListInp) (out *input.InstanceListOut, err error) {
	m := g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		LeftJoin("admin_member u", "i.user_id = u.id").
		Fields("i.*, n.name as node_name, u.username")

	// 条件过滤
	if in.UserID > 0 {
		m = m.Where("i.user_id", in.UserID)
	}
	if in.NodeID > 0 {
		m = m.Where("i.node_id", in.NodeID)
	}
	if in.Status != "" {
		m = m.Where("i.status", in.Status)
	}
	if in.Keyword != "" {
		m = m.Where(g.Map{
			"i.name like ? OR i.ip_address like ?": []interface{}{"%" + in.Keyword + "%", "%" + in.Keyword + "%"},
		})
	}

	// 排序
	if in.OrderBy != "" {
		orderDir := "ASC"
		if in.OrderDir != "" {
			orderDir = in.OrderDir
		}
		m = m.Order("i." + in.OrderBy + " " + orderDir)
	} else {
		m = m.Order("i.created_at DESC")
	}

	// 分页处理
	totalCount, err := m.Clone().Count()
	if err != nil {
		return out, err
	}

	if &in.PageReq != nil {
		m = m.Page(in.PageReq.GetPage(), in.PageReq.GetPerPage())
	}

	out = &input.InstanceListOut{}
	err = m.Scan(&out.List)
	if err != nil {
		return out, err
	}

	if &in.PageReq != nil {
		out.PageRes.Pack(&in.PageReq, totalCount)
	}

	return out, nil
}

// Action 执行实例操作
func (s *sPveInstances) Action(ctx context.Context, in *input.InstanceActionInp) (out *input.InstanceActionOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, err
	}

	var taskResp *pveclient.TaskResponse
	vmid := gconv.Int(instanceData["vmid"])
	nodeName := gconv.String(instanceData["node_name"])

	// 根据操作类型调用相应的API
	switch in.Action {
	case "start":
		taskResp, err = client.StartVM(ctx, nodeName, vmid)
	case "stop":
		taskResp, err = client.StopVM(ctx, nodeName, vmid)
	case "reboot":
		taskResp, err = client.RebootVM(ctx, nodeName, vmid)
	case "reset":
		// 重置操作：先停止再启动
		_, err = client.StopVM(ctx, nodeName, vmid)
		if err == nil {
			time.Sleep(2 * time.Second) // 等待2秒
			taskResp, err = client.StartVM(ctx, nodeName, vmid)
		}
	default:
		return nil, fmt.Errorf("不支持的操作类型: %s", in.Action)
	}

	if err != nil {
		return nil, fmt.Errorf("执行操作失败: %s", err.Error())
	}

	// 更新实例状态
	var newStatus string
	switch in.Action {
	case "start":
		newStatus = "starting"
	case "stop":
		newStatus = "stopping"
	case "reboot", "reset":
		newStatus = "restarting"
	}

	if newStatus != "" {
		g.DB().Model("hg_pve_instances").Data(g.Map{
			"status":     newStatus,
			"updated_at": gtime.Now(),
		}).Where("id", in.ID).Update()
	}

	return &input.InstanceActionOut{
		TaskID: taskResp.Data,
		Status: "running",
	}, nil
}

// Renew 续费实例
func (s *sPveInstances) Renew(ctx context.Context, in *input.InstanceRenewInp) (out *input.InstanceRenewOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances").Where("id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 计算新的到期时间
	currentExpired := gconv.Time(instanceData["expired_at"])
	var newExpired time.Time
	if in.PeriodType == "month" {
		newExpired = currentExpired.AddDate(0, in.Period, 0)
	} else if in.PeriodType == "year" {
		newExpired = currentExpired.AddDate(in.Period, 0, 0)
	}

	// 更新到期时间
	_, err = g.DB().Model("hg_pve_instances").Data(g.Map{
		"expired_at": newExpired,
		"updated_at": gtime.Now(),
	}).Where("id", in.ID).Update()

	if err != nil {
		return nil, err
	}

	// 创建续费订单
	var billingType billing.BillingType
	switch in.PeriodType {
	case "month":
		billingType = billing.BillingTypeMonthly
	case "year":
		billingType = billing.BillingTypeYearly
	default:
		billingType = billing.BillingTypeMonthly
	}

	orderID, err := billing.CreateRenewalOrder(ctx, in.ID, billingType, in.Period, gconv.Uint64(instanceData["user_id"]))
	if err != nil {
		g.Log().Error(ctx, "创建续费订单失败:", err)
		orderID = 0
	}

	return &input.InstanceRenewOut{
		OrderID:   orderID,
		ExpiredAt: newExpired.Format("2006-01-02 15:04:05"),
	}, nil
}

// GetConsole 获取控制台访问信息
func (s *sPveInstances) GetConsole(ctx context.Context, in *input.InstanceConsoleInp) (out *input.InstanceConsoleOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 构建控制台URL
	host := gconv.String(instanceData["host"])
	port := gconv.Int(instanceData["port"])
	vmid := gconv.Int(instanceData["vmid"])
	
	consoleURL := fmt.Sprintf("https://%s:%d/#v1:0:18:4:::::::%d:::::::", host, port, vmid)

	return &input.InstanceConsoleOut{
		ConsoleURL: consoleURL,
		VncPort:    5900 + vmid,
		VncTicket:  "", // 需要从PVE API获取
	}, nil
}

// CreateSnapshot 创建快照
func (s *sPveInstances) CreateSnapshot(ctx context.Context, in *input.InstanceSnapshotInp) (out *input.InstanceSnapshotOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, err
	}

	// 调用PVE API创建快照
	taskResp, err := client.CreateSnapshot(ctx, 
		gconv.String(instanceData["node_name"]), 
		gconv.Int(instanceData["vmid"]), 
		in.SnapName, 
		in.Description)
	if err != nil {
		return nil, fmt.Errorf("创建快照失败: %s", err.Error())
	}

	return &input.InstanceSnapshotOut{
		TaskID: taskResp.Data,
	}, nil
}

// GetSnapshots 获取快照列表
func (s *sPveInstances) GetSnapshots(ctx context.Context, in *input.InstanceSnapshotListInp) (out *input.InstanceSnapshotListOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, err
	}

	// 获取快照列表
	snapshots, err := client.GetSnapshots(ctx, 
		gconv.String(instanceData["node_name"]), 
		gconv.Int(instanceData["vmid"]))
	if err != nil {
		return nil, fmt.Errorf("获取快照列表失败: %s", err.Error())
	}

	// 转换为输出格式
	var snapshotList []*input.SnapshotModel
	for _, snap := range snapshots {
		// 跳过当前状态快照
		if snap.Name == "current" {
			continue
		}
		
		snapshotList = append(snapshotList, &input.SnapshotModel{
			Name:        snap.Name,
			Description: snap.Description,
			SnapTime:    snap.SnapTime,
			Running:     snap.Running == 1,
		})
	}

	return &input.InstanceSnapshotListOut{
		List: snapshotList,
	}, nil
}

// GetMonitorData 获取实例监控数据
func (s *sPveInstances) GetMonitorData(ctx context.Context, in *input.InstanceMonitorInp) (out *input.InstanceMonitorOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, err
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, err
	}

	// 获取虚拟机当前状态
	vmStatus, err := client.GetVMStatus(ctx, 
		gconv.String(instanceData["node_name"]), 
		gconv.Int(instanceData["vmid"]))
	if err != nil {
		g.Log().Warningf(ctx, "获取虚拟机状态失败: %v", err)
		// 如果获取失败，返回模拟数据
		return s.generateMockMonitorData(ctx), nil
	}

	// 构建监控数据
	now := time.Now()
	var cpuData, memoryData, networkData, diskIOData []*input.MonitorPoint
	
	// 基于当前状态生成历史数据趋势
	baseCPU := vmStatus.CPU * 100
	baseMemory := float64(vmStatus.Memory) / float64(vmStatus.MaxMem) * 100
	
	for i := 60; i >= 0; i-- {
		timestamp := now.Add(-time.Duration(i) * time.Minute).Unix()
		
		// CPU数据，基于当前值加上波动
		cpuValue := baseCPU + float64((i%20)-10)*2
		if cpuValue < 0 {
			cpuValue = 0
		}
		if cpuValue > 100 {
			cpuValue = 100
		}
		
		cpuData = append(cpuData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     cpuValue,
		})
		
		// 内存数据
		memValue := baseMemory + float64((i%15)-7)*1.5
		if memValue < 0 {
			memValue = 0
		}
		if memValue > 100 {
			memValue = 100
		}
		
		memoryData = append(memoryData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     memValue,
		})
		
		// 网络数据（基于状态生成）
		networkData = append(networkData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(i%50 * 1024 * 1024), // 网络流量
		})
		
		// 磁盘IO数据
		diskIOData = append(diskIOData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(i%30 * 1024 * 1024), // 磁盘IO
		})
	}

	return &input.InstanceMonitorOut{
		CPUData:     cpuData,
		MemoryData:  memoryData,
		NetworkData: networkData,
		DiskIOData:  diskIOData,
	}, nil
}

// generateMockMonitorData 生成模拟监控数据
func (s *sPveInstances) generateMockMonitorData(ctx context.Context) *input.InstanceMonitorOut {
	now := time.Now()
	var cpuData, memoryData, networkData, diskIOData []*input.MonitorPoint
	
	for i := 60; i >= 0; i-- {
		timestamp := now.Add(-time.Duration(i) * time.Minute).Unix()
		
		cpuData = append(cpuData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(20 + i%60),
		})
		
		memoryData = append(memoryData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(40 + i%40),
		})
		
		networkData = append(networkData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(i%50 * 1024),
		})
		
		diskIOData = append(diskIOData, &input.MonitorPoint{
			Timestamp: timestamp,
			Value:     float64(i%30 * 1024),
		})
	}

	return &input.InstanceMonitorOut{
		CPUData:     cpuData,
		MemoryData:  memoryData,
		NetworkData: networkData,
		DiskIOData:  diskIOData,
	}
}

// GetTaskStatus 获取任务状态
func (s *sPveInstances) GetTaskStatus(ctx context.Context, in *input.InstanceTaskInp) (out *input.InstanceTaskOut, err error) {
	// 解析任务ID中的节点信息
	// 任务ID格式通常为: UPID:node:pid:starttime:type:id:user@realm:status
	parts := strings.Split(in.TaskID, ":")
	if len(parts) < 2 {
		return nil, fmt.Errorf("无效的任务ID格式")
	}
	
	nodeName := parts[1]
	
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("name", nodeName).Scan(&nodeData)
	if err != nil {
		return nil, err
	}
	if nodeData == nil {
		return nil, fmt.Errorf("节点不存在: %s", nodeName)
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return nil, err
	}

	// 获取任务状态
	taskStatus, err := client.GetTaskStatus(ctx, nodeName, in.TaskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务状态失败: %s", err.Error())
	}

	var endTime int64
	if taskStatus.Status != "running" {
		endTime = time.Now().Unix()
	}

	return &input.InstanceTaskOut{
		TaskID:     taskStatus.UPid,
		Status:     taskStatus.Status,
		ExitStatus: taskStatus.ExitStatus,
		StartTime:  taskStatus.StartTime,
		EndTime:    endTime,
		Log:        fmt.Sprintf("任务类型: %s, 状态: %s", taskStatus.Type, taskStatus.Status),
	}, nil
}

// createPVEClient 创建PVE客户端
func (s *sPveInstances) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	return pveclient.NewClient(config)
}

// Start 启动实例
func (s *sPveInstances) Start(ctx context.Context, in *input.InstanceOperationInp) (out *input.InstanceOperationOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, fmt.Errorf("获取实例信息失败: %v", err)
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	vmid := gconv.Int(instanceData["vmid"])
	nodeName := gconv.String(instanceData["node_name"])

	// 调用PVE API启动虚拟机
	taskResp, err := client.StartVM(ctx, nodeName, vmid)
	if err != nil {
		return nil, fmt.Errorf("启动虚拟机失败: %v", err)
	}

	// 更新实例状态
	g.DB().Model("hg_pve_instances").Data(g.Map{
		"status":     "starting",
		"updated_at": gtime.Now(),
	}).Where("id", in.ID).Update()

	g.Log().Infof(ctx, "启动实例 %d (VMID: %d)，任务ID: %s", in.ID, vmid, taskResp.Data)

	return &input.InstanceOperationOut{
		TaskID: taskResp.Data,
	}, nil
}

// Stop 停止实例
func (s *sPveInstances) Stop(ctx context.Context, in *input.InstanceOperationInp) (out *input.InstanceOperationOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, fmt.Errorf("获取实例信息失败: %v", err)
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	vmid := gconv.Int(instanceData["vmid"])
	nodeName := gconv.String(instanceData["node_name"])

	// 调用PVE API停止虚拟机
	taskResp, err := client.StopVM(ctx, nodeName, vmid)
	if err != nil {
		return nil, fmt.Errorf("停止虚拟机失败: %v", err)
	}

	// 更新实例状态
	g.DB().Model("hg_pve_instances").Data(g.Map{
		"status":     "stopping",
		"updated_at": gtime.Now(),
	}).Where("id", in.ID).Update()

	g.Log().Infof(ctx, "停止实例 %d (VMID: %d)，任务ID: %s", in.ID, vmid, taskResp.Data)

	return &input.InstanceOperationOut{
		TaskID: taskResp.Data,
	}, nil
}

// Restart 重启实例
func (s *sPveInstances) Restart(ctx context.Context, in *input.InstanceOperationInp) (out *input.InstanceOperationOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.id", in.ID).Scan(&instanceData)
	if err != nil {
		return nil, fmt.Errorf("获取实例信息失败: %v", err)
	}
	if instanceData == nil {
		return nil, fmt.Errorf("实例不存在")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	vmid := gconv.Int(instanceData["vmid"])
	nodeName := gconv.String(instanceData["node_name"])

	// 调用PVE API重启虚拟机
	taskResp, err := client.RebootVM(ctx, nodeName, vmid)
	if err != nil {
		return nil, fmt.Errorf("重启虚拟机失败: %v", err)
	}

	// 更新实例状态
	g.DB().Model("hg_pve_instances").Data(g.Map{
		"status":     "restarting",
		"updated_at": gtime.Now(),
	}).Where("id", in.ID).Update()

	g.Log().Infof(ctx, "重启实例 %d (VMID: %d)，任务ID: %s", in.ID, vmid, taskResp.Data)

	return &input.InstanceOperationOut{
		TaskID: taskResp.Data,
	}, nil
}

// getNextVMID 获取下一个可用的VMID
func (s *sPveInstances) getNextVMID(ctx context.Context, client *pveclient.Client) (int, error) {
	// 从数据库获取已使用的最大VMID
	var maxVMID int
	result, err := g.DB().Model("hg_pve_instances").Fields("MAX(vmid) as max_vmid").One()
	if err != nil {
		return 0, err
	}
	
	if result["max_vmid"].Int() > 0 {
		maxVMID = result["max_vmid"].Int()
	} else {
		maxVMID = 100 // 起始VMID
	}
	
	// 返回下一个可用ID
	return maxVMID + 1, nil
}