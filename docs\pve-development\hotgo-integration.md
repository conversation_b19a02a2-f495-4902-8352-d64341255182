# HotGo-PVE 插件化开发指南

> 基于 HotGo v2 框架的 PVE 云平台插件开发完整指南

## 概述

本文档详细介绍如何在 HotGo 框架基础上开发 PVE (Proxmox VE) 云平台插件，包括插件架构设计、开发环境搭建、模块开发和部署发布等完整流程。

## 🏗️ 插件架构设计

### 插件化优势

**为什么选择插件模式？**
- **独立性**: PVE 功能作为独立插件，不影响主系统稳定性
- **可扩展**: 支持多种虚拟化平台（PVE、VMware、KVM等）
- **模块化**: 功能模块清晰分离，便于开发和维护
- **可插拔**: 可根据需求启用/禁用特定功能
- **团队协作**: 支持多人并行开发，互不干扰
- **版本管理**: 插件独立版本控制和升级

### 插件架构图

```mermaid
graph TD
    A[HotGo主系统] --> B[插件管理器]
    B --> C[PVE插件]
    B --> D[其他虚拟化插件]
    
    C --> E[PVE管理模块]
    C --> F[计费模块]
    C --> G[监控模块]
    C --> H[用户门户模块]
    
    E --> I[节点管理]
    E --> J[实例管理]
    E --> K[模板管理]
    
    F --> L[订单系统]
    F --> M[计费引擎]
    F --> N[支付集成]
    
    G --> O[性能监控]
    G --> P[告警系统]
    G --> Q[日志分析]
```

### 技术架构层次

```yaml
插件技术栈:
  后端架构:
    框架: HotGo v2 插件系统
    语言: Go 1.19+
    ORM: GORM (通过GoFrame)
    消息队列: Redis/Kafka/RocketMQ
    缓存: Redis/Memory
    数据库: MySQL 8.0+
    
  前端架构:
    框架: Vue 3 + TypeScript
    UI库: Naive UI
    状态管理: Pinia
    构建工具: Vite
    
  虚拟化集成:
    主要平台: Proxmox VE 8.0+
    监控: InfluxDB + Grafana
    网络: SDN集成
```

### 插件模块划分

```
PVE插件模块组成:
├── 核心管理模块 (pve-core)
│   ├── 节点管理
│   ├── 虚拟机管理
│   ├── 存储管理
│   └── 网络管理
├── 计费模块 (pve-billing)
│   ├── 资源计量
│   ├── 价格策略
│   ├── 订单处理
│   └── 发票管理
├── 监控模块 (pve-monitoring)
│   ├── 性能监控
│   ├── 资源统计
│   ├── 告警通知
│   └── 报表生成
├── 用户门户 (pve-portal)
│   ├── 自助服务
│   ├── 资源申请
│   ├── 使用统计
│   └── 工单系统
└── 运维模块 (pve-ops)
    ├── 自动化运维
    ├── 备份恢复
    ├── 安全扫描
    └── 容量规划
```

## 🛠️ 环境要求

### 基础环境
- **Node.js** >= v18.0.0
- **Go** >= v1.19
- **MySQL** >= 8.0
- **Redis** >= 6.0
- **Git** >= 2.30

### PVE环境要求
- **Proxmox VE** >= 7.0
- PVE节点需要开启API访问
- 具备管理员权限的API用户

### 开发工具推荐
- **后端IDE**: GoLand / VS Code + Go插件
- **前端IDE**: VS Code + Vetur/Volar
- **数据库工具**: Navicat / DataGrip
- **API测试**: Postman / Apifox
- **Git工具**: SourceTree / GitKraken

## 📁 插件目录结构

### HotGo插件标准结构

```
server/addons/pve/                    # PVE插件根目录
├── main.go                           # 插件入口文件
├── README.md                         # 插件说明文档
├── config.yaml                       # 插件配置文件
├── install.sql                       # 插件安装SQL
├── uninstall.sql                     # 插件卸载SQL
├── upgrade/                          # 插件升级脚本
│   ├── v1.0.1.sql
│   └── v1.1.0.sql
├── api/                              # API接口定义
│   ├── admin/                        # 管理后台API
│   │   ├── nodes/
│   │   ├── instances/
│   │   ├── templates/
│   │   ├── monitoring/
│   │   └── billing/
│   ├── user/                         # 用户端API
│   └── webhook/                      # 回调接口
├── controller/                       # 控制器层
│   ├── admin/
│   ├── user/
│   └── webhook/
├── logic/                            # 业务逻辑层
│   ├── core/                         # 核心管理逻辑
│   ├── billing/                      # 计费逻辑
│   ├── monitoring/                   # 监控逻辑
│   └── portal/                       # 用户门户逻辑
├── model/                            # 数据模型
│   ├── entity/                       # 数据实体
│   ├── input/                        # 输入模型
│   ├── output/                       # 输出模型
│   └── config/                       # 配置模型
├── service/                          # 服务接口
│   ├── core.go
│   ├── billing.go
│   ├── monitoring.go
│   └── portal.go
├── library/                          # 核心库
│   ├── pveclient/                    # PVE客户端
│   ├── provider/                     # 虚拟化提供商抽象
│   ├── billing/                      # 计费引擎
│   └── monitor/                      # 监控组件
├── router/                           # 路由注册
├── crons/                            # 定时任务
├── queues/                           # 消息队列
├── global/                           # 全局变量和初始化
├── resource/                         # 静态资源
└── hooks/                            # 生命周期钩子
```

### Web前端插件结构

```
web/src/views/addons/pve/             # PVE插件前端
├── index.vue                         # 插件首页
├── components/                       # 插件组件
├── views/                            # 页面视图
│   ├── dashboard/                    # 仪表板
│   ├── nodes/                        # 节点管理
│   ├── instances/                    # 实例管理
│   ├── templates/                    # 模板管理
│   ├── monitoring/                   # 监控面板
│   ├── billing/                      # 计费管理
│   └── settings/                     # 设置页面
├── api/                              # API接口调用
├── store/                            # 状态管理
├── types/                            # TypeScript类型
├── utils/                            # 工具函数
└── hooks/                            # Vue组合函数
```

## 🚀 开发环境搭建

### 1. 克隆项目

```bash
# 克隆基础HotGo项目
git clone https://github.com/bufanyun/hotgo.git hotgo-pve
cd hotgo-pve

# 切换到开发分支
git checkout -b feature/pve-integration
```

### 2. 后端环境配置

#### 2.1 安装Go依赖

```bash
cd server

# 设置Go代理
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn

# 下载依赖
go mod tidy
```

#### 2.2 数据库配置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE hotgo_pve CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

修改 `server/manifest/config/config.yaml`:

```yaml
# 数据库配置
database:
  default:
    link:  "mysql:root:password@tcp(127.0.0.1:3306)/hotgo_pve"
    debug: true

# Redis配置
redis:
  default:
    address: "127.0.0.1:6379"
    db: 0
```

#### 2.3 PVE配置

在配置文件中添加PVE相关配置：

```yaml
# PVE插件配置
addons:
  pve:
    enable: true
    nodes:
      - name: "pve-node1"
        host: "*************"
        port: 8006
        username: "root@pam"
        password: "your-password"
        # 或使用API Token
        token_id: "root@pam!hotgo"
        token_secret: "your-token-secret"
    billing:
      currency: "CNY"
      precision: 2
    monitoring:
      interval: 60
      retention: "7d"
```

### 3. 前端环境配置

```bash
cd web

# 安装Node.js依赖
npm install
# 或使用yarn
yarn install

# 安装开发依赖
npm install --save-dev @types/node typescript
```

### 4. 启动开发环境

```bash
# 后端启动
cd server
go run main.go

# 前端启动（新终端）
cd web
npm run dev
```

## 📦 PVE插件开发

### 1. 创建插件入口文件

创建 `server/addons/pve/main.go`：

```go
package pve

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    "hotgo/addons/pve/global"
    "hotgo/addons/pve/router"
    "hotgo/internal/service"
)

// Addon PVE插件定义
type Addon struct {
    Name        string
    Version     string
    Description string
    Install     func(ctx context.Context) error
    Uninstall   func(ctx context.Context) error
}

// GetAddon 获取插件信息
func GetAddon() *Addon {
    return &Addon{
        Name:        "pve",
        Version:     "1.0.0",
        Description: "Proxmox VE 云平台管理插件",
        Install:     Install,
        Uninstall:   Uninstall,
    }
}

// Install 插件安装
func Install(ctx context.Context) error {
    g.Log().Info(ctx, "正在安装PVE插件...")
    
    // 执行数据库迁移
    if err := global.InstallTables(ctx); err != nil {
        return err
    }
    
    // 初始化默认数据
    if err := global.InitDefaultData(ctx); err != nil {
        return err
    }
    
    g.Log().Info(ctx, "PVE插件安装完成")
    return nil
}

// Uninstall 插件卸载
func Uninstall(ctx context.Context) error {
    g.Log().Info(ctx, "正在卸载PVE插件...")
    
    // 清理数据表
    if err := global.UninstallTables(ctx); err != nil {
        return err
    }
    
    g.Log().Info(ctx, "PVE插件卸载完成")
    return nil
}

// Boot 插件启动
func Boot(ctx context.Context) {
    g.Log().Info(ctx, "PVE插件启动中...")
    
    // 初始化全局配置
    global.Init(ctx)
    
    // 注册路由
    router.RegisterAdminRoutes()
    router.RegisterUserRoutes()
    
    // 启动定时任务
    service.Cron().Add(ctx, "pve_monitor", "*/1 * * * *", func() {
        global.StartMonitoringTasks(ctx)
    })
    
    g.Log().Info(ctx, "PVE插件启动完成")
}
```

### 2. 数据库表结构设计

创建 `server/addons/pve/install.sql`：

```sql
-- PVE节点表
CREATE TABLE `hg_pve_nodes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '节点名称',
  `host` varchar(255) NOT NULL COMMENT '节点地址',
  `port` int NOT NULL DEFAULT 8006 COMMENT '端口',
  `username` varchar(100) DEFAULT NULL COMMENT '用户名',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `token_id` varchar(100) DEFAULT NULL COMMENT 'API Token ID',
  `token_secret` varchar(255) DEFAULT NULL COMMENT 'API Token Secret',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1=正常 2=异常',
  `cpu_usage` decimal(5,2) DEFAULT 0.00 COMMENT 'CPU使用率',
  `memory_usage` decimal(5,2) DEFAULT 0.00 COMMENT '内存使用率',
  `disk_usage` decimal(5,2) DEFAULT 0.00 COMMENT '磁盘使用率',
  `uptime` bigint DEFAULT 0 COMMENT '运行时间',
  `pve_version` varchar(50) DEFAULT NULL COMMENT 'PVE版本',
  `kernel_version` varchar(100) DEFAULT NULL COMMENT '内核版本',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`)
) COMMENT='PVE节点管理表';

-- PVE虚拟机实例表
CREATE TABLE `hg_pve_instances` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vmid` int NOT NULL COMMENT '虚拟机ID',
  `node_id` bigint unsigned NOT NULL COMMENT '节点ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '实例名称',
  `description` text COMMENT '描述',
  `os_template` varchar(100) DEFAULT NULL COMMENT '操作系统模板',
  `cpu_cores` int NOT NULL DEFAULT 1 COMMENT 'CPU核心数',
  `memory_mb` int NOT NULL DEFAULT 512 COMMENT '内存大小(MB)',
  `disk_gb` int NOT NULL DEFAULT 10 COMMENT '磁盘大小(GB)',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `mac_address` varchar(17) DEFAULT NULL COMMENT 'MAC地址',
  `status` varchar(20) NOT NULL DEFAULT 'stopped' COMMENT '状态',
  `cpu_usage` decimal(5,2) DEFAULT 0.00 COMMENT 'CPU使用率',
  `memory_usage` decimal(5,2) DEFAULT 0.00 COMMENT '内存使用率',
  `network_in` bigint DEFAULT 0 COMMENT '网络入流量',
  `network_out` bigint DEFAULT 0 COMMENT '网络出流量',
  `disk_read` bigint DEFAULT 0 COMMENT '磁盘读取',
  `disk_write` bigint DEFAULT 0 COMMENT '磁盘写入',
  `expired_at` timestamp NULL COMMENT '到期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vmid_node` (`vmid`, `node_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expired` (`expired_at`)
) COMMENT='PVE虚拟机实例表';

-- 更多表结构...
```

### 3. API接口开发

创建 `server/addons/pve/api/admin/instances.go`：

```go
package admin

import (
    "github.com/gogf/gf/v2/frame/g"
    "hotgo/internal/model/input/form"
)

// InstanceCreateReq 创建实例请求
type InstanceCreateReq struct {
    g.Meta      `path:"/instances" method:"post" summary:"创建虚拟机实例" tags:"PVE管理"`
    NodeID      uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"PVE节点ID"`
    Name        string `json:"name" v:"required|length:1,100#实例名称不能为空|名称长度为1-100个字符" dc:"实例名称"`
    Description string `json:"description" dc:"实例描述"`
    TemplateID  uint64 `json:"templateId" v:"required#模板ID不能为空" dc:"系统模板ID"`
    ProductID   uint64 `json:"productId" v:"required#产品ID不能为空" dc:"产品配置ID"`
    Period      int    `json:"period" v:"required|min:1#购买时长不能为空|购买时长最少1个周期" dc:"购买时长"`
    PeriodType  string `json:"periodType" v:"required|in:month,year#计费周期不能为空|计费周期只能是month或year" dc:"计费周期"`
    AutoRenew   bool   `json:"autoRenew" dc:"是否自动续费"`
}

type InstanceCreateRes struct {
    InstanceID uint64 `json:"instanceId" dc:"实例ID"`
    VMID       int    `json:"vmid" dc:"PVE虚拟机ID"`
    OrderID    uint64 `json:"orderId" dc:"订单ID"`
}

// InstanceListReq 实例列表请求
type InstanceListReq struct {
    g.Meta `path:"/instances" method:"get" summary:"获取虚拟机实例列表" tags:"PVE管理"`
    form.PageReq
    UserID   uint64 `json:"userId" dc:"用户ID"`
    NodeID   uint64 `json:"nodeId" dc:"节点ID"`
    Status   string `json:"status" dc:"实例状态"`
    Keyword  string `json:"keyword" dc:"搜索关键词"`
    OrderBy  string `json:"orderBy" dc:"排序字段"`
    OrderDir string `json:"orderDir" dc:"排序方向"`
}

type InstanceListRes struct {
    form.PageRes
    List []*InstanceListModel `json:"list" dc:"实例列表"`
}

type InstanceListModel struct {
    ID          uint64 `json:"id" dc:"实例ID"`
    VMID        int    `json:"vmid" dc:"PVE虚拟机ID"`
    NodeID      uint64 `json:"nodeId" dc:"节点ID"`
    NodeName    string `json:"nodeName" dc:"节点名称"`
    UserID      uint64 `json:"userId" dc:"用户ID"`
    Username    string `json:"username" dc:"用户名"`
    Name        string `json:"name" dc:"实例名称"`
    Description string `json:"description" dc:"实例描述"`
    OSTemplate  string `json:"osTemplate" dc:"操作系统模板"`
    CPUCores    int    `json:"cpuCores" dc:"CPU核心数"`
    MemoryMB    int    `json:"memoryMb" dc:"内存大小(MB)"`
    DiskGB      int    `json:"diskGb" dc:"磁盘大小(GB)"`
    Status      string `json:"status" dc:"实例状态"`
    IPAddress   string `json:"ipAddress" dc:"IP地址"`
    ExpiredAt   string `json:"expiredAt" dc:"到期时间"`
    CreatedAt   string `json:"createdAt" dc:"创建时间"`
    UpdatedAt   string `json:"updatedAt" dc:"更新时间"`
}

// InstanceActionReq 实例操作请求
type InstanceActionReq struct {
    g.Meta `path:"/instances/{id}/action" method:"post" summary:"执行虚拟机操作" tags:"PVE管理"`
    ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
    Action string `json:"action" v:"required|in:start,stop,reboot,reset,delete#操作类型不能为空|操作类型无效" dc:"操作类型"`
    Force  bool   `json:"force" dc:"是否强制执行"`
}

type InstanceActionRes struct {
    TaskID string `json:"taskId" dc:"任务ID"`
    Status string `json:"status" dc:"执行状态"`
}
```

### 4. 控制器实现

创建 `server/addons/pve/controller/admin/instances.go`：

```go
package admin

import (
    "context"
    "hotgo/addons/pve/api/admin"
    "hotgo/addons/pve/service"
)

var Instances = cInstances{}

type cInstances struct{}

// Create 创建虚拟机实例
func (c *cInstances) Create(ctx context.Context, req *admin.InstanceCreateReq) (res *admin.InstanceCreateRes, err error) {
    result, err := service.Instances().Create(ctx, &model.InstanceCreateInput{
        NodeID:      req.NodeID,
        Name:        req.Name,
        Description: req.Description,
        TemplateID:  req.TemplateID,
        ProductID:   req.ProductID,
        Period:      req.Period,
        PeriodType:  req.PeriodType,
        AutoRenew:   req.AutoRenew,
    })
    
    if err != nil {
        return nil, err
    }
    
    return &admin.InstanceCreateRes{
        InstanceID: result.InstanceID,
        VMID:       result.VMID,
        OrderID:    result.OrderID,
    }, nil
}

// List 获取实例列表
func (c *cInstances) List(ctx context.Context, req *admin.InstanceListReq) (res *admin.InstanceListRes, err error) {
    result, err := service.Instances().List(ctx, &model.InstanceListInput{
        Page:     req.Page,
        PageSize: req.PageSize,
        UserID:   req.UserID,
        NodeID:   req.NodeID,
        Status:   req.Status,
        Keyword:  req.Keyword,
        OrderBy:  req.OrderBy,
        OrderDir: req.OrderDir,
    })
    
    if err != nil {
        return nil, err
    }
    
    return &admin.InstanceListRes{
        PageRes: form.PageRes{
            Page:     result.Page,
            PageSize: result.PageSize,
            Total:    result.Total,
        },
        List: result.List,
    }, nil
}

// Action 执行虚拟机操作
func (c *cInstances) Action(ctx context.Context, req *admin.InstanceActionReq) (res *admin.InstanceActionRes, err error) {
    result, err := service.Instances().Action(ctx, &model.InstanceActionInput{
        ID:     req.ID,
        Action: req.Action,
        Force:  req.Force,
    })
    
    if err != nil {
        return nil, err
    }
    
    return &admin.InstanceActionRes{
        TaskID: result.TaskID,
        Status: result.Status,
    }, nil
}
```

## 🎨 前端开发指南

### 1. 创建Vue组件

创建 `web/src/views/addons/pve/instances/index.vue`：

```vue
<template>
  <div class="pve-instances">
    <n-card title="虚拟机实例管理" :bordered="false">
      <!-- 操作栏 -->
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="handleCreate">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            创建实例
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>
      
      <!-- 筛选条件 -->
      <n-form inline :model="queryForm" class="mb-4">
        <n-form-item label="节点">
          <n-select
            v-model:value="queryForm.nodeId"
            placeholder="选择节点"
            :options="nodeOptions"
            clearable
            style="width: 200px"
          />
        </n-form-item>
        <n-form-item label="状态">
          <n-select
            v-model:value="queryForm.status"
            placeholder="选择状态"
            :options="statusOptions"
            clearable
            style="width: 150px"
          />
        </n-form-item>
        <n-form-item label="关键词">
          <n-input
            v-model:value="queryForm.keyword"
            placeholder="实例名称或IP地址"
            clearable
            style="width: 200px"
          />
        </n-form-item>
        <n-form-item>
          <n-button @click="handleSearch">搜索</n-button>
          <n-button @click="handleReset" class="ml-2">重置</n-button>
        </n-form-item>
      </n-form>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        size="small"
      />
    </n-card>

    <!-- 创建/编辑对话框 -->
    <CreateModal
      v-model:show="createModal.show"
      :data="createModal.data"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { NButton, NTag, NPopconfirm, useMessage } from 'naive-ui'
import { AddOutline, RefreshOutline } from '@vicons/ionicons5'
import { useInstanceStore } from '../store/instances'
import { useNodeStore } from '../store/nodes'
import CreateModal from './components/CreateModal.vue'

// 状态管理
const instanceStore = useInstanceStore()
const nodeStore = useNodeStore()
const message = useMessage()

// 响应式数据
const loading = ref(false)
const queryForm = reactive({
  nodeId: null,
  status: null,
  keyword: ''
})

const createModal = reactive({
  show: false,
  data: null
})

// 计算属性
const tableData = computed(() => instanceStore.list)
const nodeOptions = computed(() => 
  nodeStore.list.map(node => ({
    label: node.name,
    value: node.id
  }))
)

const statusOptions = [
  { label: '运行中', value: 'running' },
  { label: '已停止', value: 'stopped' },
  { label: '暂停', value: 'paused' }
]

// 表格列配置
const columns = [
  { title: 'VMID', key: 'vmid', width: 80 },
  { title: '实例名称', key: 'name' },
  { title: '节点', key: 'nodeName', width: 120 },
  { title: '用户', key: 'username', width: 100 },
  { 
    title: '配置', 
    key: 'config',
    render: (row) => `${row.cpuCores}C/${row.memoryMb}MB/${row.diskGb}GB`
  },
  { title: 'IP地址', key: 'ipAddress', width: 120 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        running: { type: 'success', text: '运行中' },
        stopped: { type: 'default', text: '已停止' },
        paused: { type: 'warning', text: '暂停' }
      }
      const status = statusMap[row.status] || { type: 'error', text: '未知' }
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  { title: '到期时间', key: 'expiredAt', width: 150 },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => [
      h(
        NButton,
        {
          size: 'small',
          type: row.status === 'running' ? 'warning' : 'primary',
          onClick: () => handleAction(row, row.status === 'running' ? 'stop' : 'start')
        },
        { default: () => row.status === 'running' ? '停止' : '启动' }
      ),
      h(
        NPopconfirm,
        {
          onPositiveClick: () => handleAction(row, 'delete')
        },
        {
          trigger: () => h(
            NButton,
            { size: 'small', type: 'error', class: 'ml-2' },
            { default: () => '删除' }
          ),
          default: () => '确定删除该实例吗？'
        }
      )
    ]
  }
]

// 分页配置
const pagination = computed(() => ({
  page: instanceStore.pagination.page,
  pageSize: instanceStore.pagination.pageSize,
  itemCount: instanceStore.pagination.total,
  onUpdatePage: (page) => {
    instanceStore.updatePagination({ page })
    loadData()
  }
}))

// 方法
const loadData = async () => {
  loading.value = true
  try {
    await instanceStore.fetchList({
      ...queryForm,
      page: instanceStore.pagination.page,
      pageSize: instanceStore.pagination.pageSize
    })
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  instanceStore.updatePagination({ page: 1 })
  loadData()
}

const handleReset = () => {
  Object.assign(queryForm, {
    nodeId: null,
    status: null,
    keyword: ''
  })
  handleSearch()
}

const handleRefresh = () => {
  loadData()
}

const handleCreate = () => {
  createModal.show = true
  createModal.data = null
}

const handleCreateSuccess = () => {
  loadData()
}

const handleAction = async (row, action) => {
  try {
    await instanceStore.executeAction(row.id, action)
    message.success(`${action}操作已执行`)
    loadData()
  } catch (error) {
    message.error(`操作失败: ${error.message}`)
  }
}

// 生命周期
onMounted(() => {
  nodeStore.fetchList()
  loadData()
})
</script>

<style scoped>
.pve-instances {
  padding: 16px;
}
</style>
```

### 2. 状态管理

创建 `web/src/views/addons/pve/store/instances.ts`：

```typescript
import { defineStore } from 'pinia'
import { instanceAPI } from '../api/instances'
import type { Instance, InstanceListQuery, CreateInstanceData } from '../types/instances'

interface State {
  list: Instance[]
  pagination: {
    page: number
    pageSize: number
    total: number
  }
  loading: boolean
}

export const useInstanceStore = defineStore('pve-instances', {
  state: (): State => ({
    list: [],
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0
    },
    loading: false
  }),

  actions: {
    async fetchList(query: InstanceListQuery) {
      this.loading = true
      try {
        const response = await instanceAPI.getList(query)
        this.list = response.list
        this.pagination = {
          page: response.page,
          pageSize: response.pageSize,
          total: response.total
        }
      } finally {
        this.loading = false
      }
    },

    async createInstance(data: CreateInstanceData) {
      const response = await instanceAPI.create(data)
      return response
    },

    async executeAction(instanceId: number, action: string, force = false) {
      const response = await instanceAPI.executeAction(instanceId, { action, force })
      return response
    },

    updatePagination(pagination: Partial<State['pagination']>) {
      this.pagination = { ...this.pagination, ...pagination }
    }
  }
})
```

## 📚 插件配置管理

### 配置文件结构

创建 `server/addons/pve/config.yaml`：

```yaml
# PVE插件配置
addon:
  name: "pve"
  version: "1.0.0"
  description: "Proxmox VE 云平台管理插件"
  author: "HotGo Team"
  homepage: "https://github.com/bufanyun/hotgo"
  
# PVE节点配置
nodes:
  # 默认节点配置
  default:
    timeout: 30s
    retries: 3
    insecure: false
  
  # 节点列表
  list:
    - name: "pve-node1"
      host: "*************"
      port: 8006
      auth:
        type: "token"  # token 或 password
        token_id: "root@pam!hotgo"
        token_secret: "your-token-secret"
        # 或者使用密码认证
        # username: "root@pam"
        # password: "your-password"
      
    - name: "pve-node2" 
      host: "*************"
      port: 8006
      auth:
        type: "token"
        token_id: "root@pam!hotgo"
        token_secret: "your-token-secret"

# 计费配置
billing:
  currency: "CNY"
  precision: 2
  tax_rate: 0.06
  invoice:
    auto_generate: true
    template: "default"
  
  # 资源价格策略
  pricing:
    cpu:
      unit: "core/hour"
      price: 0.05
    memory:
      unit: "mb/hour"
      price: 0.001
    disk:
      unit: "gb/month"
      price: 0.1
    bandwidth:
      unit: "mbps/month"
      price: 10.0

# 监控配置
monitoring:
  enabled: true
  interval: 60  # 秒
  retention: "30d"
  metrics:
    - "cpu"
    - "memory"
    - "disk"
    - "network"
  
  # 告警配置
  alerts:
    cpu_threshold: 80
    memory_threshold: 85
    disk_threshold: 90
    
  # 数据存储
  storage:
    type: "influxdb"  # influxdb 或 mysql
    influxdb:
      host: "localhost:8086"
      database: "pve_monitoring"
      username: "admin"
      password: "password"

# 用户门户配置
portal:
  enabled: true
  features:
    - "dashboard"
    - "instances"
    - "billing"
    - "tickets"
  
  # 自助服务限制
  limits:
    max_instances_per_user: 10
    max_cpu_cores_per_user: 20
    max_memory_per_user: 40960  # MB

# 安全配置
security:
  api_rate_limit:
    enabled: true
    requests_per_minute: 60
  
  # 操作审计
  audit:
    enabled: true
    actions:
      - "create"
      - "delete" 
      - "start"
      - "stop"
      - "reboot"
      
# 通知配置
notifications:
  enabled: true
  channels:
    - "email"
    - "webhook"
  
  # 邮件配置
  email:
    smtp_host: "smtp.example.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-password"
    from: "PVE Cloud <<EMAIL>>"
  
  # Webhook配置
  webhook:
    url: "https://api.example.com/webhook"
    secret: "your-webhook-secret"
```

## 🧪 测试指南

### 1. 单元测试

创建 `server/addons/pve/test/service_test.go`：

```go
package test

import (
    "context"
    "testing"
    "hotgo/addons/pve/service"
    "github.com/stretchr/testify/assert"
)

func TestInstanceService_Create(t *testing.T) {
    ctx := context.Background()
    
    // 测试数据
    input := &model.InstanceCreateInput{
        NodeID:      1,
        Name:        "test-instance",
        Description: "测试实例",
        TemplateID:  1,
        ProductID:   1,
        Period:      1,
        PeriodType:  "month",
        AutoRenew:   false,
    }
    
    // 执行测试
    result, err := service.Instances().Create(ctx, input)
    
    // 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Greater(t, result.InstanceID, uint64(0))
    assert.Greater(t, result.VMID, 0)
}

func TestNodeService_List(t *testing.T) {
    ctx := context.Background()
    
    // 执行测试
    result, err := service.Nodes().List(ctx, &model.NodeListInput{
        Page:     1,
        PageSize: 10,
    })
    
    // 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.GreaterOrEqual(t, len(result.List), 0)
}
```

### 2. 集成测试

创建 `server/addons/pve/test/integration_test.go`：

```go
package test

import (
    "context"
    "testing"
    "time"
    "hotgo/addons/pve/library/pveclient"
)

func TestPVEIntegration(t *testing.T) {
    // 创建PVE客户端
    config := &pveclient.Config{
        Host:        "*************",
        Port:        8006,
        TokenID:     "root@pam!test",
        TokenSecret: "your-token-secret",
        Insecure:    true,
        Timeout:     30 * time.Second,
    }
    
    client, err := pveclient.NewPVEClient(config)
    assert.NoError(t, err)
    
    ctx := context.Background()
    
    // 测试获取版本信息
    version, err := client.GetVersion(ctx)
    assert.NoError(t, err)
    assert.NotEmpty(t, version.Version)
    
    // 测试获取节点列表
    nodes, err := client.GetNodes(ctx)
    assert.NoError(t, err)
    assert.GreaterOrEqual(t, len(nodes), 1)
    
    // 测试获取虚拟机列表
    if len(nodes) > 0 {
        vms, err := client.GetVMs(ctx, nodes[0].Node)
        assert.NoError(t, err)
        t.Logf("节点 %s 有 %d 个虚拟机", nodes[0].Node, len(vms))
    }
}
```

### 3. 完整测试体系

#### 测试框架配置

**创建 `server/addons/pve/test/main_test.go`**:

```go
package test

import (
    "log"
    "os"
    "testing"
)

func TestMain(m *testing.M) {
    // 测试前置准备
    setupTestEnvironment()
    
    // 运行测试
    code := m.Run()
    
    // 测试后清理
    teardownTestEnvironment()
    
    os.Exit(code)
}

func setupTestEnvironment() {
    log.Println("设置测试环境...")
    // 初始化数据库连接
    // 准备测试数据
    // 启动mock服务
}

func teardownTestEnvironment() {
    log.Println("清理测试环境...")
    // 清理测试数据
    // 关闭数据库连接
    // 停止mock服务
}
```

#### API测试脚本增强版

**创建测试脚本 `server/addons/pve/test/api_test.sh`**：

```bash
#!/bin/bash

# PVE插件API自动化测试脚本
# 使用方法: ./api_test.sh [环境]

set -e

# 配置
ENV=${1:-dev}
API_BASE="http://localhost:8000/api/v1/addon/pve"
USERNAME="admin"
PASSWORD="123456"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 认证获取token
authenticate() {
    log_info "获取认证token..."
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"'$USERNAME'","password":"'$PASSWORD'"}' \
        "$API_BASE/../auth/login")
    
    AUTH_TOKEN=$(echo $response | jq -r '.data.token')
    
    if [ "$AUTH_TOKEN" != "null" ]; then
        log_success "认证成功"
    else
        log_error "认证失败"
        exit 1
    fi
}

# 测试节点管理
test_nodes() {
    log_info "测试节点管理API..."
    
    # 获取节点列表
    local response=$(curl -s -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE/admin/nodes")
    
    local code=$(echo $response | jq -r '.code')
    if [ "$code" = "0" ]; then
        log_success "获取节点列表成功"
        local count=$(echo $response | jq '.data.list | length')
        log_info "找到 $count 个节点"
        
        # 保存第一个节点ID供后续使用
        if [ "$count" -gt 0 ]; then
            NODE_ID=$(echo $response | jq -r '.data.list[0].id')
        fi
    else
        log_error "获取节点列表失败"
        return 1
    fi
}

# 测试实例管理
test_instances() {
    log_info "测试实例管理API..."
    
    # 获取实例列表
    local response=$(curl -s -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE/admin/instances?page=1&pageSize=20")
    
    local code=$(echo $response | jq -r '.code')
    if [ "$code" = "0" ]; then
        log_success "获取实例列表成功"
        local count=$(echo $response | jq '.data.list | length')
        local total=$(echo $response | jq '.data.total')
        log_info "当前页显示 $count 个实例，总计 $total 个"
    else
        log_error "获取实例列表失败"
        return 1
    fi
}

# 测试创建实例（如果有可用节点）
test_create_instance() {
    if [ -z "$NODE_ID" ]; then
        log_info "没有可用节点，跳过实例创建测试"
        return 0
    fi
    
    log_info "测试创建实例..."
    
    local instance_name="api-test-$(date +%s)"
    local response=$(curl -s -X POST \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "nodeId": '$NODE_ID',
            "name": "'$instance_name'",
            "description": "API测试实例",
            "templateId": 1,
            "productId": 1,
            "period": 1,
            "periodType": "month",
            "autoRenew": false
        }' \
        "$API_BASE/admin/instances")
    
    local code=$(echo $response | jq -r '.code')
    if [ "$code" = "0" ]; then
        log_success "实例创建请求成功"
        local instance_id=$(echo $response | jq -r '.data.instanceId')
        local vm_id=$(echo $response | jq -r '.data.vmid')
        log_info "实例ID: $instance_id, VMID: $vm_id"
        
        # 保存实例ID供清理使用
        INSTANCE_ID=$instance_id
    else
        local message=$(echo $response | jq -r '.message')
        log_error "实例创建失败: $message"
        return 1
    fi
}

# 清理测试数据
cleanup() {
    if [ -n "$INSTANCE_ID" ]; then
        log_info "清理测试实例..."
        curl -s -X POST \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"action": "delete", "force": true}' \
            "$API_BASE/admin/instances/$INSTANCE_ID/action" > /dev/null
        log_success "清理完成"
    fi
}

# 主流程
main() {
    log_info "开始PVE插件API测试"
    
    # 设置清理钩子
    trap cleanup EXIT
    
    # 执行测试
    authenticate
    test_nodes
    test_instances
    test_create_instance
    
    log_success "所有测试完成"
}

# 检查依赖
if ! command -v jq &> /dev/null; then
    log_error "请安装 jq 工具: apt-get install jq"
    exit 1
fi

# 运行测试
main
```

## 📦 部署和发布

### 1. 编译打包

创建构建脚本 `build.sh`：

```bash
#!/bin/bash

set -e

# 版本信息
VERSION="1.0.0"
BUILD_TIME=$(date "+%Y-%m-%d %H:%M:%S")
GIT_COMMIT=$(git rev-parse --short HEAD)

# 编译后端
echo "编译后端..."
cd server
CGO_ENABLED=0 GOOS=linux go build \
  -ldflags "-X main.version=$VERSION -X main.buildTime='$BUILD_TIME' -X main.gitCommit=$GIT_COMMIT" \
  -o ../dist/hotgo-pve main.go

# 编译前端
echo "编译前端..."
cd ../web
npm run build
cp -r dist/* ../dist/web/

echo "构建完成: dist/"
```

### 2. Docker部署

创建 `Dockerfile`：

```dockerfile
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY server/ ./
RUN go mod tidy && \
    CGO_ENABLED=0 go build -o hotgo-pve main.go

FROM node:18-alpine AS web-builder

WORKDIR /app
COPY web/ ./
RUN npm ci && npm run build

FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

WORKDIR /app

COPY --from=builder /app/hotgo-pve .
COPY --from=web-builder /app/dist ./web
COPY server/manifest ./manifest
COPY server/addons ./addons

EXPOSE 8000

CMD ["./hotgo-pve"]
```

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  hotgo-pve:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GF_GCFG_FILE=manifest/config/config.docker.yaml
    volumes:
      - ./logs:/app/temp/logs
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: hotgo_pve
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  # PVE监控（可选）
  influxdb:
    image: influxdb:2.0
    environment:
      INFLUXDB_DB: pve_monitoring
      INFLUXDB_ADMIN_USER: admin
      INFLUXDB_ADMIN_PASSWORD: password
    volumes:
      - influxdb_data:/var/lib/influxdb2
    ports:
      - "8086:8086"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
  grafana_data:
```

### 3. 插件发布

创建插件发布脚本 `release.sh`：

```bash
#!/bin/bash

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "用法: $0 <version>"
    exit 1
fi

# 创建发布目录
RELEASE_DIR="release/pve-plugin-$VERSION"
mkdir -p $RELEASE_DIR

# 复制插件文件
cp -r server/addons/pve $RELEASE_DIR/
cp -r web/src/views/addons/pve $RELEASE_DIR/web/

# 创建安装脚本
cat > $RELEASE_DIR/install.sh << 'EOF'
#!/bin/bash
echo "正在安装PVE插件..."

# 检查HotGo环境
if [ ! -d "server/addons" ]; then
    echo "错误: 请在HotGo项目根目录运行此脚本"
    exit 1
fi

# 复制插件文件
cp -r pve server/addons/
cp -r web server/web/src/views/addons/

echo "插件文件复制完成"
echo "请重启HotGo服务以加载插件"
EOF

chmod +x $RELEASE_DIR/install.sh

# 创建说明文档
cat > $RELEASE_DIR/README.md << EOF
# PVE插件 v$VERSION

## 安装说明

1. 将此插件文件夹放到HotGo项目根目录
2. 运行安装脚本: ./install.sh
3. 配置PVE连接信息
4. 重启HotGo服务

## 配置说明

请参考config.yaml配置文件，配置PVE节点信息。

## 更多信息

- 文档: https://hotgo.facms.cn
- 问题反馈: https://github.com/bufanyun/hotgo/issues
EOF

# 打包发布
cd release
tar -czf "pve-plugin-$VERSION.tar.gz" "pve-plugin-$VERSION"

echo "发布包已创建: release/pve-plugin-$VERSION.tar.gz"
```

## ❓ 常见问题

### 1. 开发环境问题

**Q: Go模块依赖下载失败**
```bash
# 设置代理
go env -w GOPROXY=https://goproxy.cn,direct
go clean -modcache
go mod tidy
```

**Q: 前端依赖安装失败**
```bash
# 清理缓存
npm cache clean --force
# 或删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 2. PVE连接问题

**Q: PVE API连接超时**
- 检查PVE节点网络连通性
- 确认API端口(8006)是否开放
- 验证SSL证书配置

**Q: 认证失败**
- 检查API Token是否正确
- 确认用户权限是否足够
- 验证用户域设置(@pam)

### 3. 数据库问题

**Q: 数据表创建失败**
```sql
-- 检查用户权限
SHOW GRANTS FOR 'hotgo'@'%';
-- 手动执行SQL
SOURCE server/addons/pve/install.sql;
```

### 4. 性能优化

**Q: 大量虚拟机查询慢**
- 添加数据库索引
- 实现分页查询
- 使用Redis缓存

**Q: 监控数据存储压力大**
- 使用时序数据库(InfluxDB)
- 设置数据保留策略
- 实现数据聚合

## 🤝 社区支持

- **官方文档**: https://hotgo.facms.cn
- **GitHub仓库**: https://github.com/bufanyun/hotgo
- **问题反馈**: https://github.com/bufanyun/hotgo/issues
- **讨论交流**: 加入HotGo QQ群或微信群

---

**最后更新**: 2024-08-28  
**文档版本**: v1.0.0  
**维护团队**: HotGo 开发组

---

## 📖 相关文档

- [PVE 开发文档主页](./README.md) - 完整的项目概述和快速开始指南
- [认证和权限系统](./authentication-permissions.md) - PVE API 认证机制详解
- [PVE 客户端实现](./client-implementation.md) - Go 语言客户端开发指南
- [虚拟机生命周期管理](./vm-lifecycle-management.md) - VM 管理服务实现
- [开发进度跟踪](./development-progress.md) - 项目开发状态和里程碑

## 📚 扩展阅读

- **HotGo 官方文档**: https://hotgo.facms.cn
- **Proxmox VE API 文档**: https://pve.proxmox.com/pve-docs/api-viewer/
- **GoFrame 开发手册**: https://goframe.org/
- **Vue 3 官方文档**: https://vuejs.org/
- **TypeScript 手册**: https://www.typescriptlang.org/docs/