// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/addons/pve/model/input"
)

// DashboardGetStatsReq 仪表盘统计请求
type DashboardGetStatsReq struct {
	g.Meta `path:"/admin/addons/pve/dashboard/stats" method:"get" summary:"获取仪表盘统计数据" tags:"PVE仪表盘"`
}

type DashboardGetStatsRes struct {
	*input.DashboardStatsOut
}

// DashboardGetResourceUsageReq 获取资源使用情况请求
type DashboardGetResourceUsageReq struct {
	g.Meta `path:"/admin/addons/pve/dashboard/resources" method:"get" summary:"获取资源使用情况" tags:"PVE仪表盘"`
}

type DashboardGetResourceUsageRes struct {
	*input.DashboardResourceOut
}

// DashboardGetSystemAlertsReq 获取系统告警请求
type DashboardGetSystemAlertsReq struct {
	g.Meta   `path:"/admin/addons/pve/dashboard/alerts" method:"get" summary:"获取系统告警" tags:"PVE仪表盘"`
	Resolved *bool `json:"resolved" dc:"是否已解决，不传获取全部"`
}

type DashboardGetSystemAlertsRes struct {
	*input.DashboardAlertsOut
}

// DashboardResolveAlertReq 解决告警请求
type DashboardResolveAlertReq struct {
	g.Meta `path:"/admin/addons/pve/dashboard/alerts/resolve" method:"post" summary:"解决告警" tags:"PVE仪表盘"`
	ID     int `json:"id" v:"required" dc:"告警ID"`
}

type DashboardResolveAlertRes struct{}