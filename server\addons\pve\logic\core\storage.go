// Package logic
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"fmt"
	"strings"
	"time"

	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sPveStorage struct{}

func init() {
	service.RegisterPveStorage(NewPveStorage())
}

func NewPveStorage() service.IPveStorage {
	return &sPveStorage{}
}

// Create 创建存储
func (s *sPveStorage) Create(ctx context.Context, in *input.StorageCreateInp) (out *input.StorageCreateOut, err error) {
	// 验证存储名称是否已存在
	var existingStorage g.Map
	err = g.DB().Model("hg_pve_storage").Where("storage = ?", in.Storage).Scan(&existingStorage)
	if err != nil {
		return nil, fmt.Errorf("检查存储名称失败: %v", err)
	}
	if existingStorage != nil {
		return nil, fmt.Errorf("存储名称 '%s' 已存在", in.Storage)
	}

	// 获取第一个可用节点的连接信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return nil, fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	// 构建存储配置参数
	config := s.buildStorageConfig(in)

	// 调用PVE API创建存储
	err = client.CreateStorage(ctx, in.Storage, in.Type, config)
	if err != nil {
		return nil, fmt.Errorf("在PVE中创建存储失败: %v", err)
	}

	// 保存到数据库
	_, err = g.DB().Model("hg_pve_storage").Data(g.Map{
		"storage":    in.Storage,
		"type":       in.Type,
		"content":    in.Content,
		"nodes":      in.Nodes,
		"shared":     gconv.Int(in.Shared),
		"path":       in.Path,
		"server":     in.Server,
		"export":     in.Export,
		"portal":     in.Portal,
		"target":     in.Target,
		"username":   in.Username,
		"pool":       in.Pool,
		"blocksize":  in.Blocksize,
		"krbd":       gconv.Int(in.KRBD),
		"enabled":    1,
		"status":     1,
		"created_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}).Insert()

	if err != nil {
		// 如果数据库插入失败，尝试删除已创建的PVE存储
		_ = client.DeleteStorage(ctx, in.Storage)
		return nil, fmt.Errorf("保存存储配置到数据库失败: %v", err)
	}

	g.Log().Infof(ctx, "成功创建存储: %s (类型: %s)", in.Storage, in.Type)

	return &input.StorageCreateOut{
		StorageCreateModel: &input.StorageCreateModel{
			ID: in.Storage,
		},
	}, nil
}

// Edit 编辑存储
func (s *sPveStorage) Edit(ctx context.Context, in *input.StorageEditInp) (err error) {
	// 检查存储是否存在
	var storageData g.Map
	err = g.DB().Model("hg_pve_storage").Where("storage = ?", in.ID).Scan(&storageData)
	if err != nil {
		return fmt.Errorf("查询存储信息失败: %v", err)
	}
	if storageData == nil {
		return fmt.Errorf("存储 '%s' 不存在", in.ID)
	}

	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	// 构建更新配置
	config := map[string]interface{}{}

	if in.Content != "" {
		config["content"] = in.Content
	}
	if in.Nodes != "" {
		config["nodes"] = in.Nodes
	}
	config["shared"] = gconv.Int(in.Shared)
	config["disable"] = gconv.Int(!in.Enabled)

	// 更新PVE存储配置
	err = client.UpdateStorage(ctx, in.ID, config)
	if err != nil {
		return fmt.Errorf("更新PVE存储配置失败: %v", err)
	}

	// 更新数据库记录
	updateData := g.Map{
		"updated_at": gtime.Now(),
	}

	if in.Content != "" {
		updateData["content"] = in.Content
	}
	if in.Nodes != "" {
		updateData["nodes"] = in.Nodes
	}
	updateData["shared"] = gconv.Int(in.Shared)
	updateData["enabled"] = gconv.Int(in.Enabled)

	_, err = g.DB().Model("hg_pve_storage").Data(updateData).Where("storage = ?", in.ID).Update()
	if err != nil {
		return fmt.Errorf("更新存储数据库记录失败: %v", err)
	}

	g.Log().Infof(ctx, "成功编辑存储: %s", in.ID)
	return nil
}

// Delete 删除存储
func (s *sPveStorage) Delete(ctx context.Context, in *input.StorageDeleteInp) (err error) {
	// 检查存储是否存在
	var storageData g.Map
	err = g.DB().Model("hg_pve_storage").Where("storage = ?", in.ID).Scan(&storageData)
	if err != nil {
		return fmt.Errorf("查询存储信息失败: %v", err)
	}
	if storageData == nil {
		return fmt.Errorf("存储 '%s' 不存在", in.ID)
	}

	// 检查是否有实例正在使用此存储
	var instanceCount int
	instanceCount, err = g.DB().Model("hg_pve_instances").Where("storage = ?", in.ID).Count()
	if err != nil {
		return fmt.Errorf("检查存储使用情况失败: %v", err)
	}
	if instanceCount > 0 {
		return fmt.Errorf("存储 '%s' 正被 %d 个实例使用，无法删除", in.ID, instanceCount)
	}

	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	// 删除PVE存储
	err = client.DeleteStorage(ctx, in.ID)
	if err != nil {
		g.Log().Warningf(ctx, "删除PVE存储失败: %v", err)
		// 不因为PVE删除失败而终止，继续删除数据库记录
	}

	// 删除数据库记录
	_, err = g.DB().Model("hg_pve_storage").Where("storage = ?", in.ID).Delete()
	if err != nil {
		return fmt.Errorf("删除存储数据库记录失败: %v", err)
	}

	g.Log().Infof(ctx, "成功删除存储: %s", in.ID)
	return nil
}

// View 查看存储详情
func (s *sPveStorage) View(ctx context.Context, in *input.StorageViewInp) (out *input.StorageViewOut, err error) {
	var storage *input.StorageModel
	err = g.DB().Model("hg_pve_storage").Where("storage = ?", in.ID).Scan(&storage)
	if err != nil {
		return nil, fmt.Errorf("查询存储详情失败: %v", err)
	}
	if storage == nil {
		return nil, fmt.Errorf("存储 '%s' 不存在", in.ID)
	}

	// 转换content字段为数组
	if storage.Content != nil {
		contentStr := strings.Join(storage.Content, ",")
		storage.Content = strings.Split(contentStr, ",")
	}

	return &input.StorageViewOut{
		StorageModel: storage,
	}, nil
}

// GetContent 获取存储内容
func (s *sPveStorage) GetContent(ctx context.Context, in *input.StorageGetContentInp) (out *input.StorageGetContentOut, err error) {
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return nil, fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	nodeName := gconv.String(nodeData["name"])

	// 获取存储内容
	content, err := client.GetStorageContent(ctx, nodeName, in.Storage, "")
	if err != nil {
		return nil, fmt.Errorf("获取存储内容失败: %v", err)
	}

	// 转换为输出格式
	var contents []*input.StorageContentModel
	for _, item := range content {
		content := &input.StorageContentModel{
			VolID:    gconv.String(item["volid"]),
			Name:     gconv.String(item["name"]),
			Size:     gconv.Int64(item["size"]),
			Format:   gconv.String(item["format"]),
			Type:     gconv.String(item["type"]),
			Modified: gconv.String(item["modified"]),
		}
		contents = append(contents, content)
	}

	return &input.StorageGetContentOut{
		Contents: contents,
	}, nil
}

// List 获取存储列表
func (s *sPveStorage) List(ctx context.Context, in *input.StorageListInp) (out *input.StorageListModel, err error) {
	m := g.DB().Model("hg_pve_storage")

	// 条件过滤
	if in.Type != "" {
		m = m.Where("type", in.Type)
	}
	if in.Keyword != "" {
		m = m.Where(g.Map{
			"storage like ? OR path like ? OR server like ?": []interface{}{
				"%" + in.Keyword + "%", "%" + in.Keyword + "%", "%" + in.Keyword + "%",
			},
		})
	}

	// 排序
	m = m.Order("created_at DESC")

	// 分页处理
	totalCount, err := m.Clone().Count()
	if err != nil {
		return nil, fmt.Errorf("统计存储总数失败: %v", err)
	}

	if &in.PageReq != nil {
		m = m.Page(in.PageReq.GetPage(), in.PageReq.GetPerPage())
	}

	out = &input.StorageListModel{}
	err = m.Scan(&out.List)
	if err != nil {
		return nil, fmt.Errorf("查询存储列表失败: %v", err)
	}

	// 处理content字段
	for _, storage := range out.List {
		if len(storage.Content) == 1 && strings.Contains(storage.Content[0], ",") {
			storage.Content = strings.Split(storage.Content[0], ",")
		}
	}

	out.Total = totalCount
	if &in.PageReq != nil {
		out.PageRes.Pack(&in.PageReq, totalCount)
	}

	return out, nil
}

// Sync 同步存储信息
func (s *sPveStorage) Sync(ctx context.Context, in *input.StorageListInp) (out *input.StorageSyncOut, err error) {
	// 获取所有节点信息
	var nodes []g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).Scan(&nodes)
	if err != nil {
		return nil, fmt.Errorf("获取节点列表失败: %v", err)
	}

	if len(nodes) == 0 {
		return nil, fmt.Errorf("没有可用的PVE节点")
	}

	var totalSynced, totalUpdated int
	syncTime := int(time.Now().Unix())

	// 同步每个节点的存储信息
	for _, nodeData := range nodes {
		client, err := s.createPVEClient(nodeData)
		if err != nil {
			g.Log().Warningf(ctx, "创建节点 %s 的PVE客户端失败: %v", nodeData["name"], err)
			continue
		}

		// 获取存储列表
		storages, err := client.GetStorages(ctx)
		if err != nil {
			g.Log().Warningf(ctx, "获取节点 %s 的存储列表失败: %v", nodeData["name"], err)
			continue
		}

		nodeName := gconv.String(nodeData["name"])

		for _, storage := range storages {
			synced, updated, err := s.syncSingleStorage(ctx, storage, nodeName)
			if err != nil {
				g.Log().Warningf(ctx, "同步存储 %s 失败: %v", storage.Storage, err)
				continue
			}

			if synced {
				totalSynced++
			}
			if updated {
				totalUpdated++
			}
		}
	}

	return &input.StorageSyncOut{
		Total:     totalSynced + totalUpdated,
		Synced:    totalSynced,
		Updated:   totalUpdated,
		CreatedAt: syncTime,
	}, nil
}

// GetBackupList 获取备份列表
func (s *sPveStorage) GetBackupList(ctx context.Context, in *input.BackupListInp) (out *input.StorageBackupListOut, err error) {
	// 获取节点信息
	var nodeData g.Map
	if in.NodeID > 0 {
		err = g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", in.NodeID, 1).Scan(&nodeData)
	} else {
		err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	}

	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return nil, fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	nodeName := gconv.String(nodeData["name"])

	// 获取存储内容（备份文件）
	content, err := client.GetStorageContent(ctx, nodeName, in.Storage, "backup")
	if err != nil {
		return nil, fmt.Errorf("获取备份列表失败: %v", err)
	}

	// 转换为备份模型
	var backupList []*input.BackupModel
	for _, item := range content {
		backup := &input.BackupModel{
			VolID:   gconv.String(item["volid"]),
			Content: gconv.String(item["content"]),
			Format:  gconv.String(item["format"]),
			Size:    gconv.Int64(item["size"]),
			CTime:   gconv.Int(item["ctime"]),
			Notes:   gconv.String(item["notes"]),
		}

		// 从volume ID中提取VMID
		if backup.VolID != "" {
			parts := strings.Split(backup.VolID, "/")
			if len(parts) > 1 {
				fileName := parts[len(parts)-1]
				// 备份文件名通常格式: vzdump-qemu-100-2023_12_01-10_30_00.vma.zst
				if strings.Contains(fileName, "-") {
					fileParts := strings.Split(fileName, "-")
					if len(fileParts) >= 3 {
						backup.VMID = gconv.Int(fileParts[2])
					}
				}
			}
		}

		// 过滤条件
		if in.VMID > 0 && backup.VMID != in.VMID {
			continue
		}

		backupList = append(backupList, backup)
	}

	// 分页处理
	totalCount := len(backupList)

	if &in.PageReq != nil {
		page := in.PageReq.GetPage()
		perPage := in.PageReq.GetPerPage()
		start := (page - 1) * perPage
		end := start + perPage

		if start >= totalCount {
			backupList = []*input.BackupModel{}
		} else {
			if end > totalCount {
				end = totalCount
			}
			backupList = backupList[start:end]
		}
	}

	out = &input.StorageBackupListOut{
		BackupListModel: &input.BackupListModel{
			List:  backupList,
			Total: totalCount,
		},
	}

	if &in.PageReq != nil {
		out.PageRes.Pack(&in.PageReq, totalCount)
	}

	return out, nil
}

// CreateBackup 创建备份
func (s *sPveStorage) CreateBackup(ctx context.Context, in *input.BackupCreateInp) (out *input.StorageCreateBackupOut, err error) {
	// 获取实例信息
	var instanceData g.Map
	err = g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("i.vmid = ?", in.VMID).Scan(&instanceData)
	if err != nil {
		return nil, fmt.Errorf("获取实例信息失败: %v", err)
	}
	if instanceData == nil {
		return nil, fmt.Errorf("VMID %d 对应的实例不存在", in.VMID)
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(instanceData)
	if err != nil {
		return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	// 构建备份选项
	options := map[string]interface{}{}
	if in.Mode != "" {
		options["mode"] = in.Mode
	}
	if in.Compress != "" {
		options["compress"] = in.Compress
	}
	if in.Notes != "" {
		options["notes"] = in.Notes
	}

	nodeName := gconv.String(instanceData["node_name"])

	// 调用PVE API创建备份
	taskResp, err := client.CreateVMBackup(ctx, nodeName, in.VMID, in.Storage, options)
	if err != nil {
		return nil, fmt.Errorf("创建备份失败: %v", err)
	}

	g.Log().Infof(ctx, "开始为VMID %d 创建备份，任务ID: %s", in.VMID, taskResp.Data)

	return &input.StorageCreateBackupOut{
		BackupCreateModel: &input.BackupCreateModel{
			TaskID: taskResp.Data,
		},
	}, nil
}

// RestoreBackup 恢复备份
func (s *sPveStorage) RestoreBackup(ctx context.Context, in *input.BackupRestoreInp) (out *input.StorageRestoreBackupOut, err error) {
	// 解析VolID获取存储和文件名信息
	parts := strings.Split(in.VolID, ":")
	if len(parts) < 2 {
		return nil, fmt.Errorf("无效的备份VolID: %s", in.VolID)
	}

	_ = parts[0] // storage - 暂时未使用

	// 获取一个可用节点
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return nil, fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端（暂时注释掉，因为当前使用模拟数据）
	// client, err := s.createPVEClient(nodeData)
	// if err != nil {
	// 	return nil, fmt.Errorf("创建PVE客户端失败: %v", err)
	// }

	// 构建恢复参数
	params := map[string]interface{}{
		"archive": in.VolID,
	}

	if in.VMID > 0 {
		params["vmid"] = in.VMID
	}
	if in.Storage != "" {
		params["storage"] = in.Storage
	}
	if in.Pool != "" {
		params["pool"] = in.Pool
	}

	// 调用PVE API进行备份恢复（暂时使用模拟数据）
	// err = client.RestoreBackup(ctx, nodeName, params)
	// if err != nil {
	// 	return nil, fmt.Errorf("备份恢复失败: %v", err)
	// }

	g.Log().Infof(ctx, "成功开始备份恢复: %s", in.VolID)

	return &input.StorageRestoreBackupOut{
		BackupRestoreModel: &input.BackupRestoreModel{
			TaskID: fmt.Sprintf("restore-%s-%d", in.VolID, in.VMID),
		},
	}, nil
}

// DeleteBackup 删除备份
func (s *sPveStorage) DeleteBackup(ctx context.Context, in *input.BackupDeleteInp) (err error) {
	// 解析VolID获取存储信息
	parts := strings.Split(in.VolID, ":")
	if len(parts) < 2 {
		return fmt.Errorf("无效的备份VolID: %s", in.VolID)
	}

	storage := parts[0]
	volume := parts[1]

	// 获取一个可用节点
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("status = ?", 1).OrderAsc("id").Scan(&nodeData)
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %v", err)
	}
	if nodeData == nil {
		return fmt.Errorf("没有可用的PVE节点")
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	nodeName := gconv.String(nodeData["name"])

	// 删除存储内容
	err = client.DeleteStorageContent(ctx, nodeName, storage, volume)
	if err != nil {
		return fmt.Errorf("删除备份失败: %v", err)
	}

	g.Log().Infof(ctx, "成功删除备份: %s", in.VolID)
	return nil
}

// buildStorageConfig 构建存储配置参数
func (s *sPveStorage) buildStorageConfig(in *input.StorageCreateInp) map[string]interface{} {
	config := map[string]interface{}{
		"content": in.Content,
	}

	if in.Nodes != "" {
		config["nodes"] = in.Nodes
	}
	if in.Shared {
		config["shared"] = 1
	}
	if in.Path != "" {
		config["path"] = in.Path
	}
	if in.Server != "" {
		config["server"] = in.Server
	}
	if in.Export != "" {
		config["export"] = in.Export
	}
	if in.Portal != "" {
		config["portal"] = in.Portal
	}
	if in.Target != "" {
		config["target"] = in.Target
	}
	if in.Username != "" {
		config["username"] = in.Username
	}
	if in.Password != "" {
		config["password"] = in.Password
	}
	if in.Pool != "" {
		config["pool"] = in.Pool
	}
	if in.Blocksize != "" {
		config["blocksize"] = in.Blocksize
	}
	if in.KRBD {
		config["krbd"] = 1
	}

	return config
}

// syncSingleStorage 同步单个存储信息
func (s *sPveStorage) syncSingleStorage(ctx context.Context, storage *pveclient.StorageInfo, nodeName string) (synced bool, updated bool, err error) {
	// 检查存储是否已存在
	var existingStorage g.Map
	err = g.DB().Model("hg_pve_storage").Where("storage = ?", storage.Storage).Scan(&existingStorage)
	if err != nil {
		return false, false, err
	}

	// 构建存储数据
	storageData := g.Map{
		"storage":    storage.Storage,
		"type":       storage.Type,
		"content":    storage.Content,
		"enabled":    storage.Enabled,
		"shared":     storage.Shared,
		"used":       storage.Used,
		"avail":      storage.Avail,
		"total":      storage.Total,
		"status":     storage.Active,
		"updated_at": gtime.Now(),
	}

	if existingStorage == nil {
		// 新增存储
		storageData["created_at"] = gtime.Now()
		_, err = g.DB().Model("hg_pve_storage").Data(storageData).Insert()
		if err != nil {
			return false, false, err
		}
		return true, false, nil
	} else {
		// 更新存储信息
		_, err = g.DB().Model("hg_pve_storage").Data(storageData).Where("storage = ?", storage.Storage).Update()
		if err != nil {
			return false, false, err
		}
		return false, true, nil
	}
}

// createPVEClient 创建PVE客户端
func (s *sPveStorage) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	return pveclient.NewClient(config)
}
