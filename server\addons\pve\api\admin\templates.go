// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/model/input/form"
)

// TemplateCreateReq 创建模板请求
type TemplateCreateReq struct {
	g.Meta      `path:"/templates" method:"post" summary:"创建系统模板" tags:"PVE管理"`
	Name        string  `json:"name" v:"required|length:1,100#模板名称不能为空|名称长度为1-100个字符" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" v:"required#操作系统类型不能为空" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	CpuCores    int     `json:"cpuCores" v:"required|min:1|max:64#CPU核心数不能为空|CPU核心数最少1个|CPU核心数最多64个" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" v:"required|min:128|max:131072#内存大小不能为空|内存最少128MB|内存最多131072MB" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" v:"required|min:1|max:1024#磁盘大小不能为空|磁盘最少1GB|磁盘最多1024GB" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" v:"min:0#价格不能为负数" dc:"价格"`
	Status      int     `json:"status" v:"in:1,2#状态值错误" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
}

type TemplateCreateRes struct {
	ID uint64 `json:"id" dc:"模板ID"`
}

// TemplateEditReq 编辑模板请求
type TemplateEditReq struct {
	g.Meta      `path:"/templates/{id}" method:"put" summary:"编辑系统模板" tags:"PVE管理"`
	ID          uint64  `json:"id" v:"required#模板ID不能为空" dc:"模板ID"`
	Name        string  `json:"name" v:"required|length:1,100#模板名称不能为空|名称长度为1-100个字符" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" v:"required#操作系统类型不能为空" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	CpuCores    int     `json:"cpuCores" v:"required|min:1|max:64#CPU核心数不能为空|CPU核心数最少1个|CPU核心数最多64个" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" v:"required|min:128|max:131072#内存大小不能为空|内存最少128MB|内存最多131072MB" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" v:"required|min:1|max:1024#磁盘大小不能为空|磁盘最少1GB|磁盘最多1024GB" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" v:"min:0#价格不能为负数" dc:"价格"`
	Status      int     `json:"status" v:"in:1,2#状态值错误" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
}

type TemplateEditRes struct{}

// TemplateDeleteReq 删除模板请求
type TemplateDeleteReq struct {
	g.Meta `path:"/templates/{id}" method:"delete" summary:"删除系统模板" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#模板ID不能为空" dc:"模板ID"`
}

type TemplateDeleteRes struct{}

// TemplateViewReq 查看模板请求
type TemplateViewReq struct {
	g.Meta `path:"/templates/{id}" method:"get" summary:"查看系统模板" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#模板ID不能为空" dc:"模板ID"`
}

type TemplateViewRes struct {
	*TemplateViewModel
}

type TemplateViewModel struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
	CreatedAt   string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string  `json:"updatedAt" dc:"更新时间"`
}

// TemplateListReq 模板列表请求
type TemplateListReq struct {
	g.Meta `path:"/templates" method:"get" summary:"获取系统模板列表" tags:"PVE管理"`
	form.PageReq
	Name     string `json:"name" dc:"模板名称"`
	OsType   string `json:"osType" dc:"操作系统类型"`
	NodeID   uint64 `json:"nodeId" dc:"节点ID"`
	Status   int    `json:"status" dc:"状态"`
	OrderBy  string `json:"orderBy" dc:"排序字段"`
	OrderDir string `json:"orderDir" dc:"排序方向"`
}

type TemplateListRes struct {
	form.PageRes
	List []*TemplateListModel `json:"list" dc:"模板列表"`
}

type TemplateListModel struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	TemplateID  int     `json:"templateId" dc:"PVE模板ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
	Status      int     `json:"status" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
	CreatedAt   string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string  `json:"updatedAt" dc:"更新时间"`
}

// TemplateSyncReq 同步PVE模板请求
type TemplateSyncReq struct {
	g.Meta `path:"/templates/sync" method:"post" summary:"同步PVE模板" tags:"PVE管理"`
	NodeID uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
}

type TemplateSyncRes struct {
	Success bool                  `json:"success" dc:"同步是否成功"`
	Message string                `json:"message" dc:"同步结果信息"`
	Count   int                   `json:"count" dc:"同步模板数量"`
	List    []*PVETemplateModel   `json:"list" dc:"PVE模板列表"`
}

type PVETemplateModel struct {
	VMID        int    `json:"vmid" dc:"虚拟机ID"`
	Name        string `json:"name" dc:"模板名称"`
	Description string `json:"description" dc:"描述"`
	OsType      string `json:"osType" dc:"操作系统类型"`
	Node        string `json:"node" dc:"节点名称"`
	IsTemplate  bool   `json:"isTemplate" dc:"是否为模板"`
	Status      string `json:"status" dc:"状态"`
}

// TemplateImportReq 导入PVE模板请求
type TemplateImportReq struct {
	g.Meta      `path:"/templates/import" method:"post" summary:"导入PVE模板" tags:"PVE管理"`
	NodeID      uint64                `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Templates   []*ImportTemplateData `json:"templates" v:"required#模板列表不能为空" dc:"要导入的模板列表"`
}

type ImportTemplateData struct {
	VMID        int     `json:"vmid" v:"required#虚拟机ID不能为空" dc:"虚拟机ID"`
	Name        string  `json:"name" v:"required#模板名称不能为空" dc:"模板名称"`
	Description string  `json:"description" dc:"描述"`
	OsType      string  `json:"osType" v:"required#操作系统类型不能为空" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	CpuCores    int     `json:"cpuCores" v:"required|min:1" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" v:"required|min:128" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" v:"required|min:1" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" v:"min:0" dc:"价格"`
	Status      int     `json:"status" v:"in:1,2" dc:"状态：1=启用 2=禁用"`
	Sort        int     `json:"sort" dc:"排序"`
}

type TemplateImportRes struct {
	Success     bool     `json:"success" dc:"导入是否成功"`
	Message     string   `json:"message" dc:"导入结果信息"`
	ImportCount int      `json:"importCount" dc:"成功导入数量"`
	FailedCount int      `json:"failedCount" dc:"失败数量"`
	FailedList  []string `json:"failedList" dc:"失败的模板列表"`
}

// TemplateSelectReq 模板选择列表请求
type TemplateSelectReq struct {
	g.Meta `path:"/templates/select" method:"get" summary:"获取模板选择列表" tags:"PVE管理"`
	NodeID uint64 `json:"nodeId" dc:"节点ID"`
	Status int    `json:"status" dc:"状态，默认1启用"`
}

type TemplateSelectRes struct {
	List []*TemplateSelectModel `json:"list" dc:"模板选择列表"`
}

type TemplateSelectModel struct {
	ID          uint64  `json:"id" dc:"模板ID"`
	Name        string  `json:"name" dc:"模板名称"`
	Description string  `json:"description" dc:"模板描述"`
	OsType      string  `json:"osType" dc:"操作系统类型"`
	OsVersion   string  `json:"osVersion" dc:"系统版本"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	CpuCores    int     `json:"cpuCores" dc:"默认CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"默认内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"默认磁盘大小(GB)"`
	Price       float64 `json:"price" dc:"价格"`
}