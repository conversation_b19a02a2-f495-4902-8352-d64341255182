// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== 虚拟机高级功能 API ==========

// MigrateVM 迁移虚拟机
func (c *Client) MigrateVM(ctx context.Context, node string, vmid int, target string, online bool) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/migrate", node, vmid)
	
	params := map[string]interface{}{
		"target": target,
	}
	if online {
		params["online"] = 1
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// ResizeVMDisk 调整虚拟机磁盘大小
func (c *Client) ResizeVMDisk(ctx context.Context, node string, vmid int, disk string, size string) error {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/resize", node, vmid)
	
	params := map[string]interface{}{
		"disk": disk,
		"size": size,
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// GetVMRRDData 获取虚拟机RRD监控数据
func (c *Client) GetVMRRDData(ctx context.Context, node string, vmid int, timeframe string) (map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/rrddata", node, vmid)
	
	params := map[string]interface{}{}
	if timeframe != "" {
		params["timeframe"] = timeframe
	}

	body, err := c.request(ctx, "GET", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	data, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的RRD数据格式")
	}

	return data, nil
}

// SendVMKeys 向虚拟机发送按键
func (c *Client) SendVMKeys(ctx context.Context, node string, vmid int, keys string) error {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/sendkey", node, vmid)
	
	params := map[string]interface{}{
		"key": keys,
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// GetVMFirewallRules 获取虚拟机防火墙规则
func (c *Client) GetVMFirewallRules(ctx context.Context, node string, vmid int) ([]*FirewallRule, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/firewall/rules", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var rules []*FirewallRule
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &rules); err != nil {
		return nil, err
	}

	return rules, nil
}

// CreateVMFirewallRule 创建虚拟机防火墙规则
func (c *Client) CreateVMFirewallRule(ctx context.Context, node string, vmid int, rule *FirewallRule) error {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/firewall/rules", node, vmid)
	
	params := map[string]interface{}{
		"action": rule.Action,
		"type":   rule.Type,
	}
	
	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// UpdateVMFirewallRule 更新虚拟机防火墙规则
func (c *Client) UpdateVMFirewallRule(ctx context.Context, node string, vmid int, pos int, rule *FirewallRule) error {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/firewall/rules/%d", node, vmid, pos)
	
	params := map[string]interface{}{}
	
	if rule.Action != "" {
		params["action"] = rule.Action
	}
	if rule.Type != "" {
		params["type"] = rule.Type
	}
	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteVMFirewallRule 删除虚拟机防火墙规则
func (c *Client) DeleteVMFirewallRule(ctx context.Context, node string, vmid int, pos int) error {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/firewall/rules/%d", node, vmid, pos)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// DeleteSnapshot 删除快照
func (c *Client) DeleteSnapshot(ctx context.Context, node string, vmid int, snapname string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/snapshot/%s", node, vmid, snapname)
	body, err := c.request(ctx, "DELETE", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// GetVMPendingChanges 获取虚拟机待处理的配置变更
func (c *Client) GetVMPendingChanges(ctx context.Context, node string, vmid int) ([]map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/pending", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var changes []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &changes); err != nil {
		return nil, err
	}

	return changes, nil
}

// GetVMAgentInfo 获取虚拟机代理信息
func (c *Client) GetVMAgentInfo(ctx context.Context, node string, vmid int) (map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/agent/info", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	data, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的代理信息数据格式")
	}

	return data, nil
}

// ExecuteVMAgentCommand 执行虚拟机代理命令
func (c *Client) ExecuteVMAgentCommand(ctx context.Context, node string, vmid int, command string, args map[string]interface{}) (map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/agent/%s", node, vmid, command)
	
	body, err := c.request(ctx, "POST", path, args)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	data, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的代理命令响应格式")
	}

	return data, nil
}