// Package pve
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pve

import (
	"context"
	_ "hotgo/addons/pve/crons"
	"hotgo/addons/pve/global"
	_ "hotgo/addons/pve/logic"
	_ "hotgo/addons/pve/queues"
	"hotgo/addons/pve/router"
	"hotgo/internal/library/addons"
	"hotgo/internal/service"
	"sync"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
)

type module struct {
	skeleton *addons.Skeleton
	ctx      context.Context
	sync.Mutex
}

func init() {
	newModule()
}

func newModule() {
	m := &module{
		skeleton: &addons.Skeleton{
			Label:       "PVE云平台",
			Name:        "pve",
			Group:       1,
			Logo:        "",
			Brief:       "Proxmox VE云平台管理系统",
			Description: "基于Proxmox VE的完整云平台解决方案，包含虚拟机管理、计费系统、监控告警等功能",
			Author:      "HotGo团队",
			Version:     "v1.0.0",
		},
		ctx: gctx.New(),
	}

	addons.RegisterModule(m)
}

// Start 启动模块
func (m *module) Start(option *addons.Option) (err error) {
	// 初始化模块
	global.Init(m.ctx, m.skeleton)

	// 注册插件路由
	option.Server.Group("/", func(group *ghttp.RouterGroup) {
		group.Middleware(service.Middleware().Addon)
		router.Admin(m.ctx, group)
		router.User(m.ctx, group)
		router.Webhook(m.ctx, group)
	})
	return
}

// Stop 停止模块
func (m *module) Stop() (err error) {
	return
}

// Ctx 上下文
func (m *module) Ctx() context.Context {
	return m.ctx
}

// GetSkeleton 获取模块
func (m *module) GetSkeleton() *addons.Skeleton {
	return m.skeleton
}

// Install 安装模块
func (m *module) Install(ctx context.Context) (err error) {
	return global.Install(ctx)
}

// Upgrade 更新模块
func (m *module) Upgrade(ctx context.Context) (err error) {
	return global.Upgrade(ctx)
}

// UnInstall 卸载模块
func (m *module) UnInstall(ctx context.Context) (err error) {
	return global.UnInstall(ctx)
}
