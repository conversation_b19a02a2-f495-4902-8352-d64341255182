// Package addons
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package addons

const (
	importModules = `// Package modules
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package modules

import _ "hotgo/addons/@{.name}"
`

	webApiLayout = `import { http } from '@/utils/http/axios';

export function getConfig(params) {
  return http.request({
    url: '/@{.name}/config/get',
    method: 'get',
    params,
  });
}

export function updateConfig(params) {
  return http.request({
    url: '/@{.name}/config/update',
    method: 'post',
    params,
  });
}
`

	webConfigBasicSetting = `<template>
  <div>
    <n-spin :show="show" description="请稍候...">
      <n-form :label-width="80" :model="formValue" :rules="rules" ref="formRef">
        <n-form-item label="测试参数" path="basicTest">
          <n-input v-model:value="formValue.basicTest" placeholder="请输入测试参数" />
          <template #feedback>
            这是一个测试参数，每个插件都可以有独立的配置项，可以按需添加</template
          >
        </n-form-item>

        <div>
          <n-space>
            <n-button type="primary" @click="formSubmit">保存更新</n-button>
          </n-space>
        </div>
      </n-form>
    </n-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useMessage } from 'naive-ui';
  import { getConfig, updateConfig } from '@/api/addons/@{.name}/config';

  const group = ref('basic');

  const show = ref(false);
  const rules = {
    basicTest: {
      required: true,
      message: '请输入测试参数',
      trigger: 'blur',
    },
  };

  const formRef: any = ref(null);
  const message = useMessage();

  const formValue = ref({
    basicTest: 'HotGo',
  });

  function formSubmit() {
    formRef.value.validate((errors) => {
      if (!errors) {
        updateConfig({ group: group.value, list: formValue.value }).then((_res) => {
          message.success('更新成功');
          load();
        });
      } else {
        message.error('验证失败，请填写完整信息');
      }
    });
  }

  onMounted(() => {
    load();
  });

  function load() {
    show.value = true;
    new Promise((_resolve, _reject) => {
      getConfig({ group: group.value })
        .then((res) => {
          formValue.value = res.list;
        })
        .finally(() => {
          show.value = false;
        });
    });
  }
</script>
`

	webConfigSystem = `<template>
  <div>
    <n-grid cols="24 300:1 600:24" :x-gap="12">
      <n-grid-item span="6">
        <n-card :bordered="false" size="small" class="proCard">
          <n-thing
            class="thing-cell"
            v-for="item in typeTabList"
            :key="item.key"
            :class="{ 'thing-cell-on': type === item.key }"
            @click="switchType(item)"
          >
            <template #header>{{ item.name }}</template>
            <template #description>{{ item.desc }}</template>
          </n-thing>
        </n-card>
      </n-grid-item>
      <n-grid-item span="18">
        <n-card :bordered="false" size="small" :title="typeTitle" class="proCard">
          <BasicSetting v-if="type === 1" />
        </n-card>
      </n-grid-item>
    </n-grid>
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive, toRefs } from 'vue';
  import BasicSetting from './BasicSetting.vue';
  const typeTabList = [
    {
      name: '基本设置',
      desc: '系统常规设置',
      key: 1,
    },
  ];
  export default defineComponent({
    components: {
      BasicSetting,
    },
    setup() {
      const state = reactive({
        type: 1,
        typeTitle: '基本设置',
      });

      function switchType(e) {
        state.type = e.key;
        state.typeTitle = e.name;
      }

      return {
        ...toRefs(state),
        switchType,
        typeTabList,
      };
    },
  });
</script>
<style lang="less" scoped>
  .thing-cell {
    margin: 0 -16px 10px;
    padding: 5px 16px;

    &:hover {
      background: #f3f3f3;
      cursor: pointer;
    }
  }

  .thing-cell-on {
    background: #f0faff;
    color: #2d8cf0;

    ::v-deep(.n-thing-main .n-thing-header .n-thing-header__title) {
      color: #2d8cf0;
    }

    &:hover {
      background: #f0faff;
    }
  }
</style>
`

	resourcePublicDefaultFile = "Hello！这是创建插件 [%v] 时默认生成的一个静态目录文件，用于测试，当你看到这个提示时，说明已经联调成功啦！"

	resourceTemplateHomeFile = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no">
    <meta name="keywords" content="@{.Keywords}"/>
    <meta name="description" content="@{.Description}"/>
    <title>@{.Title}</title>
    <script type="text/javascript" src="/resource/home/<USER>/jquery-3.6.0.min.js"></script>
    <style>
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #f6f6f6;
        }
    </style>
</head>
<body>
<div style="padding-top: 100px;text-align:center;">
    <h1><p>Hello，@{.Data.name}!!</p></h1>
    <h2><p>@{.Data.module}</p></h2>
    <h2><p>服务器时间：@{.Data.time}</p></h2>
</div>

</body>
<script>

</script>
</html>`
)
