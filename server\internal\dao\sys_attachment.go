// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysAttachmentDao is internal type for wrapping internal DAO implements.
type internalSysAttachmentDao = *internal.SysAttachmentDao

// sysAttachmentDao is the data access object for table hg_sys_attachment.
// You can define custom methods on it to extend its functionality as you wish.
type sysAttachmentDao struct {
	internalSysAttachmentDao
}

var (
	// SysAttachment is globally public accessible object for table hg_sys_attachment operations.
	SysAttachment = sysAttachmentDao{
		internal.NewSysAttachmentDao(),
	}
)

// Fill with you ideas below.
