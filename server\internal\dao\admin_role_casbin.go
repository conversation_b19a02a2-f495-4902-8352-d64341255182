// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalAdminRoleCasbinDao is internal type for wrapping internal DAO implements.
type internalAdminRoleCasbinDao = *internal.AdminRoleCasbinDao

// adminRoleCasbinDao is the data access object for table hg_admin_role_casbin.
// You can define custom methods on it to extend its functionality as you wish.
type adminRoleCasbinDao struct {
	internalAdminRoleCasbinDao
}

var (
	// AdminRoleCasbin is globally public accessible object for table hg_admin_role_casbin operations.
	AdminRoleCasbin = adminRoleCasbinDao{
		internal.NewAdminRoleCasbinDao(),
	}
)

// Fill with you ideas below.
