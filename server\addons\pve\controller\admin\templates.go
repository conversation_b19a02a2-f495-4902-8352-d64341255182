// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

var Templates = cTemplates{}

type cTemplates struct{}

// Create 创建模板
func (c *cTemplates) Create(ctx context.Context, req *admin.TemplateCreateReq) (res *admin.TemplateCreateRes, err error) {
	// 转换为服务层输入参数
	in := &input.TemplateCreateInp{
		Name:        req.Name,
		Description: req.Description,
		OsType:      req.OsType,
		OsVersion:   req.OsVersion,
		TemplateID:  req.TemplateID,
		NodeID:      req.NodeID,
		CpuCores:    req.CpuCores,
		MemoryMb:    req.MemoryMb,
		DiskGb:      req.DiskGb,
		Price:       req.Price,
		Status:      req.Status,
		Sort:        req.Sort,
	}
	
	out, err := service.PveTemplates().Create(ctx, in)
	if err != nil {
		return nil, err
	}
	
	res = &admin.TemplateCreateRes{
		ID: out.ID,
	}
	return
}

// Edit 编辑模板
func (c *cTemplates) Edit(ctx context.Context, req *admin.TemplateEditReq) (res *admin.TemplateEditRes, err error) {
	// 转换为服务层输入参数
	in := &input.TemplateEditInp{
		ID:          req.ID,
		Name:        req.Name,
		Description: req.Description,
		OsType:      req.OsType,
		OsVersion:   req.OsVersion,
		TemplateID:  req.TemplateID,
		NodeID:      req.NodeID,
		CpuCores:    req.CpuCores,
		MemoryMb:    req.MemoryMb,
		DiskGb:      req.DiskGb,
		Price:       req.Price,
		Status:      req.Status,
		Sort:        req.Sort,
	}
	
	err = service.PveTemplates().Edit(ctx, in)
	if err != nil {
		return nil, err
	}
	
	res = &admin.TemplateEditRes{}
	return
}

// Delete 删除模板
func (c *cTemplates) Delete(ctx context.Context, req *admin.TemplateDeleteReq) (res *admin.TemplateDeleteRes, err error) {
	data := &input.TemplateDeleteInp{
		ID: req.ID,
	}
	
	err = service.PveTemplates().Delete(ctx, data)
	if err != nil {
		return nil, err
	}
	
	res = &admin.TemplateDeleteRes{}
	return
}

// View 查看模板详情
func (c *cTemplates) View(ctx context.Context, req *admin.TemplateViewReq) (res *admin.TemplateViewRes, err error) {
	data := &input.TemplateViewInp{
		ID: req.ID,
	}
	
	out, err := service.PveTemplates().View(ctx, data)
	if err != nil {
		return nil, err
	}
	
	return &admin.TemplateViewRes{
		TemplateViewModel: &admin.TemplateViewModel{
			ID:          out.ID,
			Name:        out.Name,
			Description: out.Description,
			OsType:      out.OsType,
			OsVersion:   out.OsVersion,
			TemplateID:  out.TemplateID,
			NodeID:      out.NodeID,
			NodeName:    out.NodeName,
			CpuCores:    out.CpuCores,
			MemoryMb:    out.MemoryMb,
			DiskGb:      out.DiskGb,
			Price:       out.Price,
			Status:      out.Status,
			Sort:        out.Sort,
			CreatedAt:   out.CreatedAt,
			UpdatedAt:   out.UpdatedAt,
		},
	}, nil
}

// List 获取模板列表
func (c *cTemplates) List(ctx context.Context, req *admin.TemplateListReq) (res *admin.TemplateListRes, err error) {
	data := &input.TemplateListInp{
		PageReq:  req.PageReq,
		Name:     req.Name,
		OsType:   req.OsType,
		NodeID:   req.NodeID,
		Status:   req.Status,
		OrderBy:  req.OrderBy,
		OrderDir: req.OrderDir,
	}
	
	out, err := service.PveTemplates().List(ctx, data)
	if err != nil {
		return nil, err
	}
	
	var list []*admin.TemplateListModel
	for _, item := range out.List {
		list = append(list, &admin.TemplateListModel{
			ID:          item.ID,
			Name:        item.Name,
			Description: item.Description,
			OsType:      item.OsType,
			OsVersion:   item.OsVersion,
			TemplateID:  item.TemplateID,
			NodeID:      item.NodeID,
			NodeName:    item.NodeName,
			CpuCores:    item.CpuCores,
			MemoryMb:    item.MemoryMb,
			DiskGb:      item.DiskGb,
			Price:       item.Price,
			Status:      item.Status,
			Sort:        item.Sort,
			CreatedAt:   item.CreatedAt,
			UpdatedAt:   item.UpdatedAt,
		})
	}
	
	return &admin.TemplateListRes{
		PageRes: out.PageRes,
		List:    list,
	}, nil
}

// Sync 同步PVE模板
func (c *cTemplates) Sync(ctx context.Context, req *admin.TemplateSyncReq) (res *admin.TemplateSyncRes, err error) {
	data := &input.TemplateSyncInp{
		NodeID: req.NodeID,
	}
	
	out, err := service.PveTemplates().Sync(ctx, data)
	if err != nil {
		return nil, err
	}
	
	var list []*admin.PVETemplateModel
	for _, item := range out.List {
		list = append(list, &admin.PVETemplateModel{
			VMID:        item.VMID,
			Name:        item.Name,
			Description: item.Description,
			OsType:      item.OsType,
			Node:        item.Node,
			IsTemplate:  item.IsTemplate,
			Status:      item.Status,
		})
	}
	
	return &admin.TemplateSyncRes{
		Success: out.Success,
		Message: out.Message,
		Count:   out.Count,
		List:    list,
	}, nil
}

// Import 导入模板
func (c *cTemplates) Import(ctx context.Context, req *admin.TemplateImportReq) (res *admin.TemplateImportRes, err error) {
	var templates []*input.ImportTemplateData
	for _, template := range req.Templates {
		templates = append(templates, &input.ImportTemplateData{
			VMID:        template.VMID,
			Name:        template.Name,
			Description: template.Description,
			OsType:      template.OsType,
			OsVersion:   template.OsVersion,
			CpuCores:    template.CpuCores,
			MemoryMb:    template.MemoryMb,
			DiskGb:      template.DiskGb,
			Price:       template.Price,
			Status:      template.Status,
			Sort:        template.Sort,
		})
	}
	
	data := &input.TemplateImportInp{
		NodeID:    req.NodeID,
		Templates: templates,
	}
	
	out, err := service.PveTemplates().Import(ctx, data)
	if err != nil {
		return nil, err
	}
	
	return &admin.TemplateImportRes{
		Success:     out.Success,
		Message:     out.Message,
		ImportCount: out.ImportCount,
		FailedCount: out.FailedCount,
		FailedList:  out.FailedList,
	}, nil
}

// GetSelectList 获取模板选择列表
func (c *cTemplates) GetSelectList(ctx context.Context, req *admin.TemplateSelectReq) (res *admin.TemplateSelectRes, err error) {
	data := &input.TemplateSelectInp{
		NodeID: req.NodeID,
		Status: req.Status,
	}
	
	out, err := service.PveTemplates().GetSelectList(ctx, data)
	if err != nil {
		return nil, err
	}
	
	var list []*admin.TemplateSelectModel
	for _, item := range out.List {
		list = append(list, &admin.TemplateSelectModel{
			ID:          item.ID,
			Name:        item.Name,
			Description: item.Description,
			OsType:      item.OsType,
			OsVersion:   item.OsVersion,
			NodeID:      item.NodeID,
			NodeName:    item.NodeName,
			CpuCores:    item.CpuCores,
			MemoryMb:    item.MemoryMb,
			DiskGb:      item.DiskGb,
			Price:       item.Price,
		})
	}
	
	return &admin.TemplateSelectRes{
		List: list,
	}, nil
}