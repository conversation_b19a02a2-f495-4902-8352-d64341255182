'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-miterlimit': '10',
    'stroke-width': '32',
    d: 'M352 368h112V256'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    fill: 'none',
    stroke: 'currentColor',
    'stroke-linecap': 'square',
    'stroke-miterlimit': '10',
    'stroke-width': '32',
    d: 'M48 144l144 144l96-96l160 160'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = [_hoisted_2, _hoisted_3]
exports.default = (0, vue_1.defineComponent)({
  name: 'TrendingDownSharp',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_4)
  }
})
