## 模块介绍及目录

目录

- 模块介绍
- 启动流程
- 目录结构

### 模块介绍

> 定位：开发独立、临时性、工具类型的功能时推荐使用插件化开发，例如：小游戏(大转盘/消消乐/抽奖/大屏互动/红包等)、小插件(广告管理/文章管理/友情链接等等)、小模块(报名/投票/签到)、小程序、大型插件微商城等等。

> 插件模块方便多项目复用，同时完美支持多人协同开发，每个插件模块都有独立的微架构目录结构，多插件之间完全隔离。

### 启动流程

HotGo 入口文件->隐式注入(hotgo/addons/modules)->注册所有插件->初始化已安装的插件->写入路由组->根据 HotGo 正常的开发和访问流程去开发访问插件


### 目录结构
- 详细介绍请参考：[目录结构](sys-catalog.md)
```
/server
├── addons           
│   ├── modules        
│   ├── xxx插件 
│   |   ├── api
│   |   ├── consts
│   |   ├── controller
│   |   ├── crons
│   |   ├── global
│   |   ├── logic
│   |   ├── model
│   |   ├── queues
│   |   ├── resource
│   |   ├── router
│   |   ├── service
│   |   ├── main.go
│   |   └── README.md
```
