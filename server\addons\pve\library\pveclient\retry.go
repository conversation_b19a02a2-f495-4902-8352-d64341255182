// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// WaitForTask 等待任务完成
func (c *Client) WaitForTask(ctx context.Context, node, taskID string, timeout time.Duration) error {
	if timeout == 0 {
		timeout = 10 * time.Minute // 默认10分钟超时
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(2 * time.Second) // 每2秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("等待任务完成超时")
		case <-ticker.C:
			status, err := c.GetTaskStatus(timeoutCtx, node, taskID)
			if err != nil {
				g.Log().Warningf(ctx, "获取任务状态失败: %v", err)
				continue
			}

			g.Log().Debugf(ctx, "任务[%s]状态: %s", taskID, status.Status)

			switch status.Status {
			case "OK":
				return nil // 任务成功完成
			case "stopped":
				if status.ExitStatus == "OK" {
					return nil
				}
				return fmt.Errorf("任务执行失败: %s", status.ExitStatus)
			case "running":
				continue // 任务仍在运行
			default:
				g.Log().Warningf(ctx, "未知任务状态: %s", status.Status)
				continue
			}
		}
	}
}

// retryRequest 带重试的请求
func (c *Client) retryRequest(ctx context.Context, maxRetries int, fn func() error) error {
	var lastErr error
	for i := 0; i <= maxRetries; i++ {
		if i > 0 {
			// 指数退避
			waitTime := time.Duration(i*i) * time.Second
			g.Log().Warningf(ctx, "请求失败，%v后重试 (第%d/%d次)", waitTime, i, maxRetries)
			
			select {
			case <-time.After(waitTime):
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		if err := fn(); err != nil {
			lastErr = err
			if !isRetryableError(err) {
				return err // 不可重试的错误，直接返回
			}
			continue
		}
		return nil // 成功
	}
	return fmt.Errorf("重试%d次后仍失败: %v", maxRetries, lastErr)
}

// isRetryableError 判断错误是否可重试
func isRetryableError(err error) bool {
	errStr := err.Error()
	// 网络相关错误通常可重试
	if contains(errStr, "timeout") || 
	   contains(errStr, "connection") || 
	   contains(errStr, "temporary") ||
	   contains(errStr, "network") {
		return true
	}
	
	// HTTP 5xx 错误可重试
	if contains(errStr, "状态码: 5") {
		return true
	}

	return false
}

// contains 检查字符串是否包含子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr || 
		      indexContains(s, substr))))
}

// indexContains 简单的字符串包含检查
func indexContains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}