// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/internal/model/input/form"
)

// PVEUsersListReq 获取PVE用户列表请求
type PVEUsersListReq struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// PVEUsersListRes 获取PVE用户列表响应
type PVEUsersListRes struct {
	List []*PVEUserViewModel `json:"list" dc:"PVE用户列表"`
	form.PageRes
}

// PVEUserViewModel PVE用户视图模型
type PVEUserViewModel struct {
	ID        uint64 `json:"id" dc:"用户ID"`
	NodeID    int    `json:"nodeId" dc:"节点ID"`
	NodeName  string `json:"nodeName" dc:"节点名称"`
	Username  string `json:"username" dc:"用户名"`
	Email     string `json:"email" dc:"邮箱"`
	FullName  string `json:"fullName" dc:"全名"`
	Enabled   bool   `json:"enabled" dc:"是否启用"`
	Expire    string `json:"expire" dc:"过期时间"`
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	UpdatedAt string `json:"updatedAt" dc:"更新时间"`
}

// PVEUsersCreateReq 创建PVE用户请求
type PVEUsersCreateReq struct {
	NodeID   int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Username string `json:"username" v:"required#用户名不能为空" dc:"用户名"`
	Email    string `json:"email" dc:"邮箱"`
	FullName string `json:"fullName" dc:"全名"`
	Password string `json:"password" v:"required#密码不能为空" dc:"密码"`
	Enabled  bool   `json:"enabled" dc:"是否启用"`
	Expire   string `json:"expire" dc:"过期时间"`
}

// PVEUsersCreateRes 创建PVE用户响应
type PVEUsersCreateRes struct {
	ID uint64 `json:"id" dc:"用户ID"`
}

// PVEUsersEditReq 编辑PVE用户请求
type PVEUsersEditReq struct {
	ID       uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
	Email    string `json:"email" dc:"邮箱"`
	FullName string `json:"fullName" dc:"全名"`
	Password string `json:"password" dc:"密码"`
	Enabled  bool   `json:"enabled" dc:"是否启用"`
	Expire   string `json:"expire" dc:"过期时间"`
}

// PVEUsersEditRes 编辑PVE用户响应
type PVEUsersEditRes struct{}

// PVEUsersDeleteReq 删除PVE用户请求
type PVEUsersDeleteReq struct {
	ID uint64 `json:"id" v:"required#用户ID不能为空" dc:"用户ID"`
}

// PVEUsersDeleteRes 删除PVE用户响应
type PVEUsersDeleteRes struct{}

// PVEUsersGetRolesReq 获取用户角色请求
type PVEUsersGetRolesReq struct {
	UserID uint64 `json:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
}

// PVEUsersGetRolesRes 获取用户角色响应
type PVEUsersGetRolesRes struct {
	Roles []*PVEUserRole `json:"roles" dc:"用户角色列表"`
}

// PVEUserRole PVE用户角色
type PVEUserRole struct {
	ID          uint64 `json:"id" dc:"角色ID"`
	Name        string `json:"name" dc:"角色名称"`
	Description string `json:"description" dc:"角色描述"`
	Privileges  string `json:"privileges" dc:"权限"`
}

// PVEUsersCreateRoleReq 创建用户角色请求
type PVEUsersCreateRoleReq struct {
	UserID      uint64 `json:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	Name        string `json:"name" v:"required#角色名称不能为空" dc:"角色名称"`
	Description string `json:"description" dc:"角色描述"`
	Privileges  string `json:"privileges" dc:"权限"`
}

// PVEUsersCreateRoleRes 创建用户角色响应
type PVEUsersCreateRoleRes struct {
	ID uint64 `json:"id" dc:"角色ID"`
}

// PVEUsersEditRoleReq 编辑用户角色请求
type PVEUsersEditRoleReq struct {
	ID          uint64 `json:"id" v:"required#角色ID不能为空" dc:"角色ID"`
	Name        string `json:"name" dc:"角色名称"`
	Description string `json:"description" dc:"角色描述"`
	Privileges  string `json:"privileges" dc:"权限"`
}

// PVEUsersEditRoleRes 编辑用户角色响应
type PVEUsersEditRoleRes struct{}

// PVEUsersDeleteRoleReq 删除用户角色请求
type PVEUsersDeleteRoleReq struct {
	ID uint64 `json:"id" v:"required#角色ID不能为空" dc:"角色ID"`
}

// PVEUsersDeleteRoleRes 删除用户角色响应
type PVEUsersDeleteRoleRes struct{}
