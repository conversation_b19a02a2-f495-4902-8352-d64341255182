// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysBlacklistDao is internal type for wrapping internal DAO implements.
type internalSysBlacklistDao = *internal.SysBlacklistDao

// sysBlacklistDao is the data access object for table hg_sys_blacklist.
// You can define custom methods on it to extend its functionality as you wish.
type sysBlacklistDao struct {
	internalSysBlacklistDao
}

var (
	// SysBlacklist is globally public accessible object for table hg_sys_blacklist operations.
	SysBlacklist = sysBlacklistDao{
		internal.NewSysBlacklistDao(),
	}
)

// Fill with you ideas below.
