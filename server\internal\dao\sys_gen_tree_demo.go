// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysGenTreeDemoDao is internal type for wrapping internal DAO implements.
type internalSysGenTreeDemoDao = *internal.SysGenTreeDemoDao

// sysGenTreeDemoDao is the data access object for table hg_sys_gen_tree_demo.
// You can define custom methods on it to extend its functionality as you wish.
type sysGenTreeDemoDao struct {
	internalSysGenTreeDemoDao
}

var (
	// SysGenTreeDemo is globally public accessible object for table hg_sys_gen_tree_demo operations.
	SysGenTreeDemo = sysGenTreeDemoDao{
		internal.NewSysGenTreeDemoDao(),
	}
)

// Fill with you ideas below.
