# HotGo-PVE云平台插件化开发文档

## 📋 目录

- [插件架构设计](#插件架构设计)
- [环境要求](#环境要求)
- [插件目录结构](#插件目录结构)
- [开发环境搭建](#开发环境搭建)
- [PVE插件开发](#pve插件开发)
- [多租户权限设计](#多租户权限设计)
- [API接口设计](#api接口设计)
- [前端组件开发](#前端组件开发)
- [数据库设计](#数据库设计)
- [插件配置管理](#插件配置管理)
- [测试指南](#测试指南)
- [部署和发布](#部署和发布)
- [常见问题](#常见问题)

## 🏗️ 插件架构设计

### 插件化优势

**为什么选择插件模式？**
- **独立性**: PVE功能作为独立插件，不影响主系统稳定性
- **可扩展**: 支持多种虚拟化平台（PVE、VMware、KVM等）
- **模块化**: 功能模块清晰分离，便于开发和维护
- **可插拔**: 可根据需求启用/禁用特定功能
- **团队协作**: 支持多人并行开发，互不干扰
- **版本管理**: 插件独立版本控制和升级

### 插件架构图

```mermaid
graph TD
    A[HotGo主系统] --> B[插件管理器]
    B --> C[PVE插件]
    B --> D[其他虚拟化插件]
    
    C --> E[PVE管理模块]
    C --> F[计费模块]
    C --> G[监控模块]
    C --> H[用户门户模块]
    
    E --> I[节点管理]
    E --> J[实例管理]
    E --> K[模板管理]
    
    F --> L[订单系统]
    F --> M[计费引擎]
    F --> N[支付集成]
    
    G --> O[性能监控]
    G --> P[告警系统]
    G --> Q[日志分析]
```

### 技术架构层次

```yaml
插件技术栈:
  后端架构:
    框架: HotGo v2 插件系统
    语言: Go 1.24+
    ORM: GORM (通过GoFrame)
    消息队列: Redis/Kafka/RocketMQ
    缓存: Redis/Memory
    数据库: MySQL 8.0+
    
  前端架构:
    框架: Vue 3 + TypeScript
    UI库: Naive UI
    状态管理: Pinia
    构建工具: Vite
    
  虚拟化集成:
    主要平台: Proxmox VE 8.0+
    API库: go-proxmox
    监控: InfluxDB + Grafana
    网络: SDN集成
```

### 插件模块划分

```
PVE插件模块组成:
├── 核心管理模块 (pve-core)
│   ├── 节点管理
│   ├── 虚拟机管理
│   ├── 存储管理
│   └── 网络管理
├── 计费模块 (pve-billing)
│   ├── 资源计量
│   ├── 价格策略
│   ├── 订单处理
│   └── 发票管理
├── 监控模块 (pve-monitoring)
│   ├── 性能监控
│   ├── 资源统计
│   ├── 告警通知
│   └── 报表生成
├── 用户门户 (pve-portal)
│   ├── 自助服务
│   ├── 资源申请
│   ├── 使用统计
│   └── 工单系统
└── 运维模块 (pve-ops)
    ├── 自动化运维
    ├── 备份恢复
    ├── 安全扫描
    └── 容量规划
```

## 🛠️ 环境要求

### 基础环境
- **Node.js** >= v20.0.0
- **Go** >= v1.24
- **MySQL** >= 8.0
- **Redis** >= 7.0
- **Git** >= 2.30

### PVE环境要求
- **Proxmox VE** >= 8.0
- PVE节点需要开启API访问
- 具备管理员权限的API用户

### 开发工具推荐
- **后端IDE**: GoLand / VS Code + Go插件
- **前端IDE**: VS Code + Vetur/Volar
- **数据库工具**: Navicat / DataGrip
- **API测试**: Postman / Apifox
- **Git工具**: SourceTree / GitKraken

## 📁 插件目录结构

### HotGo插件标准结构

```
server/addons/pve/                    # PVE插件根目录
├── main.go                           # 插件入口文件
├── README.md                         # 插件说明文档
├── config.yaml                       # 插件配置文件
├── install.sql                       # 插件安装SQL
├── uninstall.sql                     # 插件卸载SQL
├── upgrade/                          # 插件升级脚本
│   ├── v1.0.1.sql
│   └── v1.1.0.sql
├── api/                              # API接口定义
│   ├── admin/                        # 管理后台API
│   │   ├── nodes/
│   │   ├── instances/
│   │   ├── templates/
│   │   ├── monitoring/
│   │   └── billing/
│   ├── user/                         # 用户端API
│   └── webhook/                      # 回调接口
├── controller/                       # 控制器层
│   ├── admin/
│   ├── user/
│   └── webhook/
├── logic/                            # 业务逻辑层
│   ├── core/                         # 核心管理逻辑
│   │   ├── nodes.go
│   │   ├── instances.go
│   │   ├── templates.go
│   │   └── networks.go
│   ├── billing/                      # 计费逻辑
│   │   ├── pricing.go
│   │   ├── orders.go
│   │   └── invoices.go
│   ├── monitoring/                   # 监控逻辑
│   │   ├── collector.go
│   │   ├── metrics.go
│   │   └── alerts.go
│   └── portal/                       # 用户门户逻辑
│       ├── dashboard.go
│       ├── tickets.go
│       └── reports.go
├── model/                            # 数据模型
│   ├── entity/                       # 数据实体
│   ├── input/                        # 输入模型
│   ├── output/                       # 输出模型
│   └── config/                       # 配置模型
├── service/                          # 服务接口
│   ├── core.go
│   ├── billing.go
│   ├── monitoring.go
│   └── portal.go
├── library/                          # 核心库
│   ├── pveclient/                    # PVE客户端
│   │   ├── client.go
│   │   ├── auth.go
│   │   ├── vm.go
│   │   ├── node.go
│   │   └── storage.go
│   ├── provider/                     # 虚拟化提供商抽象
│   │   ├── interface.go
│   │   ├── pve_provider.go
│   │   └── mock_provider.go
│   ├── billing/                      # 计费引擎
│   │   ├── engine.go
│   │   ├── calculator.go
│   │   └── scheduler.go
│   └── monitor/                      # 监控组件
│       ├── collector.go
│       ├── aggregator.go
│       └── alerter.go
├── router/                           # 路由注册
│   ├── admin.go
│   ├── user.go
│   └── webhook.go
├── crons/                            # 定时任务
│   ├── monitor_collector.go
│   ├── billing_calculator.go
│   └── system_cleaner.go
├── queues/                           # 消息队列
│   ├── vm_operations.go
│   ├── billing_events.go
│   └── notifications.go
├── global/                           # 全局变量和初始化
│   ├── global.go
│   ├── init.go
│   ├── install.go
│   ├── upgrade.go
│   └── uninstall.go
├── resource/                         # 静态资源
│   ├── template/                     # 模板文件
│   ├── i18n/                         # 国际化文件
│   └── assets/                       # 静态资源
└── hooks/                            # 生命周期钩子
    ├── install.go                    # 安装钩子
    ├── upgrade.go                    # 升级钩子
    ├── uninstall.go                  # 卸载钩子
    └── activate.go                   # 激活/停用钩子
```

### Web前端插件结构

```
web/src/views/addons/pve/             # PVE插件前端
├── index.vue                         # 插件首页
├── components/                       # 插件组件
│   ├── NodeCard.vue
│   ├── InstanceCard.vue
│   ├── MonitorChart.vue
│   └── BillingTable.vue
├── views/                            # 页面视图
│   ├── dashboard/                    # 仪表板
│   ├── nodes/                        # 节点管理
│   ├── instances/                    # 实例管理
│   ├── templates/                    # 模板管理
│   ├── monitoring/                   # 监控面板
│   ├── billing/                      # 计费管理
│   └── settings/                     # 设置页面
├── api/                              # API接口调用
│   ├── nodes.ts
│   ├── instances.ts
│   ├── billing.ts
│   └── monitoring.ts
├── store/                            # 状态管理
│   ├── nodes.ts
│   ├── instances.ts
│   └── monitoring.ts
├── types/                            # TypeScript类型
│   ├── nodes.ts
│   ├── instances.ts
│   └── common.ts
├── utils/                            # 工具函数
│   ├── format.ts
│   └── validate.ts
└── hooks/                            # Vue组合函数
    ├── useNodes.ts
    ├── useInstances.ts
    └── useMonitoring.ts
```

## 🚀 开发环境搭建

### 1. 克隆项目

```bash
# 克隆基础HotGo项目
git clone https://github.com/bufanyun/hotgo.git hotgo-pve
cd hotgo-pve

# 切换到开发分支
git checkout -b feature/pve-integration
```

### 2. 后端环境配置

#### 2.1 安装Go依赖

```bash
cd server

# 设置Go代理
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn

# 安装依赖
go mod tidy

# 安装PVE相关依赖包
go get github.com/luthermonson/go-proxmox
go get github.com/influxdata/influxdb-client-go/v2
```

#### 2.2 数据库初始化

```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE hotgo_pve CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 导入基础数据
mysql -u root -p hotgo_pve < storage/data/hotgo.sql

# 3. 导入PVE扩展表（稍后创建）
mysql -u root -p hotgo_pve < storage/data/pve/pve_tables.sql
```

#### 2.3 配置文件设置

```bash
# 复制配置文件
cp manifest/config/config.example.yaml manifest/config/config.yaml
```

修改 `manifest/config/config.yaml`：

```yaml
# 数据库配置
database:
  default:
    link: "mysql:root:password@tcp(127.0.0.1:3306)/hotgo_pve?loc=Local&parseTime=true&charset=utf8mb4"
    debug: true
    Prefix: "hg_"

# PVE配置
pve:
  # 默认PVE节点配置
  defaultNode:
    host: "*************"
    port: 8006
    username: "root@pam"
    password: "your_password"
    # 或使用API Token
    tokenID: "root@pam!hotgo"
    tokenSecret: "your-token-secret"
  
  # 监控配置
  monitoring:
    enabled: true
    interval: 30 # 监控数据采集间隔(秒)
    retention: 7 # 数据保留天数
  
  # 计费配置
  billing:
    currency: "CNY"
    defaultPricing:
      cpu: 0.5    # 每核心每小时价格
      memory: 0.1 # 每GB内存每小时价格
      storage: 0.01 # 每GB存储每小时价格

# Redis配置(用于缓存PVE数据)
redis:
  default:
    address: "127.0.0.1:6379"
    db: "3"
    pass: ""
```

### 3. 前端环境配置

```bash
cd ../web

# 安装依赖
pnpm install

# 添加PVE相关依赖
pnpm add @vueuse/core
pnpm add echarts
pnpm add xterm xterm-addon-fit
```

修改 `.env.development`：

```env
# API地址
VITE_API_URL=http://localhost:8000

# PVE相关配置
VITE_PVE_VNC_URL=https://pve.example.com:8006
VITE_PVE_ENABLE_CONSOLE=true
```

## 🔌 PVE集成开发

### 1. 创建PVE数据表

创建 `server/storage/data/pve/pve_tables.sql`：

```sql
-- PVE节点管理表
CREATE TABLE `hg_pve_nodes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `name` varchar(100) NOT NULL COMMENT '节点名称',
  `host` varchar(255) NOT NULL COMMENT '节点地址',
  `port` int DEFAULT 8006 COMMENT 'API端口',
  `username` varchar(100) DEFAULT NULL COMMENT '用户名',
  `password_hash` varchar(255) DEFAULT NULL COMMENT '密码哈希',
  `token_id` varchar(100) DEFAULT NULL COMMENT 'Token ID',
  `token_secret` varchar(500) DEFAULT NULL COMMENT 'Token Secret',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=在线,0=离线',
  `cpu_cores` int DEFAULT 0 COMMENT 'CPU核心数',
  `memory_total` bigint DEFAULT 0 COMMENT '总内存(MB)',
  `storage_total` bigint DEFAULT 0 COMMENT '总存储(GB)',
  `memory_used` bigint DEFAULT 0 COMMENT '已用内存(MB)',
  `storage_used` bigint DEFAULT 0 COMMENT '已用存储(GB)',
  `vm_count` int DEFAULT 0 COMMENT '虚拟机数量',
  `last_seen` datetime DEFAULT NULL COMMENT '最后在线时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_host_port` (`host`,`port`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='PVE节点表';

-- 虚拟机实例表
CREATE TABLE `hg_pve_instances` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '实例ID',
  `vmid` int NOT NULL COMMENT 'PVE虚拟机ID',
  `node_id` bigint unsigned NOT NULL COMMENT '所属节点ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '实例名称',
  `description` text COMMENT '实例描述',
  `os_template` varchar(100) DEFAULT NULL COMMENT '操作系统模板',
  `cpu_cores` int DEFAULT 1 COMMENT 'CPU核心数',
  `memory_mb` int DEFAULT 1024 COMMENT '内存大小(MB)',
  `disk_gb` int DEFAULT 20 COMMENT '磁盘大小(GB)',
  `network_config` json DEFAULT NULL COMMENT '网络配置',
  `status` varchar(20) DEFAULT 'stopped' COMMENT '状态:running,stopped,paused',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `vnc_port` int DEFAULT NULL COMMENT 'VNC端口',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '关联订单ID',
  `expired_at` datetime DEFAULT NULL COMMENT '到期时间',
  `auto_renew` tinyint DEFAULT 0 COMMENT '自动续费:1=是,0=否',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_node_vmid` (`node_id`,`vmid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expired` (`expired_at`)
) ENGINE=InnoDB COMMENT='PVE虚拟机实例表';

-- 监控数据表
CREATE TABLE `hg_pve_monitoring` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `instance_id` bigint unsigned NOT NULL COMMENT '实例ID',
  `node_id` bigint unsigned NOT NULL COMMENT '节点ID',
  `metric_type` varchar(50) NOT NULL COMMENT '监控类型:cpu,memory,disk,network',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_value` decimal(10,4) NOT NULL COMMENT '监控值',
  `metric_unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `timestamp` datetime NOT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_instance_type_time` (`instance_id`,`metric_type`,`timestamp`),
  KEY `idx_node_time` (`node_id`,`timestamp`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB COMMENT='PVE监控数据表';

-- 订单表
CREATE TABLE `hg_pve_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `product_id` bigint unsigned DEFAULT NULL COMMENT '产品ID',
  `instance_id` bigint unsigned DEFAULT NULL COMMENT '实例ID',
  `order_type` varchar(20) NOT NULL COMMENT '订单类型:new,renew,upgrade,downgrade',
  `config` json NOT NULL COMMENT '配置信息',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '货币',
  `period_type` varchar(10) NOT NULL COMMENT '计费周期:hour,day,month,year',
  `period_value` int NOT NULL COMMENT '周期数量',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态:pending,paid,cancelled,refunded',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `expired_at` datetime DEFAULT NULL COMMENT '到期时间',
  `notes` text COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB COMMENT='PVE订单表';

-- 计费记录表
CREATE TABLE `hg_pve_billing` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `instance_id` bigint unsigned NOT NULL COMMENT '实例ID',
  `billing_type` varchar(20) NOT NULL COMMENT '计费类型:hourly,daily,monthly,yearly',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型:vm,storage,network',
  `quantity` decimal(10,4) NOT NULL COMMENT '用量',
  `unit_price` decimal(10,6) NOT NULL COMMENT '单价',
  `amount` decimal(10,2) NOT NULL COMMENT '费用金额',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '货币',
  `billing_period` varchar(20) NOT NULL COMMENT '计费周期',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态:pending,paid,cancelled',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_instance_id` (`instance_id`),
  KEY `idx_billing_period` (`billing_period`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='PVE计费记录表';

-- 镜像模板表
CREATE TABLE `hg_pve_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '模板描述',
  `os_type` varchar(50) NOT NULL COMMENT '操作系统类型',
  `os_version` varchar(50) DEFAULT NULL COMMENT '操作系统版本',
  `architecture` varchar(20) DEFAULT 'x86_64' COMMENT '架构',
  `template_id` varchar(100) NOT NULL COMMENT '模板ID',
  `min_cpu` int DEFAULT 1 COMMENT '最小CPU核心数',
  `min_memory` int DEFAULT 512 COMMENT '最小内存(MB)',
  `min_disk` int DEFAULT 10 COMMENT '最小磁盘(GB)',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `is_public` tinyint DEFAULT 1 COMMENT '是否公开:1=是,0=否',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_id` (`template_id`),
  KEY `idx_os_type` (`os_type`),
  KEY `idx_status_public` (`status`,`is_public`)
) ENGINE=InnoDB COMMENT='PVE模板表';

-- 产品配置表
CREATE TABLE `hg_pve_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '产品名称',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '产品描述',
  `category` varchar(50) DEFAULT 'vm' COMMENT '产品分类',
  `cpu_cores` int NOT NULL COMMENT 'CPU核心数',
  `memory_mb` int NOT NULL COMMENT '内存大小(MB)',
  `disk_gb` int NOT NULL COMMENT '磁盘大小(GB)',
  `bandwidth_mbps` int DEFAULT 100 COMMENT '带宽(Mbps)',
  `hourly_price` decimal(10,6) DEFAULT NULL COMMENT '小时价格',
  `monthly_price` decimal(10,2) DEFAULT NULL COMMENT '月价格',
  `yearly_price` decimal(10,2) DEFAULT NULL COMMENT '年价格',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '货币',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门:1=是,0=否',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=上架,0=下架',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_status` (`category`,`status`)
) ENGINE=InnoDB COMMENT='PVE产品配置表';
```

### 2. PVE API客户端开发

基于Proxmox VE REST API (https://pve.proxmox.com/pve-docs/api-viewer/) 开发完整的API客户端。

#### PVE API认证机制

PVE支持多种认证方式：
1. **用户名/密码认证**: 通过 `/api2/json/access/ticket` 获取认证票据
2. **API Token认证**: 使用API Token直接调用 (推荐用于自动化)
3. **Cookie认证**: Web界面使用的Session Cookie

#### 完整的PVE API客户端实现

创建 `server/addons/pve/library/pveclient/client.go`：

```go
// Package pveclient 基于官方API文档的PVE客户端实现
package pveclient

import (
    "context"
    "crypto/tls"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "net/url"
    "strings"
    "time"

    "github.com/gogf/gf/v2/errors/gerror"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/util/gconv"
)

// PVEClient PVE API客户端
type PVEClient struct {
    baseURL    string
    httpClient *http.Client
    
    // 认证信息
    authType   AuthType
    ticket     string       // 票据认证
    csrfToken  string       // CSRF Token
    tokenID    string       // API Token ID  
    tokenSecret string      // API Token Secret
    username   string       // 用户名
    password   string       // 密码
    
    // 配置
    timeout    time.Duration
    retries    int
}

// AuthType 认证类型
type AuthType string

const (
    AuthTypeTicket   AuthType = "ticket"   // 票据认证
    AuthTypeToken    AuthType = "token"    // API Token认证
    AuthTypePassword AuthType = "password" // 用户名密码认证
)

// Config PVE客户端配置
type Config struct {
    Host        string        `yaml:"host" json:"host"`
    Port        int           `yaml:"port" json:"port"`
    Username    string        `yaml:"username" json:"username"`
    Password    string        `yaml:"password" json:"password"`
    TokenID     string        `yaml:"tokenID" json:"tokenID"`
    TokenSecret string        `yaml:"tokenSecret" json:"tokenSecret"`
    Insecure    bool          `yaml:"insecure" json:"insecure"`
    Timeout     time.Duration `yaml:"timeout" json:"timeout"`
    Retries     int           `yaml:"retries" json:"retries"`
}

// NewPVEClient 创建PVE客户端
func NewPVEClient(config *Config) (*PVEClient, error) {
    if config.Port == 0 {
        config.Port = 8006
    }
    if config.Timeout == 0 {
        config.Timeout = 30 * time.Second
    }
    if config.Retries == 0 {
        config.Retries = 3
    }

    baseURL := fmt.Sprintf("https://%s:%d/api2/json", config.Host, config.Port)
    
    // 创建HTTP客户端
    httpClient := &http.Client{
        Timeout: config.Timeout,
        Transport: &http.Transport{
            TLSClientConfig: &tls.Config{
                InsecureSkipVerify: config.Insecure,
            },
        },
    }

    client := &PVEClient{
        baseURL:    baseURL,
        httpClient: httpClient,
        timeout:    config.Timeout,
        retries:    config.Retries,
        username:   config.Username,
        password:   config.Password,
        tokenID:    config.TokenID,
        tokenSecret: config.TokenSecret,
    }

    // 选择认证方式
    if config.TokenID != "" && config.TokenSecret != "" {
        client.authType = AuthTypeToken
    } else if config.Username != "" && config.Password != "" {
        client.authType = AuthTypeTicket
        // 立即获取票据
        if err := client.authenticate(context.Background()); err != nil {
            return nil, gerror.Wrap(err, "PVE认证失败")
        }
    } else {
        return nil, gerror.New("必须提供API Token或用户名密码")
    }

    return client, nil
}

// authenticate 执行票据认证
func (c *PVEClient) authenticate(ctx context.Context) error {
    authData := map[string]string{
        "username": c.username,
        "password": c.password,
    }
    
    var response AuthResponse
    err := c.request(ctx, "POST", "/access/ticket", authData, &response)
    if err != nil {
        return gerror.Wrap(err, "获取认证票据失败")
    }
    
    c.ticket = response.Data.Ticket
    c.csrfToken = response.Data.CSRFPreventionToken
    
    return nil
}

// request 执行HTTP请求
func (c *PVEClient) request(ctx context.Context, method, path string, data interface{}, result interface{}) error {
    url := c.baseURL + path
    
    var body io.Reader
    if data != nil {
        switch method {
        case "GET", "DELETE":
            // GET/DELETE请求参数放在URL中
            if params, ok := data.(map[string]interface{}); ok {
                values := url.Values{}
                for k, v := range params {
                    values.Add(k, gconv.String(v))
                }
                if len(values) > 0 {
                    url += "?" + values.Encode()
                }
            }
        default:
            // POST/PUT请求参数放在Body中
            if params, ok := data.(map[string]interface{}); ok {
                values := url.Values{}
                for k, v := range params {
                    values.Add(k, gconv.String(v))
                }
                body = strings.NewReader(values.Encode())
            } else if str, ok := data.(string); ok {
                body = strings.NewReader(str)
            }
        }
    }
    
    req, err := http.NewRequestWithContext(ctx, method, url, body)
    if err != nil {
        return gerror.Wrap(err, "创建HTTP请求失败")
    }
    
    // 设置请求头
    req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
    req.Header.Set("Accept", "application/json")
    
    // 添加认证信息
    switch c.authType {
    case AuthTypeToken:
        // API Token认证
        req.Header.Set("Authorization", fmt.Sprintf("PVEAPIToken=%s=%s", c.tokenID, c.tokenSecret))
    case AuthTypeTicket:
        // 票据认证
        if c.ticket != "" {
            req.Header.Set("Cookie", fmt.Sprintf("PVEAuthCookie=%s", c.ticket))
            req.Header.Set("CSRFPreventionToken", c.csrfToken)
        }
    }
    
    // 执行请求
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return gerror.Wrap(err, "HTTP请求执行失败")
    }
    defer resp.Body.Close()
    
    // 读取响应
    respBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return gerror.Wrap(err, "读取响应失败")
    }
    
    // 检查HTTP状态码
    if resp.StatusCode >= 400 {
        return gerror.Newf("HTTP错误: %d, 响应: %s", resp.StatusCode, string(respBody))
    }
    
    // 解析JSON响应
    if result != nil {
        if err := json.Unmarshal(respBody, result); err != nil {
            return gerror.Wrap(err, "解析JSON响应失败")
        }
    }
    
    return nil
}

// AuthResponse 认证响应
type AuthResponse struct {
    Data struct {
        Ticket              string `json:"ticket"`
        Username            string `json:"username"`
        CSRFPreventionToken string `json:"CSRFPreventionToken"`
    } `json:"data"`
}

// GetVersion 获取PVE版本信息
func (c *PVEClient) GetVersion(ctx context.Context) (*VersionInfo, error) {
    var response APIResponse[VersionInfo]
    err := c.request(ctx, "GET", "/version", nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// GetClusterStatus 获取集群状态
func (c *PVEClient) GetClusterStatus(ctx context.Context) ([]ClusterNode, error) {
    var response APIResponse[[]ClusterNode]
    err := c.request(ctx, "GET", "/cluster/status", nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetNodes 获取节点列表
func (c *PVEClient) GetNodes(ctx context.Context) ([]NodeInfo, error) {
    var response APIResponse[[]NodeInfo]
    err := c.request(ctx, "GET", "/nodes", nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetNodeStatus 获取节点状态
func (c *PVEClient) GetNodeStatus(ctx context.Context, node string) (*NodeStatus, error) {
    path := fmt.Sprintf("/nodes/%s/status", node)
    var response APIResponse[NodeStatus]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// GetVMs 获取虚拟机列表
func (c *PVEClient) GetVMs(ctx context.Context, node string) ([]VMInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu", node)
    var response APIResponse[[]VMInfo]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetVMConfig 获取虚拟机配置
func (c *PVEClient) GetVMConfig(ctx context.Context, node string, vmid int) (*VMConfig, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vmid)
    var response APIResponse[VMConfig]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// GetVMStatus 获取虚拟机状态
func (c *PVEClient) GetVMStatus(ctx context.Context, node string, vmid int) (*VMStatus, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, vmid)
    var response APIResponse[VMStatus]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// CreateVM 创建虚拟机
func (c *PVEClient) CreateVM(ctx context.Context, node string, config *CreateVMRequest) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu", node)
    
    // 构建请求参数
    params := map[string]interface{}{
        "vmid":   config.VMID,
        "name":   config.Name,
        "cores":  config.Cores,
        "memory": config.Memory,
    }
    
    if config.Description != "" {
        params["description"] = config.Description
    }
    if config.Template != "" {
        params["template"] = config.Template
    }
    if config.Storage != "" {
        params["storage"] = config.Storage
    }
    if config.Network != "" {
        params["net0"] = config.Network
    }
    
    var response TaskResponse
    err := c.request(ctx, "POST", path, params, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID: response.Data,
        Node: node,
    }, nil
}

// StartVM 启动虚拟机
func (c *PVEClient) StartVM(ctx context.Context, node string, vmid int) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/start", node, vmid)
    
    var response TaskResponse
    err := c.request(ctx, "POST", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID: response.Data,
        Node: node,
    }, nil
}

// StopVM 停止虚拟机
func (c *PVEClient) StopVM(ctx context.Context, node string, vmid int) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/stop", node, vmid)
    
    var response TaskResponse
    err := c.request(ctx, "POST", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID: response.Data,
        Node: node,
    }, nil
}

// RebootVM 重启虚拟机
func (c *PVEClient) RebootVM(ctx context.Context, node string, vmid int) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/reboot", node, vmid)
    
    var response TaskResponse
    err := c.request(ctx, "POST", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID: response.Data,
        Node: node,
    }, nil
}

// DeleteVM 删除虚拟机
func (c *PVEClient) DeleteVM(ctx context.Context, node string, vmid int, purge bool) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d", node, vmid)
    
    params := map[string]interface{}{}
    if purge {
        params["purge"] = 1
    }
    
    var response TaskResponse
    err := c.request(ctx, "DELETE", path, params, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID: response.Data,
        Node: node,
    }, nil
}

// GetTask 获取任务状态
func (c *PVEClient) GetTask(ctx context.Context, node, upid string) (*TaskStatus, error) {
    path := fmt.Sprintf("/nodes/%s/tasks/%s/status", node, upid)
    
    var response APIResponse[TaskStatus]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    return &response.Data, nil
}

// WaitForTask 等待任务完成
func (c *PVEClient) WaitForTask(ctx context.Context, task *TaskInfo, checkInterval time.Duration, timeout time.Duration) error {
    if checkInterval == 0 {
        checkInterval = 2 * time.Second
    }
    if timeout == 0 {
        timeout = 5 * time.Minute
    }
    
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    ticker := time.NewTicker(checkInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-timeoutCtx.Done():
            return gerror.New("等待任务完成超时")
        case <-ticker.C:
            status, err := c.GetTask(timeoutCtx, task.Node, task.UPID)
            if err != nil {
                return gerror.Wrap(err, "获取任务状态失败")
            }
            
            switch status.Status {
            case "OK":
                return nil // 任务成功完成
            case "stopped":
                if status.ExitStatus == "OK" {
                    return nil
                }
                return gerror.Newf("任务执行失败: %s", status.ExitStatus)
            case "running":
                continue // 任务仍在运行
            default:
                return gerror.Newf("未知任务状态: %s", status.Status)
            }
        }
    }
}

// GetStorages 获取存储列表
func (c *PVEClient) GetStorages(ctx context.Context, node string) ([]StorageInfo, error) {
    path := fmt.Sprintf("/nodes/%s/storage", node)
    var response APIResponse[[]StorageInfo]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetNetworks 获取网络配置
func (c *PVEClient) GetNetworks(ctx context.Context, node string) ([]NetworkInfo, error) {
    path := fmt.Sprintf("/nodes/%s/network", node)
    var response APIResponse[[]NetworkInfo]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetTemplates 获取虚拟机模板列表
func (c *PVEClient) GetTemplates(ctx context.Context, node, storage string) ([]TemplateInfo, error) {
    path := fmt.Sprintf("/nodes/%s/storage/%s/content", node, storage)
    params := map[string]interface{}{
        "content": "vztmpl,iso",
    }
    
    var response APIResponse[[]TemplateInfo]
    err := c.request(ctx, "GET", path, params, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// 数据结构定义

// APIResponse 通用API响应结构
type APIResponse[T any] struct {
    Data T `json:"data"`
}

// TaskResponse 任务响应结构
type TaskResponse struct {
    Data string `json:"data"` // UPID
}

// VersionInfo 版本信息
type VersionInfo struct {
    Version string `json:"version"`
    Release string `json:"release"`
    RepoID  string `json:"repoid"`
}

// ClusterNode 集群节点信息
type ClusterNode struct {
    ID     string `json:"id"`
    Name   string `json:"name"`
    Type   string `json:"type"`
    IP     string `json:"ip,omitempty"`
    Level  string `json:"level,omitempty"`
    Local  int    `json:"local,omitempty"`
    NodeID int    `json:"nodeid,omitempty"`
    Online int    `json:"online,omitempty"`
}

// NodeInfo 节点基本信息
type NodeInfo struct {
    Node           string  `json:"node"`
    Status         string  `json:"status"`
    CPU            float64 `json:"cpu"`
    MaxCPU         int     `json:"maxcpu"`
    Memory         int64   `json:"mem"`
    MaxMemory      int64   `json:"maxmem"`
    Disk           int64   `json:"disk"`
    MaxDisk        int64   `json:"maxdisk"`
    Uptime         int64   `json:"uptime"`
    Level          string  `json:"level"`
    ID             string  `json:"id"`
    Type           string  `json:"type"`
    SSLFingerprint string  `json:"ssl_fingerprint,omitempty"`
}

// NodeStatus 节点详细状态
type NodeStatus struct {
    CPU        float64 `json:"cpu"`
    Memory     *Memory `json:"memory"`
    RootFS     *RootFS `json:"rootfs"`
    Swap       *Swap   `json:"swap"`
    PVEVersion string  `json:"pveversion"`
    Uptime     int64   `json:"uptime"`
    LoadAvg    []string `json:"loadavg"`
    CPUInfo    *CPUInfo `json:"cpuinfo"`
}

type Memory struct {
    Free  int64   `json:"free"`
    Total int64   `json:"total"`
    Used  int64   `json:"used"`
}

type RootFS struct {
    Avail int64 `json:"avail"`
    Free  int64 `json:"free"`
    Total int64 `json:"total"`
    Used  int64 `json:"used"`
}

type Swap struct {
    Free  int64 `json:"free"`
    Total int64 `json:"total"`
    Used  int64 `json:"used"`
}

type CPUInfo struct {
    CPUs    int    `json:"cpus"`
    Model   string `json:"model"`
    Sockets int    `json:"sockets"`
    Cores   int    `json:"cores"`
    MHz     string `json:"mhz"`
}

// VMInfo 虚拟机基本信息
type VMInfo struct {
    VMID       int     `json:"vmid"`
    Name       string  `json:"name"`
    Status     string  `json:"status"`
    MaxMem     int64   `json:"maxmem"`
    MaxDisk    int64   `json:"maxdisk"`
    CPU        float64 `json:"cpu"`
    CPUs       int     `json:"cpus"`
    Disk       int64   `json:"disk"`
    DiskRead   int64   `json:"diskread"`
    DiskWrite  int64   `json:"diskwrite"`
    Mem        int64   `json:"mem"`
    NetIn      int64   `json:"netin"`
    NetOut     int64   `json:"netout"`
    PID        int     `json:"pid"`
    Uptime     int64   `json:"uptime"`
    Template   int     `json:"template,omitempty"`
    Tags       string  `json:"tags,omitempty"`
}

// VMConfig 虚拟机配置
type VMConfig struct {
    VMID        int                    `json:"vmid"`
    Name        string                 `json:"name"`
    Description string                 `json:"description,omitempty"`
    OSType      string                 `json:"ostype,omitempty"`
    Cores       int                    `json:"cores"`
    Sockets     int                    `json:"sockets"`
    Memory      int                    `json:"memory"`
    Boot        string                 `json:"boot,omitempty"`
    Bootdisk    string                 `json:"bootdisk,omitempty"`
    Agent       interface{}            `json:"agent,omitempty"`
    Tablet      int                    `json:"tablet,omitempty"`
    VGA         string                 `json:"vga,omitempty"`
    OnBoot      int                    `json:"onboot,omitempty"`
    Template    int                    `json:"template,omitempty"`
    Tags        string                 `json:"tags,omitempty"`
    Args        string                 `json:"args,omitempty"`
    Meta        string                 `json:"meta,omitempty"`
    Disks       map[string]interface{} `json:"-"` // 动态磁盘配置
    Networks    map[string]interface{} `json:"-"` // 动态网络配置
    Options     map[string]interface{} `json:"-"` // 其他选项
}

// VMStatus 虚拟机状态
type VMStatus struct {
    VMID       int     `json:"vmid"`
    Status     string  `json:"status"`
    CPU        float64 `json:"cpu,omitempty"`
    CPUs       int     `json:"cpus,omitempty"`
    Disk       int64   `json:"disk,omitempty"`
    DiskRead   int64   `json:"diskread,omitempty"`
    DiskWrite  int64   `json:"diskwrite,omitempty"`
    Ha         *Ha     `json:"ha,omitempty"`
    MaxDisk    int64   `json:"maxdisk,omitempty"`
    MaxMem     int64   `json:"maxmem,omitempty"`
    Mem        int64   `json:"mem,omitempty"`
    Name       string  `json:"name,omitempty"`
    NetIn      int64   `json:"netin,omitempty"`
    NetOut     int64   `json:"netout,omitempty"`
    PID        int     `json:"pid,omitempty"`
    QMPStatus  string  `json:"qmpstatus,omitempty"`
    RunningQemu string `json:"running-qemu,omitempty"`
    RunningMachine string `json:"running-machine,omitempty"`
    Spice      int     `json:"spice,omitempty"`
    Tags       string  `json:"tags,omitempty"`
    Template   int     `json:"template,omitempty"`
    Uptime     int64   `json:"uptime,omitempty"`
    VNC        int     `json:"vnc,omitempty"`
}

type Ha struct {
    Managed int `json:"managed"`
}

// CreateVMRequest 创建虚拟机请求
type CreateVMRequest struct {
    VMID        int    `json:"vmid"`
    Name        string `json:"name"`
    Description string `json:"description,omitempty"`
    Cores       int    `json:"cores"`
    Memory      int    `json:"memory"`
    Template    string `json:"template,omitempty"`
    Storage     string `json:"storage,omitempty"`
    Network     string `json:"network,omitempty"`
    OSType      string `json:"ostype,omitempty"`
    Boot        string `json:"boot,omitempty"`
    Agent       int    `json:"agent,omitempty"`
}

// TaskInfo 任务信息
type TaskInfo struct {
    UPID string `json:"upid"`
    Node string `json:"node"`
}

// TaskStatus 任务状态
type TaskStatus struct {
    Node       string `json:"node"`
    PID        int    `json:"pid"`
    PSTART     int64  `json:"pstart"`
    StartTime  int64  `json:"starttime"`
    Type       string `json:"type"`
    ID         string `json:"id"`
    User       string `json:"user"`
    Status     string `json:"status"`
    ExitStatus string `json:"exitstatus,omitempty"`
    UPID       string `json:"upid"`
}

// StorageInfo 存储信息
type StorageInfo struct {
    Storage  string  `json:"storage"`
    Type     string  `json:"type"`
    Content  string  `json:"content"`
    Shared   int     `json:"shared,omitempty"`
    Active   int     `json:"active,omitempty"`
    Enabled  int     `json:"enabled,omitempty"`
    Total    int64   `json:"total,omitempty"`
    Used     int64   `json:"used,omitempty"`
    Avail    int64   `json:"avail,omitempty"`
    UsedFraction float64 `json:"used_fraction,omitempty"`
}

// NetworkInfo 网络信息
type NetworkInfo struct {
    Iface     string `json:"iface"`
    Type      string `json:"type"`
    Active    int    `json:"active,omitempty"`
    AutoStart int    `json:"autostart,omitempty"`
    Bridge    string `json:"bridge,omitempty"`
    CIDR      string `json:"cidr,omitempty"`
    Gateway   string `json:"gateway,omitempty"`
    Method    string `json:"method,omitempty"`
    Method6   string `json:"method6,omitempty"`
    Netmask   string `json:"netmask,omitempty"`
    Address   string `json:"address,omitempty"`
    Priority  int    `json:"priority,omitempty"`
}

// TemplateInfo 模板信息
type TemplateInfo struct {
    Content  string `json:"content"`
    Format   string `json:"format,omitempty"`
    Path     string `json:"path,omitempty"`
    Size     int64  `json:"size,omitempty"`
    Used     int64  `json:"used,omitempty"`
    VMID     int    `json:"vmid,omitempty"`
    VolID    string `json:"volid"`
    CTime    int64  `json:"ctime,omitempty"`
}
```

#### PVE API使用示例

以下是基于官方API文档的完整使用示例：

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"

    "hotgo/addons/pve/library/pveclient"
)

func main() {
    // 配置PVE连接
    config := &pveclient.Config{
        Host:        "*************",
        Port:        8006,
        TokenID:     "root@pam!hotgo",
        TokenSecret: "your-api-token-secret",
        Insecure:    true, // 跳过TLS验证（仅开发环境）
        Timeout:     30 * time.Second,
        Retries:     3,
    }

    // 创建客户端
    client, err := pveclient.NewPVEClient(config)
    if err != nil {
        log.Fatal("创建PVE客户端失败:", err)
    }

    ctx := context.Background()

    // 1. 获取PVE版本信息
    fmt.Println("=== 获取PVE版本信息 ===")
    version, err := client.GetVersion(ctx)
    if err != nil {
        log.Printf("获取版本信息失败: %v", err)
    } else {
        fmt.Printf("PVE版本: %s, 发布版本: %s\n", version.Version, version.Release)
    }

    // 2. 获取集群状态
    fmt.Println("\n=== 获取集群状态 ===")
    clusterNodes, err := client.GetClusterStatus(ctx)
    if err != nil {
        log.Printf("获取集群状态失败: %v", err)
    } else {
        for _, node := range clusterNodes {
            fmt.Printf("节点: %s, 类型: %s, 状态: %s\n", node.Name, node.Type, node.ID)
        }
    }

    // 3. 获取节点列表
    fmt.Println("\n=== 获取节点列表 ===")
    nodes, err := client.GetNodes(ctx)
    if err != nil {
        log.Printf("获取节点列表失败: %v", err)
        return
    }

    for _, node := range nodes {
        fmt.Printf("节点: %s, 状态: %s, CPU使用率: %.2f%%, 内存使用: %d/%d MB\n",
            node.Node, node.Status, node.CPU*100, node.Memory>>20, node.MaxMemory>>20)
    }

    if len(nodes) == 0 {
        log.Println("未发现任何节点")
        return
    }

    nodeName := nodes[0].Node

    // 4. 获取节点详细状态
    fmt.Printf("\n=== 获取节点[%s]详细状态 ===\n", nodeName)
    nodeStatus, err := client.GetNodeStatus(ctx, nodeName)
    if err != nil {
        log.Printf("获取节点状态失败: %v", err)
    } else {
        fmt.Printf("CPU使用率: %.2f%%, 内存: %d/%d MB, 运行时间: %d秒\n",
            nodeStatus.CPU*100, nodeStatus.Memory.Used>>20, nodeStatus.Memory.Total>>20, nodeStatus.Uptime)
    }

    // 5. 获取虚拟机列表
    fmt.Printf("\n=== 获取节点[%s]虚拟机列表 ===\n", nodeName)
    vms, err := client.GetVMs(ctx, nodeName)
    if err != nil {
        log.Printf("获取虚拟机列表失败: %v", err)
    } else {
        for _, vm := range vms {
            fmt.Printf("虚拟机 %d: %s, 状态: %s, CPU: %d核, 内存: %d MB\n",
                vm.VMID, vm.Name, vm.Status, vm.CPUs, vm.MaxMem>>20)
        }
    }

    // 6. 创建新的虚拟机
    fmt.Println("\n=== 创建新的虚拟机 ===")
    vmConfig := &pveclient.CreateVMRequest{
        VMID:        199,
        Name:        "test-vm-199",
        Description: "通过API创建的测试虚拟机",
        Cores:       2,
        Memory:      2048,
        OSType:      "l26",
        Agent:       1,
    }

    task, err := client.CreateVM(ctx, nodeName, vmConfig)
    if err != nil {
        log.Printf("创建虚拟机失败: %v", err)
    } else {
        fmt.Printf("虚拟机创建任务已提交, UPID: %s\n", task.UPID)
        
        // 等待任务完成
        err = client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
        if err != nil {
            log.Printf("等待虚拟机创建完成失败: %v", err)
        } else {
            fmt.Println("虚拟机创建成功!")
        }
    }

    // 7. 启动虚拟机
    vmid := 199
    fmt.Printf("\n=== 启动虚拟机 %d ===\n", vmid)
    task, err = client.StartVM(ctx, nodeName, vmid)
    if err != nil {
        log.Printf("启动虚拟机失败: %v", err)
    } else {
        fmt.Printf("虚拟机启动任务已提交, UPID: %s\n", task.UPID)
        
        err = client.WaitForTask(ctx, task, 2*time.Second, 2*time.Minute)
        if err != nil {
            log.Printf("等待虚拟机启动完成失败: %v", err)
        } else {
            fmt.Println("虚拟机启动成功!")
        }
    }

    // 8. 获取虚拟机状态
    fmt.Printf("\n=== 获取虚拟机 %d 状态 ===\n", vmid)
    vmStatus, err := client.GetVMStatus(ctx, nodeName, vmid)
    if err != nil {
        log.Printf("获取虚拟机状态失败: %v", err)
    } else {
        fmt.Printf("虚拟机状态: %s, CPU使用率: %.2f%%, 内存使用: %d/%d MB\n",
            vmStatus.Status, vmStatus.CPU*100, vmStatus.Mem>>20, vmStatus.MaxMem>>20)
    }

    // 9. 获取存储列表
    fmt.Printf("\n=== 获取节点[%s]存储列表 ===\n", nodeName)
    storages, err := client.GetStorages(ctx, nodeName)
    if err != nil {
        log.Printf("获取存储列表失败: %v", err)
    } else {
        for _, storage := range storages {
            if storage.Total > 0 {
                usedPercent := float64(storage.Used) / float64(storage.Total) * 100
                fmt.Printf("存储: %s, 类型: %s, 使用率: %.2f%% (%d/%d GB)\n",
                    storage.Storage, storage.Type, usedPercent, 
                    storage.Used>>30, storage.Total>>30)
            } else {
                fmt.Printf("存储: %s, 类型: %s, 状态: %s\n",
                    storage.Storage, storage.Type, getStorageStatus(storage.Active))
            }
        }
    }
}

func getStorageStatus(active int) string {
    if active == 1 {
        return "活跃"
    }
    return "非活跃"
}
```

#### 虚拟机生命周期管理

基于PVE API的完整虚拟机生命周期管理实现：

创建 `server/addons/pve/service/vm_lifecycle.go`：

```go
package service

import (
    "context"
    "fmt"
    "time"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    "hotgo/addons/pve/library/pveclient"
    "hotgo/addons/pve/model/entity"
)

type sVMLifecycle struct {
    client *pveclient.PVEClient
}

func VMLifecycle() *sVMLifecycle {
    return &sVMLifecycle{}
}

// SetClient 设置PVE客户端
func (s *sVMLifecycle) SetClient(client *pveclient.PVEClient) {
    s.client = client
}

// CreateVMFromTemplate 从模板创建虚拟机
func (s *sVMLifecycle) CreateVMFromTemplate(ctx context.Context, req *CreateVMFromTemplateReq) (*CreateVMResult, error) {
    // 1. 验证输入参数
    if err := s.validateCreateRequest(ctx, req); err != nil {
        return nil, err
    }

    // 2. 分配VMID
    vmid, err := s.allocateVMID(ctx, req.NodeName)
    if err != nil {
        return nil, gerror.Wrap(err, "分配VMID失败")
    }

    // 3. 从模板克隆虚拟机
    cloneReq := &pveclient.CloneVMRequest{
        NewID:       vmid,
        Name:        req.Name,
        Description: req.Description,
        Full:        true, // 完整克隆
        Target:      req.NodeName,
        Storage:     req.Storage,
    }

    task, err := s.client.CloneVM(ctx, req.TemplateNode, req.TemplateVMID, cloneReq)
    if err != nil {
        return nil, gerror.Wrap(err, "克隆虚拟机失败")
    }

    // 4. 等待克隆任务完成
    err = s.client.WaitForTask(ctx, task, 5*time.Second, 10*time.Minute)
    if err != nil {
        return nil, gerror.Wrap(err, "等待虚拟机克隆完成失败")
    }

    // 5. 配置虚拟机参数
    err = s.configureVM(ctx, req.NodeName, vmid, req.Config)
    if err != nil {
        return nil, gerror.Wrap(err, "配置虚拟机参数失败")
    }

    // 6. 更新数据库记录
    err = s.createVMRecord(ctx, req, vmid)
    if err != nil {
        return nil, gerror.Wrap(err, "创建虚拟机记录失败")
    }

    return &CreateVMResult{
        VMID:     vmid,
        Name:     req.Name,
        NodeName: req.NodeName,
        Status:   "stopped",
    }, nil
}

// StartVM 启动虚拟机
func (s *sVMLifecycle) StartVM(ctx context.Context, instanceID uint64) error {
    // 1. 获取虚拟机信息
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 2. 检查当前状态
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    if vmStatus.Status == "running" {
        return gerror.New("虚拟机已经在运行中")
    }

    // 3. 启动虚拟机
    task, err := s.client.StartVM(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "启动虚拟机失败")
    }

    // 4. 等待启动完成
    err = s.client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机启动完成失败")
    }

    // 5. 更新数据库状态
    err = s.updateInstanceStatus(ctx, instanceID, "running")
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机状态失败")
    }

    // 6. 记录操作日志
    s.logVMOperation(ctx, instanceID, "start", "虚拟机启动成功")

    return nil
}

// StopVM 停止虚拟机
func (s *sVMLifecycle) StopVM(ctx context.Context, instanceID uint64, force bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 检查当前状态
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    if vmStatus.Status == "stopped" {
        return gerror.New("虚拟机已经停止")
    }

    var task *pveclient.TaskInfo
    if force {
        // 强制停止
        task, err = s.client.StopVM(ctx, instance.NodeName, instance.VMID)
    } else {
        // 优雅关闭
        task, err = s.client.ShutdownVM(ctx, instance.NodeName, instance.VMID)
    }

    if err != nil {
        return gerror.Wrap(err, "停止虚拟机失败")
    }

    // 等待停止完成
    err = s.client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机停止完成失败")
    }

    // 更新状态
    err = s.updateInstanceStatus(ctx, instanceID, "stopped")
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机状态失败")
    }

    operation := "stop"
    if force {
        operation = "force_stop"
    }
    s.logVMOperation(ctx, instanceID, operation, "虚拟机停止成功")

    return nil
}

// RebootVM 重启虚拟机
func (s *sVMLifecycle) RebootVM(ctx context.Context, instanceID uint64, force bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    var task *pveclient.TaskInfo
    if force {
        // 强制重启
        task, err = s.client.ResetVM(ctx, instance.NodeName, instance.VMID)
    } else {
        // 优雅重启
        task, err = s.client.RebootVM(ctx, instance.NodeName, instance.VMID)
    }

    if err != nil {
        return gerror.Wrap(err, "重启虚拟机失败")
    }

    err = s.client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机重启完成失败")
    }

    operation := "reboot"
    if force {
        operation = "reset"
    }
    s.logVMOperation(ctx, instanceID, operation, "虚拟机重启成功")

    return nil
}

// DeleteVM 删除虚拟机
func (s *sVMLifecycle) DeleteVM(ctx context.Context, instanceID uint64, purge bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 1. 检查虚拟机状态，如果运行中则先停止
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    if vmStatus.Status == "running" {
        // 强制停止虚拟机
        err = s.StopVM(ctx, instanceID, true)
        if err != nil {
            return gerror.Wrap(err, "停止虚拟机失败")
        }
    }

    // 2. 删除虚拟机
    task, err := s.client.DeleteVM(ctx, instance.NodeName, instance.VMID, purge)
    if err != nil {
        return gerror.Wrap(err, "删除虚拟机失败")
    }

    err = s.client.WaitForTask(ctx, task, 2*time.Second, 10*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机删除完成失败")
    }

    // 3. 更新数据库状态
    err = s.updateInstanceStatus(ctx, instanceID, "deleted")
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机状态失败")
    }

    s.logVMOperation(ctx, instanceID, "delete", "虚拟机删除成功")

    return nil
}

// CreateSnapshot 创建快照
func (s *sVMLifecycle) CreateSnapshot(ctx context.Context, instanceID uint64, snapname, description string) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    task, err := s.client.CreateSnapshot(ctx, instance.NodeName, instance.VMID, snapname, description)
    if err != nil {
        return gerror.Wrap(err, "创建快照失败")
    }

    err = s.client.WaitForTask(ctx, task, 5*time.Second, 15*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待快照创建完成失败")
    }

    s.logVMOperation(ctx, instanceID, "snapshot", fmt.Sprintf("创建快照: %s", snapname))

    return nil
}

// RestoreSnapshot 恢复快照
func (s *sVMLifecycle) RestoreSnapshot(ctx context.Context, instanceID uint64, snapname string) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    task, err := s.client.RollbackSnapshot(ctx, instance.NodeName, instance.VMID, snapname)
    if err != nil {
        return gerror.Wrap(err, "恢复快照失败")
    }

    err = s.client.WaitForTask(ctx, task, 5*time.Second, 15*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待快照恢复完成失败")
    }

    s.logVMOperation(ctx, instanceID, "restore", fmt.Sprintf("恢复快照: %s", snapname))

    return nil
}

// MigrateVM 迁移虚拟机
func (s *sVMLifecycle) MigrateVM(ctx context.Context, instanceID uint64, targetNode string, online bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    migrateReq := &pveclient.MigrateVMRequest{
        Target: targetNode,
        Online: online,
    }

    task, err := s.client.MigrateVM(ctx, instance.NodeName, instance.VMID, migrateReq)
    if err != nil {
        return gerror.Wrap(err, "迁移虚拟机失败")
    }

    err = s.client.WaitForTask(ctx, task, 10*time.Second, 30*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机迁移完成失败")
    }

    // 更新节点信息
    err = s.updateInstanceNode(ctx, instanceID, targetNode)
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机节点信息失败")
    }

    migrationType := "offline"
    if online {
        migrationType = "online"
    }
    s.logVMOperation(ctx, instanceID, "migrate", fmt.Sprintf("%s迁移到节点: %s", migrationType, targetNode))

    return nil
}

// ResizeVM 调整虚拟机配置
func (s *sVMLifecycle) ResizeVM(ctx context.Context, instanceID uint64, config *ResizeVMConfig) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 构建配置更新参数
    updateParams := make(map[string]interface{})
    
    if config.Cores > 0 {
        updateParams["cores"] = config.Cores
    }
    if config.Memory > 0 {
        updateParams["memory"] = config.Memory
    }
    if config.Balloon > 0 {
        updateParams["balloon"] = config.Balloon
    }

    err = s.client.UpdateVMConfig(ctx, instance.NodeName, instance.VMID, updateParams)
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机配置失败")
    }

    // 更新数据库记录
    err = s.updateInstanceConfig(ctx, instanceID, config)
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机配置记录失败")
    }

    s.logVMOperation(ctx, instanceID, "resize", "调整虚拟机配置成功")

    return nil
}

// 辅助方法

func (s *sVMLifecycle) validateCreateRequest(ctx context.Context, req *CreateVMFromTemplateReq) error {
    if req.Name == "" {
        return gerror.New("虚拟机名称不能为空")
    }
    if req.NodeName == "" {
        return gerror.New("节点名称不能为空")
    }
    if req.TemplateVMID == 0 {
        return gerror.New("模板VMID不能为空")
    }
    return nil
}

func (s *sVMLifecycle) allocateVMID(ctx context.Context, nodeName string) (int, error) {
    // 获取节点上已使用的VMID列表
    vms, err := s.client.GetVMs(ctx, nodeName)
    if err != nil {
        return 0, err
    }

    usedIDs := make(map[int]bool)
    for _, vm := range vms {
        usedIDs[vm.VMID] = true
    }

    // 从100开始查找可用VMID
    for vmid := 100; vmid < 999999; vmid++ {
        if !usedIDs[vmid] {
            return vmid, nil
        }
    }

    return 0, gerror.New("无可用的VMID")
}

func (s *sVMLifecycle) configureVM(ctx context.Context, nodeName string, vmid int, config *VMConfig) error {
    updateParams := make(map[string]interface{})
    
    if config.Cores > 0 {
        updateParams["cores"] = config.Cores
    }
    if config.Memory > 0 {
        updateParams["memory"] = config.Memory
    }
    if config.OSType != "" {
        updateParams["ostype"] = config.OSType
    }
    if config.Agent {
        updateParams["agent"] = 1
    }

    if len(updateParams) > 0 {
        return s.client.UpdateVMConfig(ctx, nodeName, vmid, updateParams)
    }
    
    return nil
}

func (s *sVMLifecycle) getInstanceInfo(ctx context.Context, instanceID uint64) (*InstanceInfo, error) {
    var instance entity.PveInstances
    err := g.DB().Model("pve_instances").Where("id", instanceID).Scan(&instance)
    if err != nil {
        return nil, gerror.Wrap(err, "查询虚拟机实例失败")
    }

    if instance.Id == 0 {
        return nil, gerror.New("虚拟机实例不存在")
    }

    // 获取节点名称
    var node entity.PveNodes
    err = g.DB().Model("pve_nodes").Where("id", instance.NodeId).Scan(&node)
    if err != nil {
        return nil, gerror.Wrap(err, "查询节点信息失败")
    }

    return &InstanceInfo{
        ID:       instance.Id,
        VMID:     instance.Vmid,
        Name:     instance.Name,
        NodeName: node.Name,
        Status:   instance.Status,
    }, nil
}

func (s *sVMLifecycle) updateInstanceStatus(ctx context.Context, instanceID uint64, status string) error {
    _, err := g.DB().Model("pve_instances").
        Where("id", instanceID).
        Update(g.Map{"status": status})
    return err
}

func (s *sVMLifecycle) updateInstanceNode(ctx context.Context, instanceID uint64, nodeName string) error {
    // 获取节点ID
    var nodeID uint64
    err := g.DB().Model("pve_nodes").Where("name", nodeName).Value("id", &nodeID)
    if err != nil {
        return err
    }

    _, err = g.DB().Model("pve_instances").
        Where("id", instanceID).
        Update(g.Map{"node_id": nodeID})
    return err
}

func (s *sVMLifecycle) updateInstanceConfig(ctx context.Context, instanceID uint64, config *ResizeVMConfig) error {
    updateData := g.Map{}
    if config.Cores > 0 {
        updateData["cpu_cores"] = config.Cores
    }
    if config.Memory > 0 {
        updateData["memory_mb"] = config.Memory
    }

    if len(updateData) > 0 {
        _, err := g.DB().Model("pve_instances").
            Where("id", instanceID).
            Update(updateData)
        return err
    }
    return nil
}

func (s *sVMLifecycle) createVMRecord(ctx context.Context, req *CreateVMFromTemplateReq, vmid int) error {
    // 获取节点ID
    var nodeID uint64
    err := g.DB().Model("pve_nodes").Where("name", req.NodeName).Value("id", &nodeID)
    if err != nil {
        return err
    }

    data := g.Map{
        "vmid":        vmid,
        "node_id":     nodeID,
        "user_id":     req.UserID,
        "name":        req.Name,
        "description": req.Description,
        "cpu_cores":   req.Config.Cores,
        "memory_mb":   req.Config.Memory,
        "status":      "stopped",
    }

    _, err = g.DB().Model("pve_instances").Insert(data)
    return err
}

func (s *sVMLifecycle) logVMOperation(ctx context.Context, instanceID uint64, operation, description string) {
    // 记录虚拟机操作日志
    g.Log().Infof(ctx, "虚拟机[%d]操作: %s - %s", instanceID, operation, description)
}

// 数据结构定义

type CreateVMFromTemplateReq struct {
    UserID       uint64    `json:"userId"`
    Name         string    `json:"name"`
    Description  string    `json:"description"`
    NodeName     string    `json:"nodeName"`
    TemplateNode string    `json:"templateNode"`
    TemplateVMID int       `json:"templateVMID"`
    Storage      string    `json:"storage"`
    Config       *VMConfig `json:"config"`
}

type VMConfig struct {
    Cores  int    `json:"cores"`
    Memory int    `json:"memory"`
    OSType string `json:"ostype"`
    Agent  bool   `json:"agent"`
}

type CreateVMResult struct {
    VMID     int    `json:"vmid"`
    Name     string `json:"name"`
    NodeName string `json:"nodeName"`
    Status   string `json:"status"`
}

type ResizeVMConfig struct {
    Cores   int `json:"cores"`
    Memory  int `json:"memory"`
    Balloon int `json:"balloon"`
}

type InstanceInfo struct {
    ID       uint64 `json:"id"`
    VMID     int    `json:"vmid"`
    Name     string `json:"name"`
    NodeName string `json:"nodeName"`
    Status   string `json:"status"`
}

### 3. 生成DAO和Model

```bash
cd server

# 生成PVE相关的DAO和Model
gf gen dao -c hack/config.yaml

# 手动更新tables配置，只生成PVE相关表
# 编辑 hack/config.yaml，在dao配置中添加tables过滤
```

修改 `hack/config.yaml`：

```yaml
gfcli:
  gen:
    dao:
      - link: "mysql:root:password@tcp(127.0.0.1:3306)/hotgo_pve"
        group: "default"
        tables: "hg_pve_nodes,hg_pve_instances,hg_pve_monitoring,hg_pve_orders,hg_pve_billing,hg_pve_templates,hg_pve_products"
        removePrefix: "hg_"
        descriptionTag: true
        noModelComment: true
        jsonCase: "CamelLower"
        gJsonSupport: true
```

### 4. 启动开发环境

```bash
# 后端启动
cd server
go run main.go

# 前端启动（新终端）
cd web
pnpm run dev
```

## 📝 API开发规范

### 接口定义结构

```go
// server/api/admin/pve/instances.go
package pve

import (
    "github.com/gogf/gf/v2/frame/g"
    "hotgo/internal/model/input/form"
)

// InstanceCreateReq 创建实例请求
type InstanceCreateReq struct {
    g.Meta      `path:"/instances" method:"post" summary:"创建虚拟机实例" tags:"PVE管理"`
    NodeID      uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"PVE节点ID"`
    Name        string `json:"name" v:"required|length:1,100#实例名称不能为空|名称长度为1-100个字符" dc:"实例名称"`
    Description string `json:"description" dc:"实例描述"`
    TemplateID  uint64 `json:"templateId" v:"required#模板ID不能为空" dc:"系统模板ID"`
    ProductID   uint64 `json:"productId" v:"required#产品ID不能为空" dc:"产品配置ID"`
    Period      int    `json:"period" v:"required|min:1#购买时长不能为空|购买时长最少1个周期" dc:"购买时长"`
    PeriodType  string `json:"periodType" v:"required|in:month,year#计费周期不能为空|计费周期只能是month或year" dc:"计费周期"`
    AutoRenew   bool   `json:"autoRenew" dc:"是否自动续费"`
}

type InstanceCreateRes struct {
    InstanceID uint64 `json:"instanceId" dc:"实例ID"`
    VMID       int    `json:"vmid" dc:"PVE虚拟机ID"`
    OrderID    uint64 `json:"orderId" dc:"订单ID"`
}

// InstanceListReq 实例列表请求
type InstanceListReq struct {
    g.Meta `path:"/instances" method:"get" summary:"获取虚拟机实例列表" tags:"PVE管理"`
    form.PageReq
    UserID   uint64 `json:"userId" dc:"用户ID"`
    NodeID   uint64 `json:"nodeId" dc:"节点ID"`
    Status   string `json:"status" dc:"实例状态"`
    Keyword  string `json:"keyword" dc:"搜索关键词"`
    OrderBy  string `json:"orderBy" dc:"排序字段"`
    OrderDir string `json:"orderDir" dc:"排序方向"`
}

type InstanceListRes struct {
    form.PageRes
    List []*InstanceListModel `json:"list" dc:"实例列表"`
}

type InstanceListModel struct {
    ID          uint64 `json:"id" dc:"实例ID"`
    VMID        int    `json:"vmid" dc:"PVE虚拟机ID"`
    NodeID      uint64 `json:"nodeId" dc:"节点ID"`
    NodeName    string `json:"nodeName" dc:"节点名称"`
    UserID      uint64 `json:"userId" dc:"用户ID"`
    Username    string `json:"username" dc:"用户名"`
    Name        string `json:"name" dc:"实例名称"`
    Description string `json:"description" dc:"实例描述"`
    OSTemplate  string `json:"osTemplate" dc:"操作系统模板"`
    CPUCores    int    `json:"cpuCores" dc:"CPU核心数"`
    MemoryMB    int    `json:"memoryMb" dc:"内存大小(MB)"`
    DiskGB      int    `json:"diskGb" dc:"磁盘大小(GB)"`
    Status      string `json:"status" dc:"实例状态"`
    IPAddress   string `json:"ipAddress" dc:"IP地址"`
    ExpiredAt   string `json:"expiredAt" dc:"到期时间"`
    CreatedAt   string `json:"createdAt" dc:"创建时间"`
    UpdatedAt   string `json:"updatedAt" dc:"更新时间"`
}

// InstanceActionReq 实例操作请求
type InstanceActionReq struct {
    g.Meta `path:"/instances/{id}/action" method:"post" summary:"执行虚拟机操作" tags:"PVE管理"`
    ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
    Action string `json:"action" v:"required|in:start,stop,reboot,reset,delete#操作类型不能为空|操作类型无效" dc:"操作类型"`
    Force  bool   `json:"force" dc:"是否强制执行"`
}

type InstanceActionRes struct {
    TaskID string `json:"taskId" dc:"任务ID"`
    Status string `json:"status" dc:"执行状态"`
}
```

## 🎨 前端开发指南

### 组件开发规范

创建 `web/src/components/pve/InstanceCard.vue`：

```vue
<template>
  <n-card 
    :title="instance.name" 
    hoverable
    class="instance-card"
    :class="statusClass"
  >
    <template #header-extra>
      <n-tag :type="statusTagType" size="small">
        {{ statusText }}
      </n-tag>
    </template>

    <div class="instance-info">
      <n-descriptions label-placement="left" :column="1" size="small">
        <n-descriptions-item label="配置">
          {{ instance.cpuCores }}核 {{ formatMemory(instance.memoryMb) }} {{ formatStorage(instance.diskGb) }}
        </n-descriptions-item>
        <n-descriptions-item label="IP地址">
          <n-text code>{{ instance.ipAddress || '暂无' }}</n-text>
        </n-descriptions-item>
        <n-descriptions-item label="所属节点">
          {{ instance.nodeName }}
        </n-descriptions-item>
        <n-descriptions-item label="到期时间">
          <n-text :type="expiredType">{{ formatExpiredTime }}</n-text>
        </n-descriptions-item>
      </n-descriptions>
    </div>

    <template #action>
      <n-space justify="space-between">
        <n-space>
          <n-button 
            v-if="instance.status === 'stopped'"
            type="success" 
            size="small"
            :loading="loading.start"
            @click="handleAction('start')"
          >
            启动
          </n-button>
          <n-button 
            v-if="instance.status === 'running'"
            type="warning" 
            size="small"
            :loading="loading.stop"
            @click="handleAction('stop')"
          >
            停止
          </n-button>
          <n-button 
            v-if="instance.status === 'running'"
            size="small"
            :loading="loading.reboot"
            @click="handleAction('reboot')"
          >
            重启
          </n-button>
          <n-button 
            size="small"
            @click="openConsole"
          >
            控制台
          </n-button>
        </n-space>
        
        <n-dropdown 
          :options="moreActions" 
          @select="handleMoreAction"
        >
          <n-button size="small" quaternary>
            <template #icon>
              <n-icon :component="MoreOutlined" />
            </template>
          </n-button>
        </n-dropdown>
      </n-space>
    </template>
  </n-card>
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue'
import { useMessage } from 'naive-ui'
import { MoreOutlined } from '@vicons/antd'
import { formatBytes, formatDate } from '@/utils'
import { instanceAction } from '@/api/pve/instances'

interface Props {
  instance: InstanceModel
}

interface InstanceModel {
  id: number
  vmid: number
  nodeId: number
  nodeName: string
  userId: number
  username: string
  name: string
  description: string
  osTemplate: string
  cpuCores: number
  memoryMb: number
  diskGb: number
  status: 'running' | 'stopped' | 'paused'
  ipAddress: string
  expiredAt: string
  createdAt: string
  updatedAt: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
  console: [instanceId: number]
}>()

const message = useMessage()

const loading = reactive({
  start: false,
  stop: false,
  reboot: false
})

// 状态相关计算属性
const statusClass = computed(() => {
  return `instance-card--${props.instance.status}`
})

const statusTagType = computed(() => {
  switch (props.instance.status) {
    case 'running': return 'success'
    case 'stopped': return 'default'
    case 'paused': return 'warning'
    default: return 'error'
  }
})

const statusText = computed(() => {
  switch (props.instance.status) {
    case 'running': return '运行中'
    case 'stopped': return '已停止'
    case 'paused': return '已暂停'
    default: return '未知'
  }
})

const expiredType = computed(() => {
  const expiredAt = new Date(props.instance.expiredAt)
  const now = new Date()
  const diff = expiredAt.getTime() - now.getTime()
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
  
  if (days < 0) return 'error'
  if (days <= 7) return 'warning'
  return 'default'
})

const formatExpiredTime = computed(() => {
  return formatDate(props.instance.expiredAt)
})

// 格式化函数
const formatMemory = (mb: number) => {
  return formatBytes(mb * 1024 * 1024)
}

const formatStorage = (gb: number) => {
  return formatBytes(gb * 1024 * 1024 * 1024)
}

// 更多操作菜单
const moreActions = [
  { label: '重置系统', key: 'reset' },
  { label: '创建快照', key: 'snapshot' },
  { label: '升级配置', key: 'upgrade' },
  { label: '续费', key: 'renew' },
  { label: '删除', key: 'delete', props: { style: 'color: red;' } }
]

// 处理实例操作
const handleAction = async (action: string) => {
  loading[action] = true
  
  try {
    await instanceAction(props.instance.id, { action })
    message.success(`${action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启'}操作已提交`)
    emit('refresh')
  } catch (error) {
    message.error(`操作失败: ${error.message}`)
  } finally {
    loading[action] = false
  }
}

// 处理更多操作
const handleMoreAction = (key: string) => {
  switch (key) {
    case 'reset':
      // 重置系统逻辑
      break
    case 'snapshot':
      // 创建快照逻辑
      break
    case 'upgrade':
      // 升级配置逻辑
      break
    case 'renew':
      // 续费逻辑
      break
    case 'delete':
      // 删除实例逻辑
      break
  }
}

// 打开控制台
const openConsole = () => {
  emit('console', props.instance.id)
}
</script>

<style lang="less" scoped>
.instance-card {
  &--running {
    border-left: 3px solid #52c41a;
  }
  
  &--stopped {
    border-left: 3px solid #d9d9d9;
  }
  
  &--paused {
    border-left: 3px solid #faad14;
  }
}

.instance-info {
  margin: 12px 0;
}
</style>
```

### 状态管理

创建 `web/src/store/modules/pve.ts`：

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { InstanceModel, NodeModel, MonitoringData } from '@/types/pve'
import { getInstanceList, getNodeList, getMonitoringData } from '@/api/pve'

export const usePVEStore = defineStore('pve', () => {
  // 状态
  const instances = ref<InstanceModel[]>([])
  const nodes = ref<NodeModel[]>([])
  const monitoring = ref<Record<number, MonitoringData>>({})
  const loading = ref({
    instances: false,
    nodes: false,
    monitoring: false
  })

  // 计算属性
  const runningInstances = computed(() => 
    instances.value.filter(instance => instance.status === 'running')
  )

  const totalResources = computed(() => {
    return instances.value.reduce((total, instance) => ({
      cpu: total.cpu + instance.cpuCores,
      memory: total.memory + instance.memoryMb,
      storage: total.storage + instance.diskGb
    }), { cpu: 0, memory: 0, storage: 0 })
  })

  const onlineNodes = computed(() =>
    nodes.value.filter(node => node.status === 1)
  )

  // 方法
  const fetchInstances = async (params = {}) => {
    loading.value.instances = true
    try {
      const result = await getInstanceList(params)
      instances.value = result.data.list
      return result
    } finally {
      loading.value.instances = false
    }
  }

  const fetchNodes = async () => {
    loading.value.nodes = true
    try {
      const result = await getNodeList()
      nodes.value = result.data.list
      return result
    } finally {
      loading.value.nodes = false
    }
  }

  const fetchMonitoring = async (instanceId: number) => {
    loading.value.monitoring = true
    try {
      const result = await getMonitoringData(instanceId)
      monitoring.value[instanceId] = result.data
      return result
    } finally {
      loading.value.monitoring = false
    }
  }

  const updateInstanceStatus = (instanceId: number, status: string) => {
    const instance = instances.value.find(item => item.id === instanceId)
    if (instance) {
      instance.status = status
    }
  }

  const removeInstance = (instanceId: number) => {
    const index = instances.value.findIndex(item => item.id === instanceId)
    if (index > -1) {
      instances.value.splice(index, 1)
    }
  }

  return {
    // 状态
    instances,
    nodes,
    monitoring,
    loading,
    
    // 计算属性
    runningInstances,
    totalResources,
    onlineNodes,
    
    // 方法
    fetchInstances,
    fetchNodes,
    fetchMonitoring,
    updateInstanceStatus,
    removeInstance
  }
})
```

## 🔧 插件配置管理

### 配置文件结构

#### 插件主配置文件

创建 `server/addons/pve/config.yaml`：

```yaml
# PVE插件配置文件
name: "pve"
title: "PVE虚拟化管理插件"
description: "基于Proxmox VE的虚拟化资源管理和计费系统"
version: "1.0.0"
author: "HotGo Team"
website: "https://github.com/bufanyun/hotgo"
dependencies:
  hotgo: ">=2.0.0"
  go: ">=1.24"
  mysql: ">=8.0"
  redis: ">=7.0"

# 插件功能模块
modules:
  core:
    enabled: true
    description: "核心虚拟化管理功能"
  billing:
    enabled: true
    description: "计费和订单管理"
  monitoring:
    enabled: true
    description: "性能监控和告警"
  portal:
    enabled: true
    description: "用户自助服务门户"

# 数据库配置
database:
  tables:
    - "pve_nodes"
    - "pve_instances" 
    - "pve_monitoring"
    - "pve_orders"
    - "pve_billing"
    - "pve_templates"
    - "pve_products"

# API路由配置
routes:
  admin: "/admin/pve"
  user: "/user/pve"
  webhook: "/webhook/pve"

# 权限配置
permissions:
  admin:
    - "pve:node:*"
    - "pve:instance:*"
    - "pve:template:*"
    - "pve:product:*"
    - "pve:billing:*"
    - "pve:monitoring:*"
  user:
    - "pve:instance:view"
    - "pve:instance:create"
    - "pve:instance:control"
    - "pve:billing:view"
    - "pve:monitoring:view"

# 默认配置
defaults:
  pve:
    api_timeout: 30
    retry_count: 3
    retry_delay: 5
  monitoring:
    interval: 30
    retention_days: 7
    batch_size: 100
  billing:
    currency: "CNY"
    precision: 6
    auto_billing: true
    billing_cycle: "hourly"
```

#### 环境配置管理

创建 `server/addons/pve/config/environments.yaml`：

```yaml
# 环境配置文件
development:
  pve:
    nodes:
      - name: "dev-node1"
        host: "*************"
        port: 8006
        username: "root@pam"
        password: "dev_password"
        insecure: true
      - name: "dev-node2"
        host: "*************"
        port: 8006
        token_id: "root@pam!hotgo"
        token_secret: "dev-token-secret"
        insecure: true
  monitoring:
    enabled: true
    debug: true
    interval: 10
  billing:
    enabled: false
    test_mode: true

testing:
  pve:
    mock: true
    nodes:
      - name: "mock-node"
        host: "mock.pve.local"
        mock_data: true
  monitoring:
    enabled: false
  billing:
    enabled: false

production:
  pve:
    nodes: [] # 生产环境节点配置通过环境变量设置
    connection_pool:
      max_idle: 10
      max_open: 50
      max_lifetime: 3600
  monitoring:
    enabled: true
    interval: 30
    retention_days: 30
  billing:
    enabled: true
    auto_billing: true
    currency: "CNY"
    tax_rate: 0.06
```

### 配置加载和管理

创建 `server/addons/pve/global/config.go`：

```go
package global

import (
    "context"
    "sync"
    "fmt"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/os/gcfg"
    "github.com/gogf/gf/v2/errors/gerror"
)

var (
    Config *PVEConfig
    once   sync.Once
)

// PVEConfig PVE插件配置
type PVEConfig struct {
    Name         string                 `yaml:"name"`
    Title        string                 `yaml:"title"`
    Description  string                 `yaml:"description"`
    Version      string                 `yaml:"version"`
    Author       string                 `yaml:"author"`
    Website      string                 `yaml:"website"`
    Dependencies map[string]string      `yaml:"dependencies"`
    Modules      map[string]ModuleConfig `yaml:"modules"`
    Database     DatabaseConfig         `yaml:"database"`
    Routes       RoutesConfig          `yaml:"routes"`
    Permissions  PermissionsConfig     `yaml:"permissions"`
    Defaults     DefaultsConfig        `yaml:"defaults"`
}

type ModuleConfig struct {
    Enabled     bool   `yaml:"enabled"`
    Description string `yaml:"description"`
}

type DatabaseConfig struct {
    Tables []string `yaml:"tables"`
}

type RoutesConfig struct {
    Admin   string `yaml:"admin"`
    User    string `yaml:"user"`
    Webhook string `yaml:"webhook"`
}

type PermissionsConfig struct {
    Admin []string `yaml:"admin"`
    User  []string `yaml:"user"`
}

type DefaultsConfig struct {
    PVE        PVEDefaults        `yaml:"pve"`
    Monitoring MonitoringDefaults `yaml:"monitoring"`
    Billing    BillingDefaults    `yaml:"billing"`
}

type PVEDefaults struct {
    APITimeout int `yaml:"api_timeout"`
    RetryCount int `yaml:"retry_count"`
    RetryDelay int `yaml:"retry_delay"`
}

type MonitoringDefaults struct {
    Interval       int `yaml:"interval"`
    RetentionDays  int `yaml:"retention_days"`
    BatchSize      int `yaml:"batch_size"`
}

type BillingDefaults struct {
    Currency     string `yaml:"currency"`
    Precision    int    `yaml:"precision"`
    AutoBilling  bool   `yaml:"auto_billing"`
    BillingCycle string `yaml:"billing_cycle"`
}

// InitConfig 初始化配置
func InitConfig(ctx context.Context) error {
    once.Do(func() {
        Config = &PVEConfig{}
    })

    // 加载基础配置
    configFile := "addons/pve/config.yaml"
    cfg, err := gcfg.New()
    if err != nil {
        return gerror.Wrap(err, "创建配置实例失败")
    }

    err = cfg.SetPath(configFile)
    if err != nil {
        return gerror.Wrap(err, "设置配置文件路径失败")
    }

    err = cfg.GetStruct(".", Config)
    if err != nil {
        return gerror.Wrap(err, "解析配置文件失败")
    }

    // 加载环境相关配置
    env := g.Cfg().MustGet(ctx, "system.env", "development").String()
    err = loadEnvironmentConfig(ctx, env)
    if err != nil {
        return gerror.Wrap(err, "加载环境配置失败")
    }

    g.Log().Infof(ctx, "PVE插件配置加载完成: %s v%s", Config.Title, Config.Version)
    return nil
}

// IsModuleEnabled 检查模块是否启用
func IsModuleEnabled(module string) bool {
    if Config == nil || Config.Modules == nil {
        return false
    }
    
    if moduleConfig, exists := Config.Modules[module]; exists {
        return moduleConfig.Enabled
    }
    return false
}
```

### 插件安装流程

#### 1. 安装前检查

创建 `server/addons/pve/hooks/install.go`：

```go
package hooks

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    "hotgo/addons/pve/global"
)

// PreInstall 安装前检查
func PreInstall(ctx context.Context) error {
    g.Log().Info(ctx, "开始PVE插件安装前检查...")

    // 1. 检查系统依赖
    if err := checkSystemDependencies(ctx); err != nil {
        return gerror.Wrap(err, "系统依赖检查失败")
    }

    // 2. 检查数据库连接
    if err := checkDatabaseConnection(ctx); err != nil {
        return gerror.Wrap(err, "数据库连接检查失败")
    }

    // 3. 检查Redis连接
    if err := checkRedisConnection(ctx); err != nil {
        return gerror.Wrap(err, "Redis连接检查失败")
    }

    // 4. 检查配置文件
    if err := checkConfigFiles(ctx); err != nil {
        return gerror.Wrap(err, "配置文件检查失败")
    }

    // 5. 检查权限
    if err := checkPermissions(ctx); err != nil {
        return gerror.Wrap(err, "权限检查失败")
    }

    g.Log().Info(ctx, "PVE插件安装前检查通过")
    return nil
}

// Install 执行安装
func Install(ctx context.Context) error {
    g.Log().Info(ctx, "开始安装PVE插件...")

    // 1. 创建数据库表
    if err := createDatabaseTables(ctx); err != nil {
        return gerror.Wrap(err, "创建数据库表失败")
    }

    // 2. 初始化默认数据
    if err := initializeDefaultData(ctx); err != nil {
        return gerror.Wrap(err, "初始化默认数据失败")
    }

    // 3. 注册权限
    if err := registerPermissions(ctx); err != nil {
        return gerror.Wrap(err, "注册权限失败")
    }

    // 4. 创建默认配置
    if err := createDefaultConfig(ctx); err != nil {
        return gerror.Wrap(err, "创建默认配置失败")
    }

    // 5. 启动必要服务
    if err := startServices(ctx); err != nil {
        return gerror.Wrap(err, "启动服务失败")
    }

    g.Log().Info(ctx, "PVE插件安装完成")
    return nil
}

// PostInstall 安装后处理
func PostInstall(ctx context.Context) error {
    g.Log().Info(ctx, "开始PVE插件安装后处理...")

    // 1. 验证安装结果
    if err := validateInstallation(ctx); err != nil {
        return gerror.Wrap(err, "安装验证失败")
    }

    // 2. 清理临时文件
    if err := cleanupTempFiles(ctx); err != nil {
        g.Log().Warning(ctx, "清理临时文件失败: %v", err)
    }

    // 3. 发送安装通知
    if err := sendInstallNotification(ctx); err != nil {
        g.Log().Warning(ctx, "发送安装通知失败: %v", err)
    }

    g.Log().Info(ctx, "PVE插件安装后处理完成")
    return nil
}

func checkSystemDependencies(ctx context.Context) error {
    // 检查Go版本、MySQL版本等
    return nil
}

func checkDatabaseConnection(ctx context.Context) error {
    _, err := g.DB().Exec(ctx, "SELECT 1")
    return err
}

func checkRedisConnection(ctx context.Context) error {
    _, err := g.Redis().Ping(ctx)
    return err
}

func checkConfigFiles(ctx context.Context) error {
    return global.ValidateConfig()
}

func checkPermissions(ctx context.Context) error {
    // 检查文件系统权限
    return nil
}

func createDatabaseTables(ctx context.Context) error {
    // 读取并执行install.sql
    return nil
}

func initializeDefaultData(ctx context.Context) error {
    // 插入默认模板、产品等数据
    return nil
}

func registerPermissions(ctx context.Context) error {
    // 在权限系统中注册PVE相关权限
    return nil
}

func createDefaultConfig(ctx context.Context) error {
    // 创建默认配置项
    return nil
}

func startServices(ctx context.Context) error {
    // 启动监控采集、计费等后台服务
    return nil
}

func validateInstallation(ctx context.Context) error {
    // 验证表是否创建成功、权限是否注册等
    return nil
}

func cleanupTempFiles(ctx context.Context) error {
    // 清理安装过程中的临时文件
    return nil
}

func sendInstallNotification(ctx context.Context) error {
    // 发送安装完成通知
    return nil
}
```

#### 2. 安装脚本

创建 `scripts/install-pve-plugin.sh`：

```bash
#!/bin/bash

set -e

echo "🚀 开始安装PVE插件..."

# 检查必要工具
command -v go >/dev/null 2>&1 || { echo "❌ Go未安装"; exit 1; }
command -v mysql >/dev/null 2>&1 || { echo "❌ MySQL客户端未安装"; exit 1; }

# 设置变量
PLUGIN_DIR="server/addons/pve"
CONFIG_FILE="manifest/config/config.yaml"
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-3306}
DB_USER=${DB_USER:-"root"}
DB_PASS=${DB_PASS:-""}
DB_NAME=${DB_NAME:-"hotgo"}

echo "📋 检查安装环境..."

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 检查数据库连接
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 数据库连接失败"
    exit 1
fi

echo "✅ 环境检查通过"

echo "📦 安装PVE插件依赖..."
cd server
go get github.com/luthermonson/go-proxmox
go get github.com/influxdata/influxdb-client-go/v2
go mod tidy

echo "🗄️ 创建数据库表..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$PLUGIN_DIR/install.sql"

echo "📋 初始化默认数据..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$PLUGIN_DIR/data/init_data.sql"

echo "⚙️ 生成代码..."
gf gen dao -c hack/config.yaml

echo "🔧 配置权限..."
go run main.go plugin install pve

echo "✅ PVE插件安装完成!"
echo "📚 请查看文档: docs/guide-zh-CN/pve-development.md"
echo "🌐 管理界面: http://localhost:8000/admin/pve"
```

#### 3. 分步安装指南

**步骤1: 环境准备**

```bash
# 1. 验证Go版本
go version
# 要求: Go >= 1.24

# 2. 验证MySQL连接
mysql -u root -p -e "SELECT VERSION();"
# 要求: MySQL >= 8.0

# 3. 验证Redis连接
redis-cli ping
# 要求: Redis >= 7.0

# 4. 检查HotGo主项目
cd /path/to/hotgo
ls -la server/
```

**步骤2: 下载插件代码**

```bash
# 从Git仓库拉取PVE插件代码
git clone https://github.com/your-repo/pve-plugin.git server/addons/pve

# 或手动创建目录结构
mkdir -p server/addons/pve
```

**步骤3: 配置数据库**

```sql
-- 创建PVE专用数据库（可选）
CREATE DATABASE hotgo_pve CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 或使用现有HotGo数据库，执行插件表结构
USE hotgo;
SOURCE server/addons/pve/install.sql;
```

**步骤4: 配置文件设置**

编辑 `manifest/config/config.yaml`，添加PVE配置：

```yaml
# PVE插件配置
pve:
  enabled: true
  # 默认节点配置（开发环境）
  nodes:
    - name: "pve-node1"
      host: "*************"
      port: 8006
      username: "root@pam"
      password: "your_password"
      # 或使用Token认证
      # token_id: "root@pam!hotgo"
      # token_secret: "your-token-secret"
      insecure: true # 开发环境跳过SSL验证
  
  # 监控配置
  monitoring:
    enabled: true
    interval: 30     # 数据采集间隔(秒)
    retention: 7     # 数据保留天数
  
  # 计费配置
  billing:
    enabled: true
    currency: "CNY"
    auto_billing: true
    billing_cycle: "hourly"
    
    # 默认价格策略
    pricing:
      cpu_per_core_hour: 0.50    # 每核心每小时
      memory_per_gb_hour: 0.10   # 每GB内存每小时  
      storage_per_gb_hour: 0.01  # 每GB存储每小时
```

**步骤5: 安装插件依赖**

```bash
cd server

# 安装PVE相关Go包
go get github.com/luthermonson/go-proxmox@latest
go get github.com/influxdata/influxdb-client-go/v2@latest

# 更新依赖
go mod tidy

# 验证依赖安装
go list -m github.com/luthermonson/go-proxmox
```

**步骤6: 生成代码和模型**

```bash
# 生成数据访问层代码
gf gen dao -c hack/config.yaml

# 生成API接口代码
gf gen ctrl

# 验证生成结果
ls -la internal/dao/
ls -la internal/model/
```

**步骤7: 执行插件安装**

```bash
# 方法1: 使用安装脚本（推荐）
chmod +x scripts/install-pve-plugin.sh
./scripts/install-pve-plugin.sh

# 方法2: 手动执行安装
go run main.go plugin install pve

# 方法3: 通过管理界面安装
# 访问 http://localhost:8000/admin/plugins
# 找到PVE插件点击安装
```

**步骤8: 验证安装结果**

```bash
# 检查数据库表是否创建
mysql -u root -p -e "
USE hotgo;
SHOW TABLES LIKE 'hg_pve_%';
"

# 检查插件状态
go run main.go plugin status pve

# 启动服务进行功能测试
go run main.go
```

**步骤9: 初始化PVE节点**

通过管理界面添加第一个PVE节点：

```bash
curl -X POST "http://localhost:8000/admin/pve/nodes" \
  -H "Authorization: Bearer your-admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "pve-node1",
    "host": "*************",
    "port": 8006,
    "username": "root@pam",
    "password": "your_password"
  }'
```

**步骤10: 功能测试**

```bash
# 测试节点连接
curl "http://localhost:8000/admin/pve/nodes/1/status" \
  -H "Authorization: Bearer your-admin-token"

# 获取虚拟机列表
curl "http://localhost:8000/admin/pve/instances" \
  -H "Authorization: Bearer your-admin-token"

# 访问前端管理界面
# http://localhost:3000/admin/pve
```

### 安装故障排除

#### 常见安装问题

**1. 数据库连接失败**
```bash
错误: Error 1045: Access denied for user 'root'@'localhost'
解决: 检查MySQL用户权限和密码
mysql -u root -p
GRANT ALL PRIVILEGES ON hotgo.* TO 'hotgo'@'localhost' IDENTIFIED BY 'password';
FLUSH PRIVILEGES;
```

**2. PVE API连接超时**
```bash
错误: context deadline exceeded
解决: 检查PVE节点网络和防火墙设置
# 测试连接性
telnet ************* 8006
# 检查PVE API是否启用
curl -k https://*************:8006/api2/json/version
```

**3. Go模块依赖问题**
```bash
错误: module github.com/luthermonson/go-proxmox: not found
解决: 设置Go代理
go env -w GOPROXY=https://goproxy.cn,direct
go clean -modcache
go mod tidy
```

**4. 权限不足错误**
```bash
错误: permission denied
解决: 检查文件和目录权限
sudo chown -R $(whoami):$(whoami) server/addons/pve/
chmod -R 755 server/addons/pve/
```

#### 安装验证清单

- [ ] MySQL数据库连接正常
- [ ] Redis缓存服务正常  
- [ ] PVE节点API连接正常
- [ ] 插件数据表创建成功
- [ ] Go依赖包安装完成
- [ ] 配置文件格式正确
- [ ] 权限系统注册成功
- [ ] 后端API响应正常
- [ ] 前端界面访问正常
- [ ] 基础功能测试通过

#### 卸载插件

如需卸载PVE插件：

```bash
# 方法1: 使用卸载脚本
./scripts/uninstall-pve-plugin.sh

# 方法2: 手动卸载
go run main.go plugin uninstall pve

# 清理数据库（谨慎操作）
mysql -u root -p -e "
USE hotgo;
DROP TABLE IF EXISTS hg_pve_nodes;
DROP TABLE IF EXISTS hg_pve_instances;
DROP TABLE IF EXISTS hg_pve_monitoring;
DROP TABLE IF EXISTS hg_pve_orders;
DROP TABLE IF EXISTS hg_pve_billing;
DROP TABLE IF EXISTS hg_pve_templates;
DROP TABLE IF EXISTS hg_pve_products;
"

# 删除插件文件
rm -rf server/addons/pve/
```

## 🔐 PVE API 认证和权限系统

### PVE 认证机制详解

Proxmox VE 提供了多种认证机制以支持不同的使用场景，每种认证方式都有其特定的优势和适用场景。

#### 1. API Token 认证（推荐）

API Token 是 PVE 提供的最安全、最便捷的自动化认证方式，特别适合应用程序集成：

**特点：**
- 无需存储用户密码
- 可以设置特定的权限范围
- 支持过期时间管理
- 便于权限审计和管理

**创建 API Token：**

```bash
# 通过 Web UI 创建：
# 1. 登录 PVE Web 界面
# 2. 数据中心 -> 权限 -> API Token
# 3. 添加新的 API Token
# 4. 设置权限和过期时间

# 通过命令行创建：
pveum token add <userid> <tokenid> --expire <timestamp> --comment "Application Token"
```

**使用示例：**

```go
// 使用 API Token 的客户端配置
config := &Config{
    Host:        "pve.example.com",
    Port:        8006,
    TokenID:     "root@pam!myapp",           // 用户@域!Token名称
    TokenSecret: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx", // Token密钥
    Insecure:    true,  // 在测试环境中忽略SSL证书
    Timeout:     30 * time.Second,
    Retries:     3,
}

client, err := NewPVEClient(config)
if err != nil {
    log.Fatalf("创建PVE客户端失败: %v", err)
}

// API Token 在请求头中的格式
// Authorization: PVEAPIToken=root@pam!myapp=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
```

#### 2. 票据认证（Ticket Authentication）

基于用户名/密码的传统认证方式，通过获取临时票据来维持会话：

**认证流程：**

```go
type TicketAuthRequest struct {
    Username string `json:"username"`  // 用户名@域，如 root@pam
    Password string `json:"password"`  // 用户密码
}

type TicketAuthResponse struct {
    Data struct {
        Username            string `json:"username"`
        Ticket              string `json:"ticket"`                // 认证票据
        CSRFPreventionToken string `json:"CSRFPreventionToken"`   // CSRF防护令牌
        Cap struct {
            Access  map[string]interface{} `json:"access"`   // 用户权限
            Nodes   []string              `json:"nodes"`    // 可访问的节点
        } `json:"cap"`
    } `json:"data"`
}

// 票据认证实现
func (c *PVEClient) authenticateTicket(ctx context.Context) error {
    authData := map[string]string{
        "username": c.username,
        "password": c.password,
    }
    
    var response TicketAuthResponse
    err := c.request(ctx, "POST", "/access/ticket", authData, &response)
    if err != nil {
        return gerror.Wrap(err, "获取认证票据失败")
    }
    
    // 保存认证信息
    c.ticket = response.Data.Ticket
    c.csrfToken = response.Data.CSRFPreventionToken
    
    // 票据有效期为2小时，需要定时刷新
    c.startTicketRefresh(ctx)
    
    return nil
}

// 票据自动刷新机制
func (c *PVEClient) startTicketRefresh(ctx context.Context) {
    go func() {
        ticker := time.NewTicker(90 * time.Minute) // 每90分钟刷新一次
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                if err := c.authenticateTicket(ctx); err != nil {
                    g.Log().Errorf(ctx, "票据刷新失败: %v", err)
                }
            case <-ctx.Done():
                return
            }
        }
    }()
}
```

#### 3. Cookie 认证

主要用于 Web 界面，应用程序集成不推荐使用：

```go
// Cookie认证请求配置
func (c *PVEClient) addCookieAuth(req *http.Request) {
    if c.ticket != "" {
        cookie := &http.Cookie{
            Name:  "PVEAuthCookie",
            Value: c.ticket,
        }
        req.AddCookie(cookie)
        
        // 添加CSRF Token到请求头
        if c.csrfToken != "" {
            req.Header.Set("CSRFPreventionToken", c.csrfToken)
        }
    }
}
```

### PVE 权限系统详解

PVE 采用基于路径的权限控制系统，支持细粒度的权限管理：

#### 权限路径结构

```text
/                           # 根路径，超级管理员权限
├── /nodes                  # 节点管理权限
│   ├── /nodes/{node}      # 特定节点权限
│   └── /nodes/{node}/qemu # 特定节点的虚拟机权限
├── /vms                    # 全局虚拟机权限
│   └── /vms/{vmid}        # 特定虚拟机权限
├── /pool                   # 资源池权限
│   └── /pool/{poolid}     # 特定资源池权限
├── /storage               # 存储权限
│   └── /storage/{storage} # 特定存储权限
├── /datacenter            # 数据中心配置权限
│   ├── /datacenter/firewall # 防火墙配置
│   └── /datacenter/storage  # 存储配置
└── /access                # 用户和权限管理
    ├── /access/users      # 用户管理
    ├── /access/groups     # 组管理
    └── /access/roles      # 角色管理
```

#### 内置角色和权限

```go
// PVE内置角色定义
const (
    // 系统管理员角色
    RoleAdministrator = "Administrator"  // 完全管理权限
    RoleAuditor      = "Auditor"        // 只读审计权限
    
    // 用户管理角色
    RoleUserAdmin    = "UserAdmin"      // 用户管理权限
    
    // 虚拟机管理角色
    RolePVEVMAdmin   = "PVEVMAdmin"     // 虚拟机完全管理
    RolePVEVMUser    = "PVEVMUser"      // 虚拟机基本操作
    
    // 数据存储角色
    RolePVEDatastoreAdmin = "PVEDatastoreAdmin" // 存储管理
    RolePVEDatastoreUser  = "PVEDatastoreUser"  // 存储使用
    
    // 系统操作角色
    RolePVESysAdmin      = "PVESysAdmin"      // 系统管理
    RolePVETemplateUser  = "PVETemplateUser"  // 模板使用
)

// 权限定义
const (
    // 系统权限
    PermSysAudit     = "Sys.Audit"      // 系统审计
    PermSysConsole   = "Sys.Console"    // 控制台访问  
    PermSysModify    = "Sys.Modify"     // 系统修改
    PermSysPowerMgmt = "Sys.PowerMgmt"  // 电源管理
    PermSysSyslog    = "Sys.Syslog"     // 系统日志
    
    // 数据存储权限
    PermDatastoreAllocate     = "Datastore.Allocate"      // 存储分配
    PermDatastoreAllocateSpace = "Datastore.AllocateSpace" // 存储空间分配
    PermDatastoreAudit        = "Datastore.Audit"         // 存储审计
    
    // 用户管理权限
    PermUserModify = "User.Modify"      // 用户修改
    
    // 虚拟机权限
    PermVMAudit     = "VM.Audit"        // 虚拟机审计
    PermVMBackup    = "VM.Backup"       // 虚拟机备份
    PermVMClone     = "VM.Clone"        // 虚拟机克隆
    PermVMConfig    = "VM.Config"       // 虚拟机配置
    PermVMConsole   = "VM.Console"      // 虚拟机控制台
    PermVMMigrate   = "VM.Migrate"      // 虚拟机迁移
    PermVMMonitor   = "VM.Monitor"      // 虚拟机监控
    PermVMPowerMgmt = "VM.PowerMgmt"    // 虚拟机电源管理
    PermVMSnapshot  = "VM.Snapshot"     // 虚拟机快照
)
```

#### 权限检查实现

```go
// 权限验证客户端
type PermissionChecker struct {
    client   *PVEClient
    userPerms map[string][]string  // 用户权限缓存
}

// CheckPermission 检查用户权限
func (p *PermissionChecker) CheckPermission(ctx context.Context, user, path, permission string) (bool, error) {
    // 1. 从缓存获取用户权限
    if perms, ok := p.userPerms[user]; ok {
        return p.hasPermission(perms, path, permission), nil
    }
    
    // 2. 从PVE API获取用户权限
    perms, err := p.getUserPermissions(ctx, user)
    if err != nil {
        return false, err
    }
    
    // 3. 缓存权限信息
    p.userPerms[user] = perms
    
    return p.hasPermission(perms, path, permission), nil
}

// getUserPermissions 获取用户权限
func (p *PermissionChecker) getUserPermissions(ctx context.Context, user string) ([]string, error) {
    path := fmt.Sprintf("/access/permissions?userid=%s", user)
    var response APIResponse[map[string]interface{}]
    
    err := p.client.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    // 解析权限数据
    var permissions []string
    for path, perms := range response.Data {
        if permMap, ok := perms.(map[string]interface{}); ok {
            for perm := range permMap {
                permissions = append(permissions, fmt.Sprintf("%s:%s", path, perm))
            }
        }
    }
    
    return permissions, nil
}

// hasPermission 检查是否具有特定权限
func (p *PermissionChecker) hasPermission(userPerms []string, path, permission string) bool {
    // 检查完全匹配
    exactPerm := fmt.Sprintf("%s:%s", path, permission)
    for _, perm := range userPerms {
        if perm == exactPerm {
            return true
        }
    }
    
    // 检查父路径权限继承
    for _, perm := range userPerms {
        if p.isParentPermission(perm, path, permission) {
            return true
        }
    }
    
    return false
}
```

#### 自定义权限角色

```go
// CustomRole 自定义角色管理
type CustomRole struct {
    ID          string   `json:"roleid"`
    Privileges  []string `json:"privs"`
    Comment     string   `json:"comment,omitempty"`
}

// CreateRole 创建自定义角色
func (c *PVEClient) CreateRole(ctx context.Context, role *CustomRole) error {
    data := map[string]interface{}{
        "roleid": role.ID,
        "privs":  strings.Join(role.Privileges, ","),
    }
    if role.Comment != "" {
        data["comment"] = role.Comment
    }
    
    return c.request(ctx, "POST", "/access/roles", data, nil)
}

// AssignRole 分配角色给用户
func (c *PVEClient) AssignRole(ctx context.Context, user, path, role string) error {
    data := map[string]interface{}{
        "users": user,
        "roles": role,
        "path":  path,
    }
    
    return c.request(ctx, "PUT", "/access/acl", data, nil)
}

// 应用级别的权限管理示例
func setupApplicationPermissions(ctx context.Context, client *PVEClient) error {
    // 1. 创建应用特定的角色
    appVMRole := &CustomRole{
        ID: "AppVMManager", 
        Privileges: []string{
            PermVMAudit,
            PermVMConfig,
            PermVMConsole,
            PermVMPowerMgmt,
            PermVMSnapshot,
        },
        Comment: "应用程序虚拟机管理角色",
    }
    
    if err := client.CreateRole(ctx, appVMRole); err != nil {
        return err
    }
    
    // 2. 创建应用服务账号
    appUser := map[string]interface{}{
        "userid":   "app@pve",
        "password": "secure_password",
        "comment":  "应用程序服务账号",
    }
    
    if err := client.request(ctx, "POST", "/access/users", appUser, nil); err != nil {
        return err
    }
    
    // 3. 为服务账号分配权限
    if err := client.AssignRole(ctx, "app@pve", "/vms", "AppVMManager"); err != nil {
        return err
    }
    
    return nil
}
```

### 权限最佳实践

#### 1. 最小权限原则

```go
// 根据功能需求分配最小必要权限
type ApplicationPermissions struct {
    // VM监控应用只需要审计权限
    MonitoringApp []string = []string{PermVMAudit, PermSysAudit}
    
    // VM管理应用需要操作权限  
    ManagementApp []string = []string{
        PermVMAudit, PermVMConfig, PermVMPowerMgmt, PermVMConsole,
    }
    
    // 备份应用需要备份和快照权限
    BackupApp []string = []string{
        PermVMAudit, PermVMBackup, PermVMSnapshot,
    }
}
```

#### 2. 权限分级管理

```go
// 多级权限管理结构
type PermissionHierarchy struct {
    // 系统级别 - 完全管理权限
    SystemAdmin map[string][]string
    
    // 租户级别 - 租户内资源管理
    TenantAdmin map[string][]string
    
    // 项目级别 - 项目内资源操作
    ProjectUser map[string][]string
    
    // 资源级别 - 特定资源权限
    ResourceUser map[string][]string
}

func (p *PermissionHierarchy) InitializeHierarchy() {
    p.SystemAdmin = map[string][]string{
        "/": {"Administrator"},
    }
    
    p.TenantAdmin = map[string][]string{
        "/pool/{tenant_pool}": {"PVEVMAdmin"},
        "/storage/{tenant_storage}": {"PVEDatastoreUser"},
    }
    
    p.ProjectUser = map[string][]string{
        "/vms/{project_vm_range}": {"PVEVMUser"},
    }
    
    p.ResourceUser = map[string][]string{
        "/vms/{specific_vmid}": {"VM.Console", "VM.PowerMgmt"},
    }
}
```

#### 3. 安全审计

```go
// 权限审计日志
type PermissionAuditLog struct {
    Timestamp   time.Time `json:"timestamp"`
    UserID      string    `json:"userId"`
    Action      string    `json:"action"`
    Resource    string    `json:"resource"`
    Permission  string    `json:"permission"`
    Result      bool      `json:"result"`
    IPAddress   string    `json:"ipAddress"`
}

// LogPermissionCheck 记录权限检查
func (p *PermissionChecker) LogPermissionCheck(ctx context.Context, log *PermissionAuditLog) {
    // 记录到审计日志系统
    g.Log().Info(ctx, "权限检查", g.Map{
        "user":       log.UserID,
        "action":     log.Action,
        "resource":   log.Resource,
        "permission": log.Permission,
        "result":     log.Result,
        "ip":         log.IPAddress,
    })
}
```

通过以上详细的 PVE API 认证和权限系统说明，开发者可以：

1. **选择适合的认证方式**：根据应用场景选择 API Token 或票据认证
2. **实现细粒度权限控制**：基于路径和角色的权限管理
3. **遵循安全最佳实践**：最小权限原则和权限分级管理
4. **建立完整的审计机制**：记录所有权限相关操作

这套权限系统确保了 PVE 资源的安全访问和精确的权限控制。

## 🔐 多租户权限管理设计

### 权限模型架构

PVE云平台采用基于角色的权限控制(RBAC)模型，结合多租户隔离机制，确保资源安全和数据隔离。

#### 权限层级结构

```mermaid
graph TD
    A[系统管理员] --> B[租户管理员]
    B --> C[部门管理员]
    C --> D[普通用户]
    
    A --> E[全局权限]
    B --> F[租户权限]
    C --> G[部门权限]
    D --> H[个人权限]
    
    E --> I[系统配置管理]
    E --> J[全局监控]
    E --> K[计费策略]
    
    F --> L[租户资源管理]
    F --> M[用户管理]
    F --> N[租户配额]
    
    G --> O[部门资源分配]
    G --> P[成员管理]
    
    H --> Q[个人资源使用]
    H --> R[账单查看]
```

### 数据库权限设计

#### 权限相关表结构

创建权限管理相关数据表：

```sql
-- 租户表
CREATE TABLE `hg_pve_tenants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '租户ID',
  `name` varchar(100) NOT NULL COMMENT '租户名称',
  `code` varchar(50) NOT NULL COMMENT '租户编码',
  `description` text COMMENT '租户描述',
  `admin_user_id` bigint unsigned NOT NULL COMMENT '租户管理员用户ID',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  `quota_config` json DEFAULT NULL COMMENT '配额配置',
  `billing_config` json DEFAULT NULL COMMENT '计费配置',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_admin_user` (`admin_user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='PVE租户表';

-- 部门表
CREATE TABLE `hg_pve_departments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `parent_id` bigint unsigned DEFAULT 0 COMMENT '父部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `code` varchar(50) NOT NULL COMMENT '部门编码',
  `description` text COMMENT '部门描述',
  `manager_user_id` bigint unsigned DEFAULT NULL COMMENT '部门管理员ID',
  `quota_config` json DEFAULT NULL COMMENT '部门配额配置',
  `level` int DEFAULT 1 COMMENT '部门层级',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_code` (`tenant_id`,`code`),
  KEY `idx_tenant_parent` (`tenant_id`,`parent_id`),
  KEY `idx_manager` (`manager_user_id`)
) ENGINE=InnoDB COMMENT='PVE部门表';

-- 用户租户关联表
CREATE TABLE `hg_pve_user_tenants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `department_id` bigint unsigned DEFAULT NULL COMMENT '部门ID',
  `role_type` varchar(20) NOT NULL COMMENT '角色类型:admin,manager,user',
  `permissions` json DEFAULT NULL COMMENT '额外权限',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  `joined_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `expired_at` datetime DEFAULT NULL COMMENT '到期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_tenant` (`user_id`,`tenant_id`),
  KEY `idx_tenant_dept` (`tenant_id`,`department_id`),
  KEY `idx_role_type` (`role_type`)
) ENGINE=InnoDB COMMENT='用户租户关联表';

-- 资源权限表
CREATE TABLE `hg_pve_resource_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型:node,instance,template,product',
  `resource_id` bigint unsigned NOT NULL COMMENT '资源ID',
  `subject_type` varchar(20) NOT NULL COMMENT '主体类型:user,department,tenant',
  `subject_id` bigint unsigned NOT NULL COMMENT '主体ID',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型:view,create,edit,delete,control',
  `granted_by` bigint unsigned NOT NULL COMMENT '授权者ID',
  `granted_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `expired_at` datetime DEFAULT NULL COMMENT '权限到期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resource_subject_permission` (`resource_type`,`resource_id`,`subject_type`,`subject_id`,`permission_type`),
  KEY `idx_resource` (`resource_type`,`resource_id`),
  KEY `idx_subject` (`subject_type`,`subject_id`)
) ENGINE=InnoDB COMMENT='资源权限表';

-- 配额限制表
CREATE TABLE `hg_pve_quotas` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `subject_type` varchar(20) NOT NULL COMMENT '主体类型:tenant,department,user',
  `subject_id` bigint unsigned NOT NULL COMMENT '主体ID',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型:cpu,memory,storage,instance_count',
  `quota_limit` bigint NOT NULL COMMENT '配额限制',
  `quota_used` bigint DEFAULT 0 COMMENT '已使用配额',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位:core,gb,count',
  `period_type` varchar(20) DEFAULT 'total' COMMENT '周期类型:total,monthly,daily',
  `status` tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_subject_resource_period` (`subject_type`,`subject_id`,`resource_type`,`period_type`),
  KEY `idx_subject` (`subject_type`,`subject_id`)
) ENGINE=InnoDB COMMENT='配额限制表';
```

### 权限检查中间件

#### 创建权限检查中间件

创建 `server/addons/pve/middleware/auth.go`：

```go
package middleware

import (
    "context"
    "strings"

    "github.com/gogf/gf/v2/net/ghttp"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    "hotgo/addons/pve/service"
    "hotgo/internal/model/entity"
)

// PVEAuth PVE权限中间件
func PVEAuth() ghttp.MiddlewareFunc {
    return func(r *ghttp.Request) {
        ctx := r.Context()
        
        // 获取当前用户信息
        user := getCurrentUser(ctx)
        if user == nil {
            r.Response.WriteJsonExit(g.Map{
                "code": 401,
                "message": "未登录",
            })
            return
        }

        // 检查PVE权限
        if !hasAccess, err := checkPVEAccess(ctx, user, r); err != nil {
            g.Log().Error(ctx, "权限检查失败: %v", err)
            r.Response.WriteJsonExit(g.Map{
                "code": 500,
                "message": "权限检查失败",
            })
            return
        } else if !hasAccess {
            r.Response.WriteJsonExit(g.Map{
                "code": 403,
                "message": "权限不足",
            })
            return
        }

        // 设置用户上下文
        ctx = context.WithValue(ctx, "pve_user", user)
        r.SetCtx(ctx)
        r.Middleware.Next()
    }
}

// TenantIsolation 租户隔离中间件
func TenantIsolation() ghttp.MiddlewareFunc {
    return func(r *ghttp.Request) {
        ctx := r.Context()
        
        // 获取当前用户
        user := getCurrentUser(ctx)
        if user == nil {
            r.Middleware.Next()
            return
        }

        // 获取用户租户信息
        tenantInfo, err := service.Permission().GetUserTenant(ctx, user.ID)
        if err != nil {
            g.Log().Error(ctx, "获取用户租户信息失败: %v", err)
            r.Response.WriteJsonExit(g.Map{
                "code": 500,
                "message": "获取租户信息失败",
            })
            return
        }

        // 设置租户上下文
        ctx = context.WithValue(ctx, "tenant_info", tenantInfo)
        r.SetCtx(ctx)
        r.Middleware.Next()
    }
}

// ResourcePermission 资源权限中间件
func ResourcePermission(resourceType string, permissionType string) ghttp.MiddlewareFunc {
    return func(r *ghttp.Request) {
        ctx := r.Context()
        
        // 获取资源ID
        resourceId := getResourceIdFromRequest(r, resourceType)
        if resourceId == 0 {
            r.Middleware.Next()
            return
        }

        // 获取当前用户
        user := getCurrentUser(ctx)
        if user == nil {
            r.Response.WriteJsonExit(g.Map{
                "code": 401,
                "message": "未登录",
            })
            return
        }

        // 检查资源权限
        hasPermission, err := service.Permission().CheckResourcePermission(
            ctx, user.ID, resourceType, resourceId, permissionType,
        )
        if err != nil {
            g.Log().Error(ctx, "检查资源权限失败: %v", err)
            r.Response.WriteJsonExit(g.Map{
                "code": 500,
                "message": "权限检查失败",
            })
            return
        }

        if !hasPermission {
            r.Response.WriteJsonExit(g.Map{
                "code": 403,
                "message": "无权限访问该资源",
            })
            return
        }

        r.Middleware.Next()
    }
}

// getCurrentUser 获取当前用户信息
func getCurrentUser(ctx context.Context) *entity.SysUser {
    // 从JWT或Session中获取用户信息
    // 这里需要根据HotGo的用户认证机制实现
    return nil
}

// checkPVEAccess 检查PVE访问权限
func checkPVEAccess(ctx context.Context, user *entity.SysUser, r *ghttp.Request) (bool, error) {
    // 检查用户是否有PVE相关权限
    return service.Permission().CheckUserPVEAccess(ctx, user.ID)
}

// getResourceIdFromRequest 从请求中获取资源ID
func getResourceIdFromRequest(r *ghttp.Request, resourceType string) uint64 {
    var resourceId uint64
    
    // 从URL路径参数中获取ID
    switch resourceType {
    case "node":
        resourceId = r.Get("nodeId").Uint64()
        if resourceId == 0 {
            resourceId = r.Get("id").Uint64()
        }
    case "instance":
        resourceId = r.Get("instanceId").Uint64()
        if resourceId == 0 {
            resourceId = r.Get("id").Uint64()
        }
    // 其他资源类型...
    }

    return resourceId
}
```

### 权限服务实现

创建 `server/addons/pve/service/permission.go`：

```go
package service

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    "hotgo/addons/pve/model/entity"
)

type sPermission struct{}

func Permission() *sPermission {
    return &sPermission{}
}

// GetUserTenant 获取用户租户信息
func (s *sPermission) GetUserTenant(ctx context.Context, userId uint64) (*UserTenantInfo, error) {
    var userTenant *entity.PveUserTenants
    err := g.DB().Model("pve_user_tenants").
        Where("user_id = ? AND status = 1", userId).
        Scan(&userTenant)
    
    if err != nil {
        return nil, gerror.Wrap(err, "查询用户租户信息失败")
    }

    if userTenant == nil {
        return nil, gerror.New("用户未分配租户")
    }

    return &UserTenantInfo{
        UserID:       userTenant.UserId,
        TenantID:     userTenant.TenantId,
        DepartmentID: userTenant.DepartmentId,
        RoleType:     userTenant.RoleType,
    }, nil
}

// CheckUserPVEAccess 检查用户PVE访问权限
func (s *sPermission) CheckUserPVEAccess(ctx context.Context, userId uint64) (bool, error) {
    // 检查用户是否有PVE权限
    var count int
    err := g.DB().Model("pve_user_tenants").
        Where("user_id = ? AND status = 1", userId).
        Count(&count)
    
    if err != nil {
        return false, gerror.Wrap(err, "检查用户PVE权限失败")
    }

    return count > 0, nil
}

// CheckResourcePermission 检查资源权限
func (s *sPermission) CheckResourcePermission(ctx context.Context, userId uint64, 
    resourceType string, resourceId uint64, permissionType string) (bool, error) {
    
    // 获取用户租户信息
    userTenant, err := s.GetUserTenant(ctx, userId)
    if err != nil {
        return false, err
    }

    // 检查直接权限
    hasDirectPermission, err := s.checkDirectPermission(ctx, userId, resourceType, resourceId, permissionType)
    if err != nil {
        return false, err
    }
    if hasDirectPermission {
        return true, nil
    }

    // 检查部门权限
    if userTenant.DepartmentID != nil {
        hasDeptPermission, err := s.checkDepartmentPermission(ctx, *userTenant.DepartmentID, resourceType, resourceId, permissionType)
        if err != nil {
            return false, err
        }
        if hasDeptPermission {
            return true, nil
        }
    }

    // 检查租户权限
    hasTenantPermission, err := s.checkTenantPermission(ctx, userTenant.TenantID, resourceType, resourceId, permissionType)
    if err != nil {
        return false, err
    }

    return hasTenantPermission, nil
}

// checkDirectPermission 检查直接权限
func (s *sPermission) checkDirectPermission(ctx context.Context, userId uint64,
    resourceType string, resourceId uint64, permissionType string) (bool, error) {
    
    var count int
    err := g.DB().Model("pve_resource_permissions").
        Where("subject_type = 'user' AND subject_id = ?", userId).
        Where("resource_type = ? AND resource_id = ?", resourceType, resourceId).
        Where("permission_type = ?", permissionType).
        Where("expired_at IS NULL OR expired_at > NOW()").
        Count(&count)
    
    if err != nil {
        return false, gerror.Wrap(err, "检查直接权限失败")
    }

    return count > 0, nil
}

// checkDepartmentPermission 检查部门权限
func (s *sPermission) checkDepartmentPermission(ctx context.Context, departmentId uint64,
    resourceType string, resourceId uint64, permissionType string) (bool, error) {
    
    var count int
    err := g.DB().Model("pve_resource_permissions").
        Where("subject_type = 'department' AND subject_id = ?", departmentId).
        Where("resource_type = ? AND resource_id = ?", resourceType, resourceId).
        Where("permission_type = ?", permissionType).
        Where("expired_at IS NULL OR expired_at > NOW()").
        Count(&count)
    
    if err != nil {
        return false, gerror.Wrap(err, "检查部门权限失败")
    }

    return count > 0, nil
}

// checkTenantPermission 检查租户权限
func (s *sPermission) checkTenantPermission(ctx context.Context, tenantId uint64,
    resourceType string, resourceId uint64, permissionType string) (bool, error) {
    
    var count int
    err := g.DB().Model("pve_resource_permissions").
        Where("subject_type = 'tenant' AND subject_id = ?", tenantId).
        Where("resource_type = ? AND resource_id = ?", resourceType, resourceId).
        Where("permission_type = ?", permissionType).
        Where("expired_at IS NULL OR expired_at > NOW()").
        Count(&count)
    
    if err != nil {
        return false, gerror.Wrap(err, "检查租户权限失败")
    }

    return count > 0, nil
}

// CheckQuota 检查配额限制
func (s *sPermission) CheckQuota(ctx context.Context, subjectType string, subjectId uint64,
    resourceType string, requestAmount int64) (bool, error) {
    
    var quota *entity.PveQuotas
    err := g.DB().Model("pve_quotas").
        Where("subject_type = ? AND subject_id = ?", subjectType, subjectId).
        Where("resource_type = ? AND status = 1", resourceType).
        Scan(&quota)
    
    if err != nil {
        return false, gerror.Wrap(err, "查询配额信息失败")
    }

    if quota == nil {
        // 没有配额限制，允许使用
        return true, nil
    }

    // 检查是否超出配额
    if quota.QuotaUsed+requestAmount > quota.QuotaLimit {
        return false, nil
    }

    return true, nil
}

type UserTenantInfo struct {
    UserID       uint64  `json:"userId"`
    TenantID     uint64  `json:"tenantId"`
    DepartmentID *uint64 `json:"departmentId,omitempty"`
    RoleType     string  `json:"roleType"`
}
```

### 权限管理API

创建权限管理相关的API接口，支持租户、部门、权限的管理操作。

#### 租户管理API

创建 `server/api/admin/pve/tenants.go`：

```go
package pve

import (
    "github.com/gogf/gf/v2/frame/g"
    "hotgo/internal/model/input/form"
)

// TenantCreateReq 创建租户请求
type TenantCreateReq struct {
    g.Meta      `path:"/tenants" method:"post" summary:"创建租户" tags:"PVE租户管理"`
    Name        string `json:"name" v:"required|length:1,100#租户名称不能为空|名称长度为1-100个字符"`
    Code        string `json:"code" v:"required|length:1,50#租户编码不能为空|编码长度为1-50个字符"`
    Description string `json:"description"`
    AdminUserId uint64 `json:"adminUserId" v:"required#管理员用户ID不能为空"`
    QuotaConfig string `json:"quotaConfig"`
    BillingConfig string `json:"billingConfig"`
}

type TenantCreateRes struct {
    TenantID uint64 `json:"tenantId"`
}

// TenantListReq 租户列表请求
type TenantListReq struct {
    g.Meta `path:"/tenants" method:"get" summary:"获取租户列表" tags:"PVE租户管理"`
    form.PageReq
    Name     string `json:"name"`
    Code     string `json:"code"`
    Status   *int   `json:"status"`
    OrderBy  string `json:"orderBy"`
    OrderDir string `json:"orderDir"`
}

type TenantListRes struct {
    form.PageRes
    List []*TenantListModel `json:"list"`
}

type TenantListModel struct {
    ID            uint64 `json:"id"`
    Name          string `json:"name"`
    Code          string `json:"code"`
    Description   string `json:"description"`
    AdminUserId   uint64 `json:"adminUserId"`
    AdminUsername string `json:"adminUsername"`
    Status        int    `json:"status"`
    QuotaConfig   string `json:"quotaConfig"`
    BillingConfig string `json:"billingConfig"`
    CreatedAt     string `json:"createdAt"`
    UpdatedAt     string `json:"updatedAt"`
}

// DepartmentCreateReq 创建部门请求  
type DepartmentCreateReq struct {
    g.Meta       `path:"/departments" method:"post" summary:"创建部门" tags:"PVE部门管理"`
    TenantID     uint64  `json:"tenantId" v:"required#租户ID不能为空"`
    ParentID     uint64  `json:"parentId"`
    Name         string  `json:"name" v:"required|length:1,100#部门名称不能为空|名称长度为1-100个字符"`
    Code         string  `json:"code" v:"required|length:1,50#部门编码不能为空|编码长度为1-50个字符"`
    Description  string  `json:"description"`
    ManagerUserId *uint64 `json:"managerUserId"`
    QuotaConfig  string  `json:"quotaConfig"`
}
```

### 前端权限管理界面

创建权限管理前端界面，支持租户、部门、用户权限的可视化管理。

通过以上多租户权限管理设计，系统能够实现：

1. **层级化权限管理**: 系统管理员 -> 租户管理员 -> 部门管理员 -> 普通用户
2. **资源隔离**: 不同租户之间的资源完全隔离
3. **灵活授权**: 支持用户、部门、租户三个层级的权限授权
4. **配额控制**: 对CPU、内存、存储等资源进行配额限制
5. **权限继承**: 支持部门和租户权限的继承机制
6. **审计追踪**: 完整的权限变更记录和审计日志

## 🔄 数据一致性和错误处理

### 数据一致性设计

#### 事务管理策略

在PVE云平台中，数据一致性至关重要，特别是在以下场景：

1. **虚拟机创建流程**：订单创建 -> 资源分配 -> PVE操作 -> 计费开始
2. **计费处理**：资源使用统计 -> 费用计算 -> 账户扣款 -> 发票生成
3. **权限变更**：用户授权 -> 资源访问 -> 审计日志记录

#### 分布式事务处理

创建 `server/addons/pve/library/transaction/manager.go`：

```go
package transaction

import (
    "context"
    "database/sql"
    "fmt"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/database/gdb"
    "github.com/gogf/gf/v2/errors/gerror"
)

type TransactionManager struct {
    db gdb.DB
}

func NewTransactionManager() *TransactionManager {
    return &TransactionManager{
        db: g.DB(),
    }
}

// ExecuteInTransaction 在事务中执行操作
func (tm *TransactionManager) ExecuteInTransaction(ctx context.Context, fn func(ctx context.Context, tx gdb.TX) error) error {
    return tm.db.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        return fn(ctx, tx)
    })
}

// CreateInstanceWithOrder 创建虚拟机实例（包含订单处理）
func (tm *TransactionManager) CreateInstanceWithOrder(ctx context.Context, req *CreateInstanceRequest) (*CreateInstanceResult, error) {
    var result *CreateInstanceResult
    var err error

    err = tm.ExecuteInTransaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 1. 创建订单记录
        order, err := tm.createOrder(ctx, tx, req)
        if err != nil {
            return gerror.Wrap(err, "创建订单失败")
        }

        // 2. 检查资源配额
        if err := tm.checkResourceQuota(ctx, tx, req); err != nil {
            return gerror.Wrap(err, "资源配额检查失败")
        }

        // 3. 分配VMID
        vmid, err := tm.allocateVMID(ctx, tx, req.NodeID)
        if err != nil {
            return gerror.Wrap(err, "分配VMID失败")
        }

        // 4. 创建实例记录
        instance, err := tm.createInstance(ctx, tx, req, order.ID, vmid)
        if err != nil {
            return gerror.Wrap(err, "创建实例记录失败")
        }

        // 5. 更新资源配额使用量
        if err := tm.updateQuotaUsage(ctx, tx, req); err != nil {
            return gerror.Wrap(err, "更新配额使用量失败")
        }

        result = &CreateInstanceResult{
            OrderID:    order.ID,
            InstanceID: instance.ID,
            VMID:       vmid,
        }

        return nil
    })

    if err != nil {
        return nil, err
    }

    // 6. 异步创建PVE虚拟机（事务外执行）
    go tm.createPVEInstanceAsync(context.Background(), result)

    return result, nil
}

// 异步创建PVE虚拟机
func (tm *TransactionManager) createPVEInstanceAsync(ctx context.Context, result *CreateInstanceResult) {
    // 异步任务：创建PVE虚拟机
    // 如果失败，需要回滚数据库状态并记录错误
}

type CreateInstanceRequest struct {
    UserID      uint64 `json:"userId"`
    NodeID      uint64 `json:"nodeId"`
    ProductID   uint64 `json:"productId"`
    TemplateID  uint64 `json:"templateId"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Period      int    `json:"period"`
    PeriodType  string `json:"periodType"`
}

type CreateInstanceResult struct {
    OrderID    uint64 `json:"orderId"`
    InstanceID uint64 `json:"instanceId"`
    VMID       int    `json:"vmid"`
}

func (tm *TransactionManager) createOrder(ctx context.Context, tx gdb.TX, req *CreateInstanceRequest) (*Order, error) {
    // 实现订单创建逻辑
    return nil, nil
}

func (tm *TransactionManager) checkResourceQuota(ctx context.Context, tx gdb.TX, req *CreateInstanceRequest) error {
    // 实现配额检查逻辑
    return nil
}

func (tm *TransactionManager) allocateVMID(ctx context.Context, tx gdb.TX, nodeID uint64) (int, error) {
    // 实现VMID分配逻辑，确保唯一性
    return 0, nil
}

func (tm *TransactionManager) createInstance(ctx context.Context, tx gdb.TX, req *CreateInstanceRequest, orderID uint64, vmid int) (*Instance, error) {
    // 实现实例记录创建
    return nil, nil
}

func (tm *TransactionManager) updateQuotaUsage(ctx context.Context, tx gdb.TX, req *CreateInstanceRequest) error {
    // 实现配额使用量更新
    return nil
}

type Order struct {
    ID uint64
}

type Instance struct {
    ID uint64
}
```

#### 数据同步机制

创建 `server/addons/pve/library/sync/synchronizer.go`：

```go
package sync

import (
    "context"
    "time"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/os/gcron"
    "github.com/gogf/gf/v2/errors/gerror"
)

type DataSynchronizer struct {
    pveClient  *PVEClient
    updateChan chan SyncTask
}

type SyncTask struct {
    Type       string      `json:"type"`       // 同步类型：instance, node, monitoring
    ResourceID uint64      `json:"resourceId"` // 资源ID
    Action     string      `json:"action"`     // 操作类型：create, update, delete
    Data       interface{} `json:"data"`       // 相关数据
    Priority   int         `json:"priority"`   // 优先级
    RetryCount int         `json:"retryCount"` // 重试次数
    CreatedAt  time.Time   `json:"createdAt"`
}

func NewDataSynchronizer() *DataSynchronizer {
    return &DataSynchronizer{
        updateChan: make(chan SyncTask, 1000),
    }
}

// Start 启动数据同步服务
func (ds *DataSynchronizer) Start(ctx context.Context) error {
    // 启动同步任务处理协程
    go ds.processSyncTasks(ctx)

    // 启动定时同步任务
    gcron.AddSingleton(ctx, "@every 5m", ds.syncInstanceStatus)
    gcron.AddSingleton(ctx, "@every 10m", ds.syncNodeStatus)
    gcron.AddSingleton(ctx, "@every 30s", ds.syncMonitoringData)

    g.Log().Info(ctx, "数据同步服务启动完成")
    return nil
}

// processSyncTasks 处理同步任务队列
func (ds *DataSynchronizer) processSyncTasks(ctx context.Context) {
    for {
        select {
        case task := <-ds.updateChan:
            if err := ds.executeSync(ctx, task); err != nil {
                g.Log().Error(ctx, "同步任务执行失败: %v, 任务: %+v", err, task)
                // 重试逻辑
                if task.RetryCount < 3 {
                    task.RetryCount++
                    // 延迟重试
                    time.AfterFunc(time.Duration(task.RetryCount)*time.Minute, func() {
                        ds.updateChan <- task
                    })
                }
            }
        case <-ctx.Done():
            return
        }
    }
}

// executeSync 执行同步任务
func (ds *DataSynchronizer) executeSync(ctx context.Context, task SyncTask) error {
    switch task.Type {
    case "instance":
        return ds.syncInstance(ctx, task)
    case "node":
        return ds.syncNode(ctx, task)
    case "monitoring":
        return ds.syncMonitoring(ctx, task)
    default:
        return gerror.Newf("未知的同步任务类型: %s", task.Type)
    }
}

// SyncInstanceStatus 同步实例状态
func (ds *DataSynchronizer) syncInstanceStatus(ctx context.Context) error {
    g.Log().Debug(ctx, "开始同步实例状态...")

    // 获取所有活跃实例
    instances, err := ds.getActiveInstances(ctx)
    if err != nil {
        return gerror.Wrap(err, "获取活跃实例失败")
    }

    for _, instance := range instances {
        // 获取PVE实例状态
        pveStatus, err := ds.pveClient.GetVMStatus(ctx, instance.NodeName, instance.VMID)
        if err != nil {
            g.Log().Warning(ctx, "获取实例[%d]PVE状态失败: %v", instance.ID, err)
            continue
        }

        // 比较状态差异
        if instance.Status != pveStatus.Status {
            // 更新数据库状态
            err = ds.updateInstanceStatus(ctx, instance.ID, pveStatus.Status)
            if err != nil {
                g.Log().Error(ctx, "更新实例[%d]状态失败: %v", instance.ID, err)
            } else {
                g.Log().Info(ctx, "实例[%d]状态已同步: %s -> %s", 
                    instance.ID, instance.Status, pveStatus.Status)
            }
        }
    }

    return nil
}

func (ds *DataSynchronizer) syncNodeStatus(ctx context.Context) error {
    // 实现节点状态同步
    return nil
}

func (ds *DataSynchronizer) syncMonitoringData(ctx context.Context) error {
    // 实现监控数据同步
    return nil
}

func (ds *DataSynchronizer) getActiveInstances(ctx context.Context) ([]*InstanceInfo, error) {
    // 实现获取活跃实例
    return nil, nil
}

func (ds *DataSynchronizer) updateInstanceStatus(ctx context.Context, instanceID uint64, status string) error {
    // 实现状态更新
    return nil
}

type InstanceInfo struct {
    ID       uint64 `json:"id"`
    VMID     int    `json:"vmid"`
    NodeName string `json:"nodeName"`
    Status   string `json:"status"`
}

type PVEClient struct {
    // PVE客户端实现
}

func (c *PVEClient) GetVMStatus(ctx context.Context, nodeName string, vmid int) (*VMStatus, error) {
    // 实现PVE状态获取
    return nil, nil
}

type VMStatus struct {
    Status string `json:"status"`
}
```

### 错误处理框架

#### 统一错误定义

创建 `server/addons/pve/library/errors/codes.go`：

```go
package errors

import (
    "fmt"
    "github.com/gogf/gf/v2/errors/gerror"
)

// 错误码定义
const (
    // 通用错误 (10000-10999)
    CodeInternalError   = 10000
    CodeInvalidRequest  = 10001
    CodeUnauthorized    = 10002
    CodeForbidden       = 10003
    CodeNotFound        = 10004
    CodeConflict        = 10005
    CodeTooManyRequests = 10006

    // PVE连接错误 (11000-11099)
    CodePVEConnectionFailed  = 11000
    CodePVEAuthFailed        = 11001
    CodePVETimeout           = 11002
    CodePVENodeUnavailable   = 11003
    CodePVEAPIError          = 11004

    // 虚拟机管理错误 (11100-11199)
    CodeVMCreationFailed     = 11100
    CodeVMNotFound           = 11101
    CodeVMOperationFailed    = 11102
    CodeVMStatusInvalid      = 11103
    CodeVMIDAlreadyExists    = 11104
    CodeVMResourceInsufficient = 11105

    // 权限和配额错误 (11200-11299)
    CodePermissionDenied     = 11200
    CodeQuotaExceeded        = 11201
    CodeTenantNotFound       = 11202
    CodeUserNotInTenant      = 11203
    CodeResourceUnauthorized = 11204

    // 计费系统错误 (11300-11399)
    CodeBillingCalculationFailed = 11300
    CodeInsufficientBalance      = 11301
    CodeOrderProcessingFailed    = 11302
    CodePaymentFailed           = 11303
    CodeInvoiceGenerationFailed = 11304

    // 监控系统错误 (11400-11499)
    CodeMonitoringDataUnavailable = 11400
    CodeMetricsCollectionFailed   = 11401
    CodeAlertDeliveryFailed       = 11402
)

// 错误消息映射
var ErrorMessages = map[int]string{
    CodeInternalError:   "内部服务器错误",
    CodeInvalidRequest:  "请求参数无效",
    CodeUnauthorized:    "未授权访问",
    CodeForbidden:       "权限不足",
    CodeNotFound:        "资源不存在",
    CodeConflict:        "资源冲突",
    CodeTooManyRequests: "请求过于频繁",

    CodePVEConnectionFailed:  "PVE连接失败",
    CodePVEAuthFailed:        "PVE认证失败",
    CodePVETimeout:           "PVE操作超时",
    CodePVENodeUnavailable:   "PVE节点不可用",
    CodePVEAPIError:          "PVE API调用错误",

    CodeVMCreationFailed:     "虚拟机创建失败",
    CodeVMNotFound:           "虚拟机不存在",
    CodeVMOperationFailed:    "虚拟机操作失败",
    CodeVMStatusInvalid:      "虚拟机状态无效",
    CodeVMIDAlreadyExists:    "虚拟机ID已存在",
    CodeVMResourceInsufficient: "虚拟机资源不足",

    CodePermissionDenied:     "权限被拒绝",
    CodeQuotaExceeded:        "配额已超限",
    CodeTenantNotFound:       "租户不存在",
    CodeUserNotInTenant:      "用户不属于该租户",
    CodeResourceUnauthorized: "无权访问该资源",

    CodeBillingCalculationFailed: "计费计算失败",
    CodeInsufficientBalance:      "账户余额不足",
    CodeOrderProcessingFailed:    "订单处理失败",
    CodePaymentFailed:           "支付失败",
    CodeInvoiceGenerationFailed: "发票生成失败",

    CodeMonitoringDataUnavailable: "监控数据不可用",
    CodeMetricsCollectionFailed:   "指标采集失败",
    CodeAlertDeliveryFailed:       "告警通知发送失败",
}

// PVEError 自定义错误类型
type PVEError struct {
    Code     int    `json:"code"`
    Message  string `json:"message"`
    Details  string `json:"details,omitempty"`
    Internal error  `json:"-"`
}

func (e *PVEError) Error() string {
    if e.Details != "" {
        return fmt.Sprintf("PVE错误[%d]: %s - %s", e.Code, e.Message, e.Details)
    }
    return fmt.Sprintf("PVE错误[%d]: %s", e.Code, e.Message)
}

// NewError 创建新的PVE错误
func NewError(code int, details ...string) *PVEError {
    message, exists := ErrorMessages[code]
    if !exists {
        message = "未知错误"
    }

    err := &PVEError{
        Code:    code,
        Message: message,
    }

    if len(details) > 0 {
        err.Details = details[0]
    }

    return err
}

// WrapError 包装标准错误为PVE错误
func WrapError(code int, err error, details ...string) *PVEError {
    pveErr := NewError(code, details...)
    pveErr.Internal = err
    return pveErr
}

// 常用错误构造函数
func NewPVEConnectionError(details string) *PVEError {
    return NewError(CodePVEConnectionFailed, details)
}

func NewVMCreationError(details string) *PVEError {
    return NewError(CodeVMCreationFailed, details)
}

func NewPermissionDeniedError(details string) *PVEError {
    return NewError(CodePermissionDenied, details)
}

func NewQuotaExceededError(resourceType string, current, limit int64) *PVEError {
    details := fmt.Sprintf("资源类型: %s, 当前使用: %d, 配额限制: %d", resourceType, current, limit)
    return NewError(CodeQuotaExceeded, details)
}
```

#### 错误处理中间件

创建 `server/addons/pve/middleware/error_handler.go`：

```go
package middleware

import (
    "net/http"
    
    "github.com/gogf/gf/v2/net/ghttp"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    pveerrors "hotgo/addons/pve/library/errors"
)

// ErrorHandler PVE错误处理中间件
func ErrorHandler() ghttp.MiddlewareFunc {
    return ghttp.MiddlewareHandlerResponse(func(r *ghttp.Request, responderObject interface{}) {
        // 检查是否有错误发生
        if err := r.GetError(); err != nil {
            // 记录错误日志
            g.Log().Error(r.Context(), "PVE请求处理错误: %+v", err)

            // 错误分类处理
            code, message, httpStatus := categorizeError(err)
            
            // 返回统一格式的错误响应
            r.Response.Status = httpStatus
            r.Response.WriteJsonExit(g.Map{
                "code":    code,
                "message": message,
                "data":    nil,
                "error":   formatErrorDetails(err),
            })
        }
    })
}

// categorizeError 错误分类
func categorizeError(err error) (int, string, int) {
    // 检查是否为PVE自定义错误
    if pveErr, ok := err.(*pveerrors.PVEError); ok {
        return pveErr.Code, pveErr.Message, mapCodeToHTTPStatus(pveErr.Code)
    }

    // 检查是否为GoFrame标准错误
    if gerr := gerror.Current(err); gerr != nil {
        switch gerr.Code() {
        case gerror.CodeValidationFailed:
            return pveerrors.CodeInvalidRequest, "请求参数验证失败", http.StatusBadRequest
        case gerror.CodeNotAuthorized:
            return pveerrors.CodeUnauthorized, "未授权访问", http.StatusUnauthorized
        case gerror.CodeNotFound:
            return pveerrors.CodeNotFound, "资源不存在", http.StatusNotFound
        default:
            return pveerrors.CodeInternalError, "内部服务器错误", http.StatusInternalServerError
        }
    }

    // 默认错误处理
    return pveerrors.CodeInternalError, "内部服务器错误", http.StatusInternalServerError
}

// mapCodeToHTTPStatus 将错误码映射为HTTP状态码
func mapCodeToHTTPStatus(code int) int {
    switch {
    case code >= 10001 && code <= 10001:
        return http.StatusBadRequest
    case code >= 10002 && code <= 10002:
        return http.StatusUnauthorized
    case code >= 10003 && code <= 10003:
        return http.StatusForbidden
    case code >= 10004 && code <= 10004:
        return http.StatusNotFound
    case code >= 10005 && code <= 10005:
        return http.StatusConflict
    case code >= 10006 && code <= 10006:
        return http.StatusTooManyRequests
    case code >= 11000 && code <= 11499:
        return http.StatusInternalServerError
    default:
        return http.StatusInternalServerError
    }
}

// formatErrorDetails 格式化错误详情（生产环境可能需要隐藏）
func formatErrorDetails(err error) interface{} {
    // 在开发环境显示详细错误信息
    if g.Cfg().MustGet(nil, "system.env").String() == "development" {
        return err.Error()
    }
    
    // 生产环境隐藏详细信息
    return nil
}
```

#### 重试机制

创建 `server/addons/pve/library/retry/retry.go`：

```go
package retry

import (
    "context"
    "time"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
)

// RetryConfig 重试配置
type RetryConfig struct {
    MaxAttempts int           `json:"maxAttempts"` // 最大重试次数
    InitialDelay time.Duration `json:"initialDelay"` // 初始延迟
    MaxDelay     time.Duration `json:"maxDelay"`     // 最大延迟
    Multiplier   float64       `json:"multiplier"`   // 延迟倍数
}

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = &RetryConfig{
    MaxAttempts:  3,
    InitialDelay: 1 * time.Second,
    MaxDelay:     30 * time.Second,
    Multiplier:   2.0,
}

// RetryFunc 可重试的函数类型
type RetryFunc func(ctx context.Context, attempt int) error

// ShouldRetryFunc 判断是否应该重试的函数类型
type ShouldRetryFunc func(err error) bool

// Execute 执行重试逻辑
func Execute(ctx context.Context, config *RetryConfig, fn RetryFunc, shouldRetry ShouldRetryFunc) error {
    if config == nil {
        config = DefaultRetryConfig
    }

    var lastErr error
    delay := config.InitialDelay

    for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
        // 执行函数
        err := fn(ctx, attempt)
        if err == nil {
            return nil // 成功，无需重试
        }

        lastErr = err
        
        // 检查是否应该重试
        if shouldRetry != nil && !shouldRetry(err) {
            return err // 不应该重试的错误
        }

        // 最后一次尝试失败
        if attempt == config.MaxAttempts {
            break
        }

        // 记录重试日志
        g.Log().Warningf(ctx, "操作失败，第 %d/%d 次重试, 延迟 %v: %v", 
            attempt, config.MaxAttempts, delay, err)

        // 等待重试间隔
        select {
        case <-time.After(delay):
        case <-ctx.Done():
            return ctx.Err()
        }

        // 计算下次重试延迟（指数退避）
        delay = time.Duration(float64(delay) * config.Multiplier)
        if delay > config.MaxDelay {
            delay = config.MaxDelay
        }
    }

    return gerror.Wrapf(lastErr, "重试 %d 次后仍然失败", config.MaxAttempts)
}

// ExecuteWithPVERetry PVE操作专用重试
func ExecuteWithPVERetry(ctx context.Context, fn RetryFunc) error {
    return Execute(ctx, DefaultRetryConfig, fn, func(err error) bool {
        // PVE特定的重试判断逻辑
        if gerror.HasCode(err, "timeout") || 
           gerror.HasCode(err, "connection") ||
           gerror.HasCode(err, "temporary") {
            return true
        }
        return false
    })
}
```

#### 断路器模式

创建 `server/addons/pve/library/circuitbreaker/breaker.go`：

```go
package circuitbreaker

import (
    "context"
    "sync"
    "time"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
)

// State 断路器状态
type State int

const (
    StateClosed State = iota // 关闭状态（正常）
    StateOpen                // 开放状态（熔断）
    StateHalfOpen            // 半开状态（尝试恢复）
)

// CircuitBreaker 断路器
type CircuitBreaker struct {
    name          string
    maxFailures   int           // 最大失败次数
    resetTimeout  time.Duration // 重置超时时间
    
    mutex         sync.RWMutex
    state         State
    failures      int
    lastFailTime  time.Time
    successCount  int
    requestCount  int
}

// Config 断路器配置
type Config struct {
    Name         string        `json:"name"`
    MaxFailures  int           `json:"maxFailures"`  // 最大失败次数，达到后进入开放状态
    ResetTimeout time.Duration `json:"resetTimeout"` // 开放状态持续时间，之后进入半开状态
}

// NewCircuitBreaker 创建新的断路器
func NewCircuitBreaker(config Config) *CircuitBreaker {
    if config.MaxFailures <= 0 {
        config.MaxFailures = 5
    }
    if config.ResetTimeout <= 0 {
        config.ResetTimeout = 60 * time.Second
    }

    return &CircuitBreaker{
        name:         config.Name,
        maxFailures:  config.MaxFailures,
        resetTimeout: config.ResetTimeout,
        state:        StateClosed,
    }
}

// Execute 执行操作
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func() error) error {
    if !cb.allowRequest() {
        return gerror.Newf("断路器[%s]处于开放状态，拒绝请求", cb.name)
    }

    err := fn()
    cb.recordResult(err == nil)
    
    return err
}

// allowRequest 检查是否允许请求
func (cb *CircuitBreaker) allowRequest() bool {
    cb.mutex.RLock()
    defer cb.mutex.RUnlock()

    switch cb.state {
    case StateClosed:
        return true
    case StateOpen:
        // 检查是否到了重置时间
        return time.Since(cb.lastFailTime) >= cb.resetTimeout
    case StateHalfOpen:
        return true
    default:
        return false
    }
}

// recordResult 记录执行结果
func (cb *CircuitBreaker) recordResult(success bool) {
    cb.mutex.Lock()
    defer cb.mutex.Unlock()

    if success {
        cb.onSuccess()
    } else {
        cb.onFailure()
    }
}

// onSuccess 处理成功结果
func (cb *CircuitBreaker) onSuccess() {
    switch cb.state {
    case StateClosed:
        cb.failures = 0
    case StateHalfOpen:
        cb.successCount++
        cb.requestCount++
        // 半开状态下连续成功，转为关闭状态
        if cb.successCount >= 3 {
            cb.state = StateClosed
            cb.failures = 0
            cb.successCount = 0
            cb.requestCount = 0
            g.Log().Infof(nil, "断路器[%s]恢复到关闭状态", cb.name)
        }
    }
}

// onFailure 处理失败结果
func (cb *CircuitBreaker) onFailure() {
    cb.failures++
    cb.lastFailTime = time.Now()

    switch cb.state {
    case StateClosed:
        if cb.failures >= cb.maxFailures {
            cb.state = StateOpen
            g.Log().Warningf(nil, "断路器[%s]进入开放状态，失败次数: %d", cb.name, cb.failures)
        }
    case StateHalfOpen:
        // 半开状态失败，直接回到开放状态
        cb.state = StateOpen
        cb.successCount = 0
        cb.requestCount = 0
        g.Log().Warningf(nil, "断路器[%s]从半开状态回到开放状态", cb.name)
    }
}

// GetState 获取当前状态
func (cb *CircuitBreaker) GetState() State {
    cb.mutex.RLock()
    defer cb.mutex.RUnlock()

    // 检查是否需要从开放状态转为半开状态
    if cb.state == StateOpen && time.Since(cb.lastFailTime) >= cb.resetTimeout {
        cb.mutex.RUnlock()
        cb.mutex.Lock()
        if cb.state == StateOpen && time.Since(cb.lastFailTime) >= cb.resetTimeout {
            cb.state = StateHalfOpen
            cb.successCount = 0
            cb.requestCount = 0
            g.Log().Infof(nil, "断路器[%s]进入半开状态", cb.name)
        }
        cb.mutex.Unlock()
        cb.mutex.RLock()
    }

    return cb.state
}

// GetStats 获取统计信息
func (cb *CircuitBreaker) GetStats() map[string]interface{} {
    cb.mutex.RLock()
    defer cb.mutex.RUnlock()

    return map[string]interface{}{
        "name":         cb.name,
        "state":        cb.state,
        "failures":     cb.failures,
        "successCount": cb.successCount,
        "requestCount": cb.requestCount,
        "lastFailTime": cb.lastFailTime,
    }
}
```

### 数据一致性最佳实践

#### 1. 事务边界设计
- 保持事务尽可能短小
- 避免在事务中进行远程调用
- 使用补偿事务处理分布式场景

#### 2. 幂等性设计
- 所有API接口支持幂等操作
- 使用唯一标识符防止重复处理
- 状态机设计确保操作安全

#### 3. 最终一致性
- 关键业务数据保证强一致性
- 非关键数据可接受最终一致性
- 定期数据校验和修复机制

#### 4. 监控和告警
- 实时监控数据同步状态
- 异常情况自动告警
- 提供数据修复工具

通过以上数据一致性和错误处理框架，PVE云平台能够：

1. **保证数据一致性**：通过事务管理和数据同步确保数据准确性
2. **优雅处理错误**：统一错误码和错误处理流程
3. **提高系统稳定性**：重试机制和断路器防止级联故障
4. **便于问题诊断**：详细的错误日志和监控指标

## 🧪 测试指南

### 单元测试

创建 `server/internal/logic/pve/instances_test.go`：

```go
package pve_test

import (
    "context"
    "testing"

    "github.com/gogf/gf/v2/test/gtest"
    "hotgo/internal/logic/pve"
    "hotgo/internal/model/input/pvein"
)

func TestInstances_Create(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        // 准备测试数据
        req := &pvein.InstanceCreateInp{
            NodeID:     1,
            Name:       "test-vm",
            TemplateID: 1,
            ProductID:  1,
            Period:     1,
            PeriodType: "month",
        }

        // 执行测试
        res, err := pve.NewInstances().Create(ctx, req)
        
        // 验证结果
        t.AssertNil(err)
        t.AssertGT(res.InstanceID, 0)
        t.AssertGT(res.VMID, 0)
    })
}

func TestInstances_List(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        req := &pvein.InstanceListInp{
            Page: 1,
            Size: 10,
        }

        res, err := pve.NewInstances().List(ctx, req)
        
        t.AssertNil(err)
        t.AssertGE(len(res.List), 0)
    })
}
```

### API测试

创建 `tests/api/pve_test.http`：

```http
### 获取PVE节点列表
GET http://localhost:8000/admin/pve/nodes
Authorization: Bearer {{token}}

### 获取虚拟机实例列表
GET http://localhost:8000/admin/pve/instances?page=1&size=20
Authorization: Bearer {{token}}

### 创建虚拟机实例
POST http://localhost:8000/admin/pve/instances
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "nodeId": 1,
  "name": "test-vm-001",
  "description": "测试虚拟机",
  "templateId": 1,
  "productId": 1,
  "period": 1,
  "periodType": "month",
  "autoRenew": false
}

### 启动虚拟机
POST http://localhost:8000/admin/pve/instances/1/action
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "action": "start"
}

### 获取监控数据
GET http://localhost:8000/admin/pve/instances/1/monitoring?metrics=cpu,memory&period=1h
Authorization: Bearer {{token}}
```

## 🚀 部署说明

### Docker部署

创建 `docker-compose.pve.yml`：

```yaml
version: '3.8'

services:
  hotgo-pve-api:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - GF_GCFG_FILE=config.yaml
      - DATABASE_URL=mysql://hotgo:password@mysql:3306/hotgo_pve
      - REDIS_URL=redis://redis:6379/3
    volumes:
      - ./server/manifest/config:/app/manifest/config
      - ./server/logs:/app/logs
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  hotgo-pve-web:
    build:
      context: ./web
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:8000
    depends_on:
      - hotgo-pve-api
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=hotgo_pve
      - MYSQL_USER=hotgo
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server/storage/data:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  influxdb:
    image: influxdb:2.7
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=adminpassword
      - DOCKER_INFLUXDB_INIT_ORG=hotgo
      - DOCKER_INFLUXDB_INIT_BUCKET=pve_monitoring
    volumes:
      - influxdb_data:/var/lib/influxdb2
    ports:
      - "8086:8086"
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
```

### 部署脚本

创建 `scripts/deploy.sh`：

```bash
#!/bin/bash

set -e

echo "🚀 开始部署HotGo-PVE云平台..."

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 构建镜像
echo "📦 构建Docker镜像..."
docker-compose -f docker-compose.pve.yml build

# 启动服务
echo "🔧 启动服务..."
docker-compose -f docker-compose.pve.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🔍 检查服务状态..."
if curl -f http://localhost:8000/admin/common/site >/dev/null 2>&1; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    docker-compose -f docker-compose.pve.yml logs hotgo-pve-api
    exit 1
fi

if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败"
    docker-compose -f docker-compose.pve.yml logs hotgo-pve-web
    exit 1
fi

echo "🎉 部署完成！"
echo "🌐 前端地址: http://localhost:3000"
echo "🔗 后端地址: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/swagger"
```

## ❓ 常见问题

### 1. PVE连接问题

**问题**: 无法连接到PVE节点
**解决方案**:
- 检查PVE节点网络连通性
- 验证API用户权限
- 确认SSL证书配置
- 查看防火墙设置

### 2. 虚拟机创建失败

**问题**: 创建虚拟机时报错
**解决方案**:
- 检查PVE节点资源是否充足
- 验证模板是否存在
- 确认存储空间是否足够
- 查看PVE日志详细错误信息

### 3. 监控数据不更新

**问题**: 监控图表无数据显示
**解决方案**:
- 检查WebSocket连接状态
- 验证监控数据采集服务是否运行
- 确认数据库连接正常
- 查看监控服务日志

### 4. 前端界面异常

**问题**: 页面显示异常或报错
**解决方案**:
- 清除浏览器缓存
- 检查控制台错误信息
- 验证API接口返回数据格式
- 确认环境变量配置正确

### 5. 权限访问问题

**问题**: 用户无法访问某些功能
**解决方案**:
- 检查用户角色权限配置
- 验证JWT Token有效性
- 确认API接口权限设置
- 查看权限认证日志

---

## 📞 技术支持

如果在开发过程中遇到问题，可以通过以下方式获取帮助：

1. **文档查阅**: 查看项目内置文档和API文档
2. **Issue提交**: 在GitHub仓库提交Issue
3. **社区讨论**: 加入技术交流群
4. **专业支持**: 联系技术支持团队

---

*本文档将随着项目开发进度持续更新，请关注最新版本。*
