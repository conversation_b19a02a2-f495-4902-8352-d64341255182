# PVE云平台官网和用户系统设计方案

## 📋 整体架构设计

### 应用入口架构
```
HotGo-PVE完整架构:
├── 🌐 官网首页 (Website) - 域名根目录 /
├── 👤 用户中心 (User Center) - /user
├── 🛠️ 管理后台 (Admin) - /admin  
├── 🔌 API接口 (API) - /api
├── 📡 WebSocket服务 - /socket
```

### 系统角色划分
```
用户角色体系:
├── 游客 (Visitor) - 浏览官网，注册账号
├── 普通用户 (User) - 购买和管理云服务器
├── 代理商 (Agent) - 销售云服务器，查看下级
├── 管理员 (Admin) - 系统管理，运营数据
├── 超管 (SuperAdmin) - 系统配置，权限控制
```

## 🌐 官网首页 (Website)

### 页面结构设计

#### 1. 首页 (Homepage)
```html
网站结构:
├── 🎯 首页横幅 - Hero Section
│   ├── 主标题："专业的云服务器解决方案"
│   ├── 副标题："基于PVE的高性能云计算平台"
│   ├── CTA按钮："立即体验" "了解更多"
│   └── 产品演示视频/动画
├── ✨ 产品特色 - Features
│   ├── 高性能计算
│   ├── 灵活配置
│   ├── 安全可靠  
│   └── 7x24服务
├── 💰 产品套餐 - Pricing
│   ├── 基础型
│   ├── 标准型
│   ├── 企业型
│   └── 定制型
├── 📊 解决方案 - Solutions
│   ├── 网站托管
│   ├── 应用开发
│   ├── 数据备份
│   └── 企业办公
├── 🏆 客户案例 - Cases
├── 📰 新闻资讯 - News
└── 📞 联系我们 - Contact
```

#### 2. 产品页面
- **云服务器ECS** - 详细介绍虚拟机产品
- **产品价格** - 透明化价格展示和计算器
- **技术文档** - API文档、使用教程
- **帮助中心** - 常见问题、故障排除

#### 3. 用户服务
- **注册登录** - 邮箱/手机注册，第三方登录
- **实名认证** - 个人/企业认证流程
- **客服支持** - 在线客服、工单系统

### 技术实现方案

#### 前端技术栈
```yaml
框架: Nuxt.js 3 (Vue3 + SSR)
UI库: TailwindCSS + HeadlessUI
动画: GSAP / Framer Motion
图标: Heroicons / Lucide
SEO: Nuxt SEO模块
部署: Vercel / Cloudflare Pages
```

#### 项目结构
```
website/                    # 官网项目
├── pages/                  # 页面路由
│   ├── index.vue          # 首页
│   ├── products/          # 产品页面
│   ├── pricing/           # 价格页面
│   ├── solutions/         # 解决方案
│   ├── about/             # 关于我们
│   ├── news/              # 新闻资讯
│   ├── contact/           # 联系我们
│   ├── login.vue          # 登录页面
│   └── register.vue       # 注册页面
├── components/            # 公共组件
│   ├── Layout/           # 布局组件
│   ├── Hero/             # 首页横幅
│   ├── Features/         # 特色功能
│   ├── Pricing/          # 价格卡片
│   ├── Testimonials/     # 客户证言
│   └── Contact/          # 联系表单
├── assets/               # 静态资源
│   ├── images/          # 图片素材
│   ├── videos/          # 视频素材
│   └── css/             # 样式文件
├── plugins/             # 插件配置
├── middleware/          # 中间件
├── composables/         # 组合式函数
└── nuxt.config.ts       # Nuxt配置
```

## 👤 用户中心 (User Center)

### 功能模块设计

#### 1. 个人信息管理
```yaml
个人中心:
  基本信息:
    - 头像上传
    - 昵称修改
    - 联系方式
    - 邮箱绑定
  安全设置:
    - 密码修改
    - 手机绑定
    - 二次验证
    - 登录记录
  实名认证:
    - 个人认证
    - 企业认证
    - 认证状态
```

#### 2. 云服务器管理
```yaml
ECS管理:
  实例列表:
    - 实例状态
    - 配置信息
    - 到期时间
    - 操作按钮
  实例详情:
    - 基本信息
    - 配置变更
    - 监控数据
    - 操作日志
  远程连接:
    - VNC控制台
    - SSH终端
    - 文件传输
```

#### 3. 财务管理
```yaml
财务中心:
  账户余额:
    - 余额查询
    - 充值记录
    - 消费明细
  订单管理:
    - 待支付订单
    - 历史订单
    - 退款申请
  发票管理:
    - 发票申请
    - 发票记录
    - 电子发票
```

#### 4. 工单服务
```yaml
客服支持:
  工单系统:
    - 提交工单
    - 工单状态
    - 历史记录
  知识库:
    - 使用教程
    - 常见问题
    - 视频教程
```

### 前端架构设计

#### 用户中心技术栈
```yaml
框架: Vue 3 + Vite
UI库: Ant Design Vue / Element Plus
状态管理: Pinia
路由: Vue Router 4
HTTP: Axios
实时通信: Socket.io / WebSocket
图表: ECharts
终端: Xterm.js
```

#### 用户中心项目结构
```
user-center/               # 用户中心项目
├── src/
│   ├── views/            # 页面组件
│   │   ├── dashboard/    # 控制台首页
│   │   ├── profile/      # 个人信息
│   │   ├── ecs/          # 云服务器管理
│   │   ├── finance/      # 财务管理
│   │   ├── ticket/       # 工单系统
│   │   └── settings/     # 账户设置
│   ├── components/       # 公共组件
│   │   ├── Layout/       # 布局组件
│   │   ├── ECS/          # ECS组件
│   │   ├── Charts/       # 图表组件
│   │   └── Terminal/     # 终端组件
│   ├── api/              # API接口
│   ├── store/            # 状态管理
│   ├── router/           # 路由配置
│   ├── utils/            # 工具函数
│   └── types/            # 类型定义
└── public/               # 静态资源
```

## 🔗 后端API扩展

### 用户相关API接口

#### 1. 用户认证接口
```go
// 用户注册
POST /api/auth/register
{
  "username": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "phone": "13800138000",
  "verifyCode": "123456"
}

// 用户登录
POST /api/auth/login
{
  "username": "<EMAIL>",
  "password": "password123",
  "captcha": "ABCD"
}

// 退出登录
POST /api/auth/logout

// 刷新Token
POST /api/auth/refresh
```

#### 2. 用户信息接口
```go
// 获取用户信息
GET /api/user/profile

// 更新用户信息
PUT /api/user/profile
{
  "nickname": "昵称",
  "avatar": "头像URL",
  "phone": "手机号"
}

// 修改密码
PUT /api/user/password
{
  "oldPassword": "旧密码",
  "newPassword": "新密码"
}

// 实名认证
POST /api/user/verify
{
  "type": "personal", // personal/enterprise
  "realName": "真实姓名",
  "idCard": "身份证号",
  "idCardFront": "身份证正面",
  "idCardBack": "身份证背面"
}
```

#### 3. ECS用户接口
```go
// 获取用户ECS列表
GET /api/user/ecs/instances

// 创建ECS实例
POST /api/user/ecs/instances
{
  "productId": 1,
  "templateId": 1,
  "period": 1,
  "periodType": "month",
  "quantity": 1
}

// ECS实例操作
POST /api/user/ecs/instances/:id/action
{
  "action": "start", // start/stop/reboot/reset
  "force": false
}

// 获取ECS监控数据
GET /api/user/ecs/instances/:id/monitoring

// 获取VNC信息
GET /api/user/ecs/instances/:id/vnc
```

#### 4. 财务相关接口
```go
// 获取账户余额
GET /api/user/finance/balance

// 充值订单
POST /api/user/finance/recharge
{
  "amount": 100.00,
  "paymentMethod": "alipay"
}

// 订单列表
GET /api/user/finance/orders

// 消费明细
GET /api/user/finance/bills

// 发票申请
POST /api/user/finance/invoice
{
  "orderId": 123,
  "type": "electronic",
  "title": "发票抬头"
}
```

## 🎨 UI/UX设计参考

### 官网首页设计风格

#### 1. 色彩方案
```css
/* 主色调 */
:root {
  --primary-color: #2563eb;      /* 蓝色主色 */
  --secondary-color: #1e40af;    /* 深蓝辅助色 */
  --accent-color: #06b6d4;       /* 青色强调色 */
  --success-color: #10b981;      /* 绿色成功色 */
  --warning-color: #f59e0b;      /* 黄色警告色 */
  --error-color: #ef4444;        /* 红色错误色 */
  --gray-50: #f9fafb;           /* 浅灰背景 */
  --gray-900: #111827;          /* 深灰文字 */
}
```

#### 2. 首页Hero区域
```vue
<template>
  <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-transparent"></div>
      <div class="absolute top-0 left-0 w-full h-full">
        <div class="floating-shapes"></div>
      </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
      <div class="text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
          专业的
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">
            云服务器
          </span>
          解决方案
        </h1>
        <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
          基于Proxmox VE的高性能云计算平台，提供稳定可靠的虚拟化服务，
          助力您的业务快速发展
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button class="btn-primary">
            立即体验
          </button>
          <button class="btn-secondary">
            查看演示
          </button>
        </div>
      </div>
      
      <!-- 产品预览 -->
      <div class="mt-16">
        <div class="relative mx-auto max-w-4xl">
          <img 
            src="/images/dashboard-preview.png" 
            alt="用户控制台预览"
            class="rounded-lg shadow-2xl ring-1 ring-white/10"
          />
        </div>
      </div>
    </div>
  </section>
</template>
```

#### 3. 产品特色区域
```vue
<template>
  <section class="py-24 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          为什么选择我们
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          专业的技术团队，丰富的运维经验，为您提供最优质的云服务器体验
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div v-for="feature in features" :key="feature.title" 
             class="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-xl flex items-center justify-center">
            <component :is="feature.icon" class="w-8 h-8 text-blue-600" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">
            {{ feature.title }}
          </h3>
          <p class="text-gray-600">
            {{ feature.description }}
          </p>
        </div>
      </div>
    </div>
  </section>
</template>
```

### 用户中心设计风格

#### 1. 侧边栏导航
```vue
<template>
  <div class="flex h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <div class="hidden md:flex md:w-64 md:flex-col">
      <div class="flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r">
        <!-- Logo -->
        <div class="flex items-center px-4 mb-8">
          <img class="h-8 w-auto" src="/logo.svg" alt="Logo" />
          <span class="ml-2 text-xl font-semibold text-gray-900">云控制台</span>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="flex-1 px-2 space-y-1">
          <router-link 
            v-for="item in navigation" 
            :key="item.name"
            :to="item.href"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path === item.href }"
          >
            <component :is="item.icon" class="w-5 h-5" />
            <span>{{ item.name }}</span>
          </router-link>
        </nav>
      </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- 顶部导航栏 -->
      <header class="bg-white shadow-sm border-b">
        <div class="flex items-center justify-between px-6 py-4">
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ pageTitle }}
          </h1>
          <UserDropdown />
        </div>
      </header>
      
      <!-- 页面内容 -->
      <main class="flex-1 overflow-y-auto p-6">
        <router-view />
      </main>
    </div>
  </div>
</template>
```

#### 2. ECS实例卡片
```vue
<template>
  <div class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
    <!-- 实例头部 -->
    <div class="p-6 border-b">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <ServerIcon class="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-900">
              {{ instance.name }}
            </h3>
            <p class="text-sm text-gray-500">
              实例ID: {{ instance.id }}
            </p>
          </div>
        </div>
        <StatusBadge :status="instance.status" />
      </div>
    </div>
    
    <!-- 实例信息 -->
    <div class="p-6">
      <dl class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <dt class="text-gray-500">配置</dt>
          <dd class="font-medium text-gray-900">
            {{ instance.cpuCores }}核 {{ formatMemory(instance.memory) }}
          </dd>
        </div>
        <div>
          <dt class="text-gray-500">IP地址</dt>
          <dd class="font-medium text-gray-900">
            {{ instance.ipAddress || '暂无' }}
          </dd>
        </div>
        <div>
          <dt class="text-gray-500">创建时间</dt>
          <dd class="font-medium text-gray-900">
            {{ formatDate(instance.createdAt) }}
          </dd>
        </div>
        <div>
          <dt class="text-gray-500">到期时间</dt>
          <dd class="font-medium" :class="expiredClass">
            {{ formatDate(instance.expiredAt) }}
          </dd>
        </div>
      </dl>
    </div>
    
    <!-- 操作按钮 -->
    <div class="px-6 py-4 bg-gray-50 border-t">
      <div class="flex space-x-2">
        <button 
          v-if="instance.status === 'stopped'"
          class="btn-sm btn-success"
          @click="startInstance"
        >
          启动
        </button>
        <button 
          v-if="instance.status === 'running'"
          class="btn-sm btn-warning"
          @click="stopInstance"
        >
          停止
        </button>
        <button class="btn-sm btn-outline">
          控制台
        </button>
        <DropdownMenu :options="moreActions" />
      </div>
    </div>
  </div>
</template>
```

## 🚀 开发实施计划

### 第一阶段：官网开发 (2周)

#### Week 1: 官网基础
- [ ] 初始化Nuxt.js项目
- [ ] 设计系统搭建（色彩、字体、组件）
- [ ] 首页Hero区域开发
- [ ] 导航和页脚组件

#### Week 2: 官网完善
- [ ] 产品介绍页面
- [ ] 价格展示页面
- [ ] 用户注册登录
- [ ] SEO优化和部署

### 第二阶段：用户中心开发 (3周)

#### Week 3: 用户中心基础
- [ ] Vue3项目初始化
- [ ] 路由和状态管理
- [ ] 布局和导航组件
- [ ] 个人信息管理

#### Week 4-5: 核心功能
- [ ] ECS实例管理界面
- [ ] 财务管理模块
- [ ] 工单系统
- [ ] 实时监控图表

### 第三阶段：后端API开发 (2周)

#### Week 6-7: API接口
- [ ] 用户认证API
- [ ] 用户信息API
- [ ] ECS用户API
- [ ] 财务相关API

## 📱 移动端适配

### 响应式设计
- **桌面端**: 1200px+ 完整功能
- **平板端**: 768px-1199px 适配布局
- **手机端**: <768px 核心功能

### PWA支持
- 离线缓存
- 推送通知
- 安装到桌面

## 🔒 安全考虑

### 前端安全
- XSS防护
- CSRF令牌
- 内容安全策略(CSP)
- HTTPS强制

### 后端安全
- JWT认证
- 接口限流
- 参数验证
- SQL注入防护

---

这个设计方案将为您的PVE云平台提供完整的前台展示和用户管理能力，与现有的管理后台形成完整的业务闭环。您觉得这个方案如何？需要我详细展开某个部分吗？
