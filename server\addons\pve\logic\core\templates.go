// Package logic
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

type sPveTemplates struct{}

func init() {
	service.RegisterPveTemplates(NewPveTemplates())
}

func NewPveTemplates() service.IPveTemplates {
	return &sPveTemplates{}
}

// Create 创建模板
func (s *sPveTemplates) Create(ctx context.Context, in *input.TemplateCreateInp) (out *input.TemplateCreateOut, err error) {
	// 验证模板名称唯一性
	count, err := g.DB().Model("hg_pve_templates").Where("name", in.Name).Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, fmt.Errorf("模板名称已存在")
	}

	// 验证节点是否存在
	count, err = g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", in.NodeID, 1).Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, fmt.Errorf("节点不存在或状态异常")
	}

	// 插入模板数据
	result, err := g.DB().Model("hg_pve_templates").Data(g.Map{
		"name":        in.Name,
		"description": in.Description,
		"os_type":     in.OsType,
		"os_version":  in.OsVersion,
		"template_id": in.TemplateID,
		"node_id":     in.NodeID,
		"cpu_cores":   in.CpuCores,
		"memory_mb":   in.MemoryMb,
		"disk_gb":     in.DiskGb,
		"price":       in.Price,
		"status":      in.Status,
		"sort":        in.Sort,
		"created_at":  gtime.Now(),
		"updated_at":  gtime.Now(),
	}).Insert()

	if err != nil {
		return nil, err
	}

	id, _ := result.LastInsertId()
	return &input.TemplateCreateOut{
		ID: uint64(id),
	}, nil
}

// Edit 编辑模板
func (s *sPveTemplates) Edit(ctx context.Context, in *input.TemplateEditInp) (err error) {
	// 检查模板是否存在
	count, err := g.DB().Model("hg_pve_templates").Where("id", in.ID).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return fmt.Errorf("模板不存在")
	}

	// 验证模板名称唯一性（排除自己）
	count, err = g.DB().Model("hg_pve_templates").Where("name = ? AND id != ?", in.Name, in.ID).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("模板名称已存在")
	}

	// 验证节点是否存在
	count, err = g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", in.NodeID, 1).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return fmt.Errorf("节点不存在或状态异常")
	}

	// 更新模板数据
	_, err = g.DB().Model("hg_pve_templates").Data(g.Map{
		"name":        in.Name,
		"description": in.Description,
		"os_type":     in.OsType,
		"os_version":  in.OsVersion,
		"template_id": in.TemplateID,
		"node_id":     in.NodeID,
		"cpu_cores":   in.CpuCores,
		"memory_mb":   in.MemoryMb,
		"disk_gb":     in.DiskGb,
		"price":       in.Price,
		"status":      in.Status,
		"sort":        in.Sort,
		"updated_at":  gtime.Now(),
	}).Where("id", in.ID).Update()

	return err
}

// Delete 删除模板
func (s *sPveTemplates) Delete(ctx context.Context, in *input.TemplateDeleteInp) (err error) {
	// 检查是否有关联的实例
	count, err := g.DB().Model("hg_pve_instances").
		InnerJoin("hg_pve_templates", "hg_pve_instances.os_template = hg_pve_templates.name").
		Where("hg_pve_templates.id", in.ID).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("该模板下还有虚拟机实例，无法删除")
	}

	// 删除模板
	_, err = g.DB().Model("hg_pve_templates").Where("id", in.ID).Delete()
	return err
}

// View 查看模板详情
func (s *sPveTemplates) View(ctx context.Context, in *input.TemplateViewInp) (out *input.TemplateViewOut, err error) {
	var template *input.TemplateViewOut
	err = g.DB().Model("hg_pve_templates t").
		LeftJoin("hg_pve_nodes n", "t.node_id = n.id").
		Fields("t.*, n.name as node_name").
		Where("t.id", in.ID).Scan(&template)
	if err != nil {
		return nil, err
	}
	if template == nil {
		return nil, fmt.Errorf("模板不存在")
	}

	return template, nil
}

// List 获取模板列表
func (s *sPveTemplates) List(ctx context.Context, in *input.TemplateListInp) (out *input.TemplateListOut, err error) {
	m := g.DB().Model("hg_pve_templates t").
		LeftJoin("hg_pve_nodes n", "t.node_id = n.id").
		Fields("t.*, n.name as node_name")

	// 条件过滤
	if in.Name != "" {
		m = m.WhereLike("t.name", "%"+in.Name+"%")
	}
	if in.OsType != "" {
		m = m.Where("t.os_type", in.OsType)
	}
	if in.NodeID > 0 {
		m = m.Where("t.node_id", in.NodeID)
	}
	if in.Status > 0 {
		m = m.Where("t.status", in.Status)
	}

	// 排序
	if in.OrderBy != "" {
		orderDir := "ASC"
		if in.OrderDir != "" {
			orderDir = in.OrderDir
		}
		m = m.Order("t." + in.OrderBy + " " + orderDir)
	} else {
		m = m.Order("t.sort ASC, t.created_at DESC")
	}

	// 分页处理
	totalCount, err := m.Clone().Count()
	if err != nil {
		return out, err
	}

	if &in.PageReq != nil {
		m = m.Page(in.PageReq.GetPage(), in.PageReq.GetPerPage())
	}

	out = &input.TemplateListOut{}
	err = m.Scan(&out.List)
	if err != nil {
		return out, err
	}

	if &in.PageReq != nil {
		out.PageRes.Pack(&in.PageReq, totalCount)
	}

	return out, nil
}

// Sync 同步PVE模板
func (s *sPveTemplates) Sync(ctx context.Context, in *input.TemplateSyncInp) (out *input.TemplateSyncOut, err error) {
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", in.NodeID, 1).Scan(&nodeData)
	if err != nil {
		return nil, err
	}
	if nodeData == nil {
		return &input.TemplateSyncOut{
			Success: false,
			Message: "节点不存在或状态异常",
			Count:   0,
		}, nil
	}

	// 创建PVE客户端
	client, err := s.createPVEClient(nodeData)
	if err != nil {
		return &input.TemplateSyncOut{
			Success: false,
			Message: fmt.Sprintf("创建PVE客户端失败: %s", err.Error()),
			Count:   0,
		}, nil
	}

	// 获取PVE模板列表
	vms, err := client.GetVMs(ctx, gconv.String(nodeData["name"]))
	if err != nil {
		return &input.TemplateSyncOut{
			Success: false,
			Message: fmt.Sprintf("获取PVE模板失败: %s", err.Error()),
			Count:   0,
		}, nil
	}

	var templates []*input.PVETemplateModel
	for _, vm := range vms {
		if vm.Template == 1 { // 只处理模板
			templates = append(templates, &input.PVETemplateModel{
				VMID:        vm.VMID,
				Name:        vm.Name,
				Description: "",
				OsType:      "linux", // 默认值，需要根据实际情况判断
				Node:        gconv.String(nodeData["name"]),
				IsTemplate:  true,
				Status:      vm.Status,
			})
		}
	}

	return &input.TemplateSyncOut{
		Success: true,
		Message: "同步成功",
		Count:   len(templates),
		List:    templates,
	}, nil
}

// Import 导入模板
func (s *sPveTemplates) Import(ctx context.Context, in *input.TemplateImportInp) (out *input.TemplateImportOut, err error) {
	var importCount, failedCount int
	var failedList []string

	// 验证节点是否存在
	count, err := g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", in.NodeID, 1).Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return &input.TemplateImportOut{
			Success:     false,
			Message:     "节点不存在或状态异常",
			ImportCount: 0,
			FailedCount: 0,
		}, nil
	}

	for _, template := range in.Templates {
		// 检查模板名称是否已存在
		count, err := g.DB().Model("hg_pve_templates").Where("name", template.Name).Count()
		if err != nil || count > 0 {
			failedCount++
			failedList = append(failedList, template.Name+"(名称已存在)")
			continue
		}

		// 插入模板
		_, err = g.DB().Model("hg_pve_templates").Data(g.Map{
			"name":        template.Name,
			"description": template.Description,
			"os_type":     template.OsType,
			"os_version":  template.OsVersion,
			"template_id": template.VMID,
			"node_id":     in.NodeID,
			"cpu_cores":   template.CpuCores,
			"memory_mb":   template.MemoryMb,
			"disk_gb":     template.DiskGb,
			"price":       template.Price,
			"status":      template.Status,
			"sort":        template.Sort,
			"created_at":  gtime.Now(),
			"updated_at":  gtime.Now(),
		}).Insert()

		if err != nil {
			failedCount++
			failedList = append(failedList, template.Name+"(导入失败)")
		} else {
			importCount++
		}
	}

	return &input.TemplateImportOut{
		Success:     importCount > 0,
		Message:     fmt.Sprintf("导入完成，成功%d个，失败%d个", importCount, failedCount),
		ImportCount: importCount,
		FailedCount: failedCount,
		FailedList:  failedList,
	}, nil
}

// GetSelectList 获取模板选择列表
func (s *sPveTemplates) GetSelectList(ctx context.Context, in *input.TemplateSelectInp) (out *input.TemplateSelectOut, err error) {
	m := g.DB().Model("hg_pve_templates t").
		LeftJoin("hg_pve_nodes n", "t.node_id = n.id").
		Fields("t.id, t.name, t.description, t.os_type, t.os_version, t.node_id, n.name as node_name, t.cpu_cores, t.memory_mb, t.disk_gb, t.price")

	// 条件过滤
	if in.NodeID > 0 {
		m = m.Where("t.node_id", in.NodeID)
	}
	
	status := 1
	if in.Status > 0 {
		status = in.Status
	}
	m = m.Where("t.status", status)

	// 排序
	m = m.Order("t.sort ASC, t.created_at ASC")

	var list []*input.TemplateSelectModel
	err = m.Scan(&list)
	if err != nil {
		return nil, err
	}

	return &input.TemplateSelectOut{
		List: list,
	}, nil
}

// createPVEClient 创建PVE客户端 (复用实例管理的方法)
func (s *sPveTemplates) createPVEClient(nodeData g.Map) (*pveclient.Client, error) {
	// 这里引用pveclient包
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	return pveclient.NewClient(config)
}