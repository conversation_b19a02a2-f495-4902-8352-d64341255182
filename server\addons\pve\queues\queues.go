// Package queues
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package queues

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
	"hotgo/internal/consts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/adminin"
	adminService "hotgo/internal/service"
)

func init() {
	// 注册队列消费者
	registerQueueConsumers()
}

// registerQueueConsumers 注册队列消费者
func registerQueueConsumers() {
	ctx := context.Background()

	// 实现队列消费者注册逻辑
	// 当HotGo框架支持消息队列时，可以在这里注册具体的消费者
	// 目前使用内存处理方式
	g.Log().Info(ctx, "PVE插件消息队列注册完成 - 使用内存处理模式")
}

// InstanceActionMessage 实例操作消息
type InstanceActionMessage struct {
	InstanceID uint64 `json:"instanceId"`
	Action     string `json:"action"`
	UserID     uint64 `json:"userId"`
	Timestamp  int64  `json:"timestamp"`
}

// handleInstanceAction 处理实例操作
func handleInstanceAction(ctx context.Context, data []byte) error {
	var msg InstanceActionMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		g.Log().Error(ctx, "解析实例操作消息失败:", err)
		return err
	}

	g.Log().Info(ctx, "处理实例操作消息:", g.Map{
		"instanceId": msg.InstanceID,
		"action":     msg.Action,
		"userId":     msg.UserID,
	})

	// 实现具体的实例操作逻辑
	switch msg.Action {
	case "start":
		_, err := service.PveInstances().Start(ctx, &input.InstanceOperationInp{ID: msg.InstanceID})
		if err != nil {
			g.Log().Error(ctx, "启动实例失败:", err)
			return err
		}
	case "stop":
		_, err := service.PveInstances().Stop(ctx, &input.InstanceOperationInp{ID: msg.InstanceID})
		if err != nil {
			g.Log().Error(ctx, "停止实例失败:", err)
			return err
		}
	case "restart":
		_, err := service.PveInstances().Restart(ctx, &input.InstanceOperationInp{ID: msg.InstanceID})
		if err != nil {
			g.Log().Error(ctx, "重启实例失败:", err)
			return err
		}
	default:
		g.Log().Warning(ctx, "未知的实例操作:", msg.Action)
	}

	return nil
}

// InstanceCreateMessage 实例创建消息
type InstanceCreateMessage struct {
	InstanceID uint64 `json:"instanceId"`
	VMID       int    `json:"vmid"`
	UserID     uint64 `json:"userId"`
	NodeID     uint64 `json:"nodeId"`
	TaskID     string `json:"taskId"`
}

// handleInstanceCreate 处理实例创建
func handleInstanceCreate(ctx context.Context, data []byte) error {
	var msg InstanceCreateMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		g.Log().Error(ctx, "解析实例创建消息失败:", err)
		return err
	}

	g.Log().Info(ctx, "处理实例创建消息:", g.Map{
		"instanceId": msg.InstanceID,
		"vmid":       msg.VMID,
		"userId":     msg.UserID,
		"nodeId":     msg.NodeID,
		"taskId":     msg.TaskID,
	})

	// 实现实例创建后的处理逻辑
	// 1. 更新实例状态为正在创建
	_, err := g.DB().Model("hg_pve_instances").
		Data(g.Map{
			"status":     "creating",
			"updated_at": gtime.Now(),
		}).
		Where("id", msg.InstanceID).
		Update()
	if err != nil {
		g.Log().Error(ctx, "更新实例创建状态失败:", err)
		return err
	}

	// 2. 发送创建通知
	err = sendInstanceCreateNotification(ctx, msg.InstanceID, msg.UserID, "creating")
	if err != nil {
		g.Log().Error(ctx, "发送实例创建通知失败:", err)
	}

	return nil
}

// InstanceDeleteMessage 实例删除消息
type InstanceDeleteMessage struct {
	InstanceID uint64 `json:"instanceId"`
	VMID       int    `json:"vmid"`
	UserID     uint64 `json:"userId"`
	NodeID     uint64 `json:"nodeId"`
	TaskID     string `json:"taskId"`
}

// handleInstanceDelete 处理实例删除
func handleInstanceDelete(ctx context.Context, data []byte) error {
	var msg InstanceDeleteMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		g.Log().Error(ctx, "解析实例删除消息失败:", err)
		return err
	}

	g.Log().Info(ctx, "处理实例删除消息:", g.Map{
		"instanceId": msg.InstanceID,
		"vmid":       msg.VMID,
		"userId":     msg.UserID,
		"nodeId":     msg.NodeID,
		"taskId":     msg.TaskID,
	})

	// 实现实例删除后的处理逻辑
	// 1. 更新实例状态为正在删除
	_, err := g.DB().Model("hg_pve_instances").
		Data(g.Map{
			"status":     "deleting",
			"updated_at": gtime.Now(),
		}).
		Where("id", msg.InstanceID).
		Update()
	if err != nil {
		g.Log().Error(ctx, "更新实例删除状态失败:", err)
		return err
	}

	// 2. 清理相关快照数据
	_, err = g.DB().Model("hg_pve_snapshots").
		Where("instance_id", msg.InstanceID).
		Delete()
	if err != nil {
		g.Log().Error(ctx, "清理实例快照数据失败:", err)
	}

	// 3. 发送删除通知
	err = sendInstanceDeleteNotification(ctx, msg.InstanceID, msg.UserID)
	if err != nil {
		g.Log().Error(ctx, "发送实例删除通知失败:", err)
	}

	return nil
}

// NodeSyncMessage 节点同步消息
type NodeSyncMessage struct {
	NodeID    uint64 `json:"nodeId"`
	Timestamp int64  `json:"timestamp"`
}

// handleNodeSync 处理节点状态同步
func handleNodeSync(ctx context.Context, data []byte) error {
	var msg NodeSyncMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		g.Log().Error(ctx, "解析节点同步消息失败:", err)
		return err
	}

	g.Log().Info(ctx, "处理节点同步消息:", g.Map{
		"nodeId": msg.NodeID,
	})

	// 实现节点状态同步逻辑
	_, err := service.PveNodes().SyncStatus(ctx, &input.NodeSyncInp{
		ID: msg.NodeID,
	})
	if err != nil {
		g.Log().Error(ctx, "节点状态同步失败:", err)
		return err
	}

	g.Log().Info(ctx, "节点状态同步完成:", g.Map{
		"nodeId": msg.NodeID,
	})

	return nil
}

// sendInstanceCreateNotification 发送实例创建通知
func sendInstanceCreateNotification(ctx context.Context, instanceID, userID uint64, status string) error {
	// 获取实例信息
	instance, err := g.DB().Model("hg_pve_instances").Where("id", instanceID).One()
	if err != nil {
		return err
	}
	if instance == nil {
		return fmt.Errorf("实例 %d 不存在", instanceID)
	}

	instanceName := gconv.String(instance["name"])
	title := "PVE实例创建通知"
	var content string

	switch status {
	case "creating":
		content = fmt.Sprintf("您的PVE实例 [%s] 正在创建中，请稍候...", instanceName)
	case "created":
		content = fmt.Sprintf("您的PVE实例 [%s] 创建成功，可以开始使用了。", instanceName)
	case "failed":
		content = fmt.Sprintf("您的PVE实例 [%s] 创建失败，请联系管理员。", instanceName)
	}

	// 发送通知
	return adminService.AdminNotice().Edit(ctx, &adminin.NoticeEditInp{
		AdminNotice: entity.AdminNotice{
			Title:   title,
			Content: content,
			Type:    consts.NoticeTypeLetter,
		},
		Receiver: []int64{int64(userID)},
	})
}

// sendInstanceDeleteNotification 发送实例删除通知
func sendInstanceDeleteNotification(ctx context.Context, instanceID, userID uint64) error {
	// 获取实例信息
	instance, err := g.DB().Model("hg_pve_instances").Where("id", instanceID).One()
	if err != nil {
		return err
	}
	if instance == nil {
		return fmt.Errorf("实例 %d 不存在", instanceID)
	}

	instanceName := gconv.String(instance["name"])
	title := "PVE实例删除通知"
	content := fmt.Sprintf("您的PVE实例 [%s] 已被删除，相关数据已清理完成。", instanceName)

	// 发送通知
	return adminService.AdminNotice().Edit(ctx, &adminin.NoticeEditInp{
		AdminNotice: entity.AdminNotice{
			Title:   title,
			Content: content,
			Type:    consts.NoticeTypeLetter,
		},
		Receiver: []int64{int64(userID)},
	})
}