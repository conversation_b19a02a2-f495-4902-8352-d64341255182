// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/internal/model/input/form"
)

// BackupListReq 获取备份列表请求
type BackupListReq struct {
	form.PageReq
	NodeID     int    `json:"nodeId" dc:"节点ID"`
	Storage    string `json:"storage" dc:"存储名称"`
	InstanceID int    `json:"instanceId" dc:"实例ID"`
	Keyword    string `json:"keyword" dc:"搜索关键词"`
}

// BackupListRes 获取备份列表响应
type BackupListRes struct {
	List []*BackupViewModel `json:"list" dc:"备份列表"`
	form.PageRes
}

// BackupViewModel 备份视图模型
type BackupViewModel struct {
	ID         uint64 `json:"id" dc:"备份ID"`
	NodeID     int    `json:"nodeId" dc:"节点ID"`
	NodeName   string `json:"nodeName" dc:"节点名称"`
	Storage    string `json:"storage" dc:"存储名称"`
	InstanceID int    `json:"instanceId" dc:"实例ID"`
	InstanceName string `json:"instanceName" dc:"实例名称"`
	FileName   string `json:"fileName" dc:"文件名"`
	Size       int64  `json:"size" dc:"大小(字节)"`
	Format     string `json:"format" dc:"格式"`
	Notes      string `json:"notes" dc:"备注"`
	CreatedAt  string `json:"createdAt" dc:"创建时间"`
}

// BackupCreateReq 创建备份请求
type BackupCreateReq struct {
	NodeID     int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	InstanceID int    `json:"instanceId" v:"required#实例ID不能为空" dc:"实例ID"`
	Storage    string `json:"storage" v:"required#存储名称不能为空" dc:"存储名称"`
	Mode       string `json:"mode" dc:"备份模式"`
	Compress   string `json:"compress" dc:"压缩方式"`
	Notes      string `json:"notes" dc:"备注"`
}

// BackupCreateRes 创建备份响应
type BackupCreateRes struct {
	TaskID string `json:"taskId" dc:"任务ID"`
}

// BackupDeleteReq 删除备份请求
type BackupDeleteReq struct {
	ID uint64 `json:"id" v:"required#备份ID不能为空" dc:"备份ID"`
}

// BackupDeleteRes 删除备份响应
type BackupDeleteRes struct {
	TaskID string `json:"taskId" dc:"任务ID"`
}

// BackupRestoreReq 恢复备份请求
type BackupRestoreReq struct {
	ID       uint64 `json:"id" v:"required#备份ID不能为空" dc:"备份ID"`
	NewVMID  int    `json:"newVmid" dc:"新虚拟机ID"`
	Storage  string `json:"storage" dc:"存储名称"`
	Unique   bool   `json:"unique" dc:"是否唯一"`
}

// BackupRestoreRes 恢复备份响应
type BackupRestoreRes struct {
	TaskID string `json:"taskId" dc:"任务ID"`
}
