// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

// 集群管理相关输入输出模型

// ClusterStatusInp 获取集群状态输入
type ClusterStatusInp struct {
}

// ClusterStatusModel 集群状态输出
type ClusterStatusModel struct {
	Quorate int    `json:"quorate"`
	Nodes   int    `json:"nodes"`
	Name    string `json:"name"`
	Version string `json:"version"`
	ID      string `json:"id"`
}

// ClusterResourcesInp 获取集群资源输入
type ClusterResourcesInp struct {
	Type string `json:"type"`
}

// ClusterResource 集群资源
type ClusterResource struct {
	ID      string  `json:"id"`
	Type    string  `json:"type"`
	Status  string  `json:"status"`
	Node    string  `json:"node,omitempty"`
	VMID    int     `json:"vmid,omitempty"`
	Name    string  `json:"name,omitempty"`
	CPU     float64 `json:"cpu,omitempty"`
	MaxCPU  int     `json:"maxcpu,omitempty"`
	Mem     int64   `json:"mem,omitempty"`
	MaxMem  int64   `json:"maxmem,omitempty"`
	Disk    int64   `json:"disk,omitempty"`
	MaxDisk int64   `json:"maxdisk,omitempty"`
	Uptime  int     `json:"uptime,omitempty"`
}

// ClusterResourcesModel 集群资源输出
type ClusterResourcesModel struct {
	List []*ClusterResource `json:"list"`
}

// ClusterNodesInp 获取集群节点输入
type ClusterNodesInp struct {
}

// ClusterNode 集群节点
type ClusterNode struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	IP         string `json:"ip"`
	Level      string `json:"level"`
	Local      int    `json:"local"`
	NodeID     int    `json:"nodeid"`
	Online     int    `json:"online"`
	Ring0Addr  string `json:"ring0_addr"`
}

// ClusterNodesModel 集群节点输出
type ClusterNodesModel struct {
	List []*ClusterNode `json:"list"`
}

// ClusterNextIDInp 获取下一个可用VMID输入
type ClusterNextIDInp struct {
}

// ClusterNextIDModel 下一个可用VMID输出
type ClusterNextIDModel struct {
	VMID int `json:"vmid"`
}

// ClusterLogInp 获取集群日志输入
type ClusterLogInp struct {
	Max int `json:"max"`
}

// ClusterLog 集群日志
type ClusterLog struct {
	UID  int    `json:"uid"`
	Time int    `json:"time"`
	Pri  string `json:"pri"`
	Tag  string `json:"tag"`
	Msg  string `json:"msg"`
	Node string `json:"node"`
}

// ClusterLogModel 集群日志输出
type ClusterLogModel struct {
	List []*ClusterLog `json:"list"`
}