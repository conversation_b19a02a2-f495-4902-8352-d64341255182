// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysCronGroupDao is internal type for wrapping internal DAO implements.
type internalSysCronGroupDao = *internal.SysCronGroupDao

// sysCronGroupDao is the data access object for table hg_sys_cron_group.
// You can define custom methods on it to extend its functionality as you wish.
type sysCronGroupDao struct {
	internalSysCronGroupDao
}

var (
	// SysCronGroup is globally public accessible object for table hg_sys_cron_group operations.
	SysCronGroup = sysCronGroupDao{
		internal.NewSysCronGroupDao(),
	}
)

// Fill with you ideas below.
