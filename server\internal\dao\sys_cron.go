// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysCronDao is internal type for wrapping internal DAO implements.
type internalSysCronDao = *internal.SysCronDao

// sysCronDao is the data access object for table hg_sys_cron.
// You can define custom methods on it to extend its functionality as you wish.
type sysCronDao struct {
	internalSysCronDao
}

var (
	// SysCron is globally public accessible object for table hg_sys_cron operations.
	SysCron = sysCronDao{
		internal.NewSysCronDao(),
	}
)

// Fill with you ideas below.
