// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/internal/model/input/form"
)

// FirewallRulesReq 获取防火墙规则请求
type FirewallRulesReq struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// FirewallRulesRes 获取防火墙规则响应
type FirewallRulesRes struct {
	List []*FirewallRuleViewModel `json:"list" dc:"防火墙规则列表"`
	form.PageRes
}

// FirewallRuleViewModel 防火墙规则视图模型
type FirewallRuleViewModel struct {
	ID          uint64 `json:"id" dc:"规则ID"`
	NodeID      int    `json:"nodeId" dc:"节点ID"`
	NodeName    string `json:"nodeName" dc:"节点名称"`
	Rule        string `json:"rule" dc:"规则内容"`
	Action      string `json:"action" dc:"动作(ACCEPT/DROP/REJECT)"`
	Direction   string `json:"direction" dc:"方向(IN/OUT)"`
	Protocol    string `json:"protocol" dc:"协议"`
	Source      string `json:"source" dc:"源地址"`
	Destination string `json:"destination" dc:"目标地址"`
	Port        string `json:"port" dc:"端口"`
	Enabled     bool   `json:"enabled" dc:"是否启用"`
	CreatedAt   string `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string `json:"updatedAt" dc:"更新时间"`
}

// FirewallCreateRuleReq 创建防火墙规则请求
type FirewallCreateRuleReq struct {
	NodeID      int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Rule        string `json:"rule" v:"required#规则内容不能为空" dc:"规则内容"`
	Action      string `json:"action" v:"required#动作不能为空" dc:"动作(ACCEPT/DROP/REJECT)"`
	Direction   string `json:"direction" v:"required#方向不能为空" dc:"方向(IN/OUT)"`
	Protocol    string `json:"protocol" dc:"协议"`
	Source      string `json:"source" dc:"源地址"`
	Destination string `json:"destination" dc:"目标地址"`
	Port        string `json:"port" dc:"端口"`
	Enabled     bool   `json:"enabled" dc:"是否启用"`
}

// FirewallCreateRuleRes 创建防火墙规则响应
type FirewallCreateRuleRes struct {
	ID uint64 `json:"id" dc:"规则ID"`
}

// FirewallEditRuleReq 编辑防火墙规则请求
type FirewallEditRuleReq struct {
	ID          uint64 `json:"id" v:"required#规则ID不能为空" dc:"规则ID"`
	Rule        string `json:"rule" dc:"规则内容"`
	Action      string `json:"action" dc:"动作(ACCEPT/DROP/REJECT)"`
	Direction   string `json:"direction" dc:"方向(IN/OUT)"`
	Protocol    string `json:"protocol" dc:"协议"`
	Source      string `json:"source" dc:"源地址"`
	Destination string `json:"destination" dc:"目标地址"`
	Port        string `json:"port" dc:"端口"`
	Enabled     bool   `json:"enabled" dc:"是否启用"`
}

// FirewallEditRuleRes 编辑防火墙规则响应
type FirewallEditRuleRes struct{}

// FirewallDeleteRuleReq 删除防火墙规则请求
type FirewallDeleteRuleReq struct {
	ID uint64 `json:"id" v:"required#规则ID不能为空" dc:"规则ID"`
}

// FirewallDeleteRuleRes 删除防火墙规则响应
type FirewallDeleteRuleRes struct{}
