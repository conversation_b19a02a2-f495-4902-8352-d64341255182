// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// 存储和备份相关输入输出模型

// 存储管理
type StorageListInp struct {
	form.PageReq
	NodeID  int    `json:"nodeId"`
	Type    string `json:"type"`
	Keyword string `json:"keyword"`
}

type StorageModel struct {
	ID      string   `json:"id"`
	Storage string   `json:"storage"`
	Type    string   `json:"type"`
	Content []string `json:"content"`
	Nodes   string   `json:"nodes"`
	Enabled int      `json:"enabled"`
	Shared  int      `json:"shared"`
	Path    string   `json:"path,omitempty"`
	Server  string   `json:"server,omitempty"`
	Export  string   `json:"export,omitempty"`
	Portal  string   `json:"portal,omitempty"`
	Target  string   `json:"target,omitempty"`
	Used    int64    `json:"used,omitempty"`
	Avail   int64    `json:"avail,omitempty"`
	Total   int64    `json:"total,omitempty"`
}

type StorageListModel struct {
	List  []*StorageModel `json:"list"`
	Total int             `json:"total"`
	form.PageRes
}

type StorageViewInp struct {
	ID string `json:"id" v:"required"`
}

type StorageViewModel struct {
	*StorageModel
}

type StorageCreateInp struct {
	Storage   string `json:"storage" v:"required"`
	Type      string `json:"type" v:"required"`
	Content   string `json:"content" v:"required"`
	Nodes     string `json:"nodes"`
	Shared    bool   `json:"shared"`
	Path      string `json:"path"`
	Server    string `json:"server"`
	Export    string `json:"export"`
	Portal    string `json:"portal"`
	Target    string `json:"target"`
	Username  string `json:"username"`
	Password  string `json:"password"`
	Pool      string `json:"pool"`
	Blocksize string `json:"blocksize"`
	KRBD      bool   `json:"krbd"`
}

type StorageCreateModel struct {
	ID string `json:"id"`
}

type StorageEditInp struct {
	ID      string `json:"id" v:"required"`
	Content string `json:"content"`
	Nodes   string `json:"nodes"`
	Shared  bool   `json:"shared"`
	Enabled bool   `json:"enabled"`
}

type StorageEditModel struct {
	ID string `json:"id"`
}

type StorageDeleteInp struct {
	ID string `json:"id" v:"required"`
}

type StorageDeleteModel struct {
	ID string `json:"id"`
}

type StorageContentInp struct {
	NodeID  int    `json:"nodeId" v:"required"`
	Storage string `json:"storage" v:"required"`
	Content string `json:"content"`
}

// StorageGetContentInp 获取存储内容输入
type StorageGetContentInp struct {
	NodeID  int    `json:"nodeId" v:"required"`
	Storage string `json:"storage" v:"required"`
	Path    string `json:"path"`
}

// StorageGetContentOut 获取存储内容输出
type StorageGetContentOut struct {
	Contents []*StorageContentModel `json:"contents"`
}

// StorageContentModel 存储内容模型
type StorageContentModel struct {
	VolID    string `json:"volId"`
	Name     string `json:"name"`
	Size     int64  `json:"size"`
	Format   string `json:"format"`
	Type     string `json:"type"`
	Modified string `json:"modified"`
}

type StorageContent struct {
	VolID   string `json:"volid"`
	Content string `json:"content"`
	Format  string `json:"format"`
	Size    int64  `json:"size"`
	Used    int64  `json:"used,omitempty"`
	VMID    int    `json:"vmid,omitempty"`
	CTime   int    `json:"ctime,omitempty"`
	Notes   string `json:"notes,omitempty"`
}

type StorageContentListModel struct {
	List []*StorageContent `json:"list"`
}

// 备份管理
type BackupListInp struct {
	form.PageReq
	NodeID  int    `json:"nodeId"`
	Storage string `json:"storage"`
	VMID    int    `json:"vmid"`
}

type BackupModel struct {
	VolID        string `json:"volid"`
	Content      string `json:"content"`
	Format       string `json:"format"`
	Size         int64  `json:"size"`
	CTime        int    `json:"ctime"`
	VMID         int    `json:"vmid,omitempty"`
	Notes        string `json:"notes,omitempty"`
	Verification string `json:"verification,omitempty"`
}

type BackupListModel struct {
	List  []*BackupModel `json:"list"`
	Total int            `json:"total"`
	form.PageRes
}

type BackupCreateInp struct {
	VMID     int    `json:"vmid" v:"required"`
	Storage  string `json:"storage" v:"required"`
	Mode     string `json:"mode"`     // snapshot, suspend, stop
	Compress string `json:"compress"` // none, lzo, gzip, zstd
	Notes    string `json:"notes"`
}

type BackupCreateModel struct {
	TaskID string `json:"taskId"`
}

type BackupDeleteInp struct {
	ID    int    `json:"id" v:"required"`
	VolID string `json:"volid" v:"required"`
}

type BackupDeleteModel struct {
	ID int `json:"id"`
}

type BackupRestoreInp struct {
	VolID   string `json:"volid" v:"required"`
	VMID    int    `json:"vmid"`
	Storage string `json:"storage"`
	Pool    string `json:"pool"`
}

type BackupRestoreModel struct {
	TaskID string `json:"taskId"`
}

type StorageViewOut struct {
	*StorageModel
}

type StorageCreateOut struct {
	*StorageCreateModel
}

type StorageSyncOut struct {
	Total     int `json:"total"`
	Synced    int `json:"synced"`
	Updated   int `json:"updated"`
	CreatedAt int `json:"created_at"`
}

type StorageBackupListOut struct {
	*BackupListModel
}

type StorageCreateBackupOut struct {
	*BackupCreateModel
}

type StorageRestoreBackupOut struct {
	*BackupRestoreModel
}

type NetworkDeleteModel struct {
	ID int `json:"id"`
}
