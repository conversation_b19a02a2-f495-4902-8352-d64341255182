// Package service
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package service

import (
	"context"
	"hotgo/addons/pve/model/input"
)

// IPveNodes PVE节点管理接口
type IPveNodes interface {
	// Create 创建节点
	Create(ctx context.Context, in *input.NodeCreateInp) (out *input.NodeCreateOut, err error)
	// Edit 编辑节点
	Edit(ctx context.Context, in *input.NodeEditInp) (err error)
	// Delete 删除节点
	Delete(ctx context.Context, in *input.NodeDeleteInp) (err error)
	// View 查看节点详情
	View(ctx context.Context, in *input.NodeViewInp) (out *input.NodeViewOut, err error)
	// List 获取节点列表
	List(ctx context.Context, in *input.NodeListInp) (out *input.NodeListOut, err error)
	// TestConnection 测试节点连接
	TestConnection(ctx context.Context, in *input.NodeTestInp) (out *input.NodeTestOut, err error)
	// SyncStatus 同步节点状态
	SyncStatus(ctx context.Context, in *input.NodeSyncInp) (out *input.NodeSyncOut, err error)
	// GetMonitorData 获取节点监控数据
	GetMonitorData(ctx context.Context, in *input.NodeMonitorInp) (out *input.NodeMonitorOut, err error)
	// GetServices 获取节点服务列表
	GetServices(ctx context.Context, in *input.NodeServicesInp) (out *input.NodeServicesOut, err error)
	// ControlService 控制节点服务
	ControlService(ctx context.Context, in *input.NodeServiceControlInp) (out *input.NodeServiceControlOut, err error)
	// RebootNode 重启节点
	RebootNode(ctx context.Context, in *input.NodeRebootInp) (out *input.NodeRebootOut, err error)
	// ShutdownNode 关闭节点
	ShutdownNode(ctx context.Context, in *input.NodeShutdownInp) (out *input.NodeShutdownOut, err error)
}

// IPveInstances PVE实例管理接口
type IPveInstances interface {
	// Create 创建实例
	Create(ctx context.Context, in *input.InstanceCreateInp) (out *input.InstanceCreateOut, err error)
	// Edit 编辑实例
	Edit(ctx context.Context, in *input.InstanceEditInp) (err error)
	// Delete 删除实例
	Delete(ctx context.Context, in *input.InstanceDeleteInp) (out *input.InstanceDeleteOut, err error)
	// View 查看实例详情
	View(ctx context.Context, in *input.InstanceViewInp) (out *input.InstanceViewOut, err error)
	// List 获取实例列表
	List(ctx context.Context, in *input.InstanceListInp) (out *input.InstanceListOut, err error)
	// Action 执行实例操作
	Action(ctx context.Context, in *input.InstanceActionInp) (out *input.InstanceActionOut, err error)
	// Renew 续费实例
	Renew(ctx context.Context, in *input.InstanceRenewInp) (out *input.InstanceRenewOut, err error)
	// GetConsole 获取控制台访问信息
	GetConsole(ctx context.Context, in *input.InstanceConsoleInp) (out *input.InstanceConsoleOut, err error)
	// CreateSnapshot 创建快照
	CreateSnapshot(ctx context.Context, in *input.InstanceSnapshotInp) (out *input.InstanceSnapshotOut, err error)
	// GetSnapshots 获取快照列表
	GetSnapshots(ctx context.Context, in *input.InstanceSnapshotListInp) (out *input.InstanceSnapshotListOut, err error)
	// GetMonitorData 获取实例监控数据
	GetMonitorData(ctx context.Context, in *input.InstanceMonitorInp) (out *input.InstanceMonitorOut, err error)
	// GetTaskStatus 获取任务状态
	GetTaskStatus(ctx context.Context, in *input.InstanceTaskInp) (out *input.InstanceTaskOut, err error)
	// Start 启动实例
	Start(ctx context.Context, in *input.InstanceOperationInp) (out *input.InstanceOperationOut, err error)
	// Stop 停止实例
	Stop(ctx context.Context, in *input.InstanceOperationInp) (out *input.InstanceOperationOut, err error)
	// Restart 重启实例
	Restart(ctx context.Context, in *input.InstanceOperationInp) (out *input.InstanceOperationOut, err error)
}

// IPveTemplates PVE模板管理接口
type IPveTemplates interface {
	// Create 创建模板
	Create(ctx context.Context, in *input.TemplateCreateInp) (out *input.TemplateCreateOut, err error)
	// Edit 编辑模板
	Edit(ctx context.Context, in *input.TemplateEditInp) (err error)
	// Delete 删除模板
	Delete(ctx context.Context, in *input.TemplateDeleteInp) (err error)
	// View 查看模板详情
	View(ctx context.Context, in *input.TemplateViewInp) (out *input.TemplateViewOut, err error)
	// List 获取模板列表
	List(ctx context.Context, in *input.TemplateListInp) (out *input.TemplateListOut, err error)
	// Sync 同步PVE模板
	Sync(ctx context.Context, in *input.TemplateSyncInp) (out *input.TemplateSyncOut, err error)
	// Import 导入模板
	Import(ctx context.Context, in *input.TemplateImportInp) (out *input.TemplateImportOut, err error)
	// GetSelectList 获取模板选择列表
	GetSelectList(ctx context.Context, in *input.TemplateSelectInp) (out *input.TemplateSelectOut, err error)
}

// IPveStorage PVE存储管理接口
type IPveStorage interface {
	// Create 创建存储
	Create(ctx context.Context, in *input.StorageCreateInp) (out *input.StorageCreateOut, err error)
	// Edit 编辑存储
	Edit(ctx context.Context, in *input.StorageEditInp) (err error)
	// Delete 删除存储
	Delete(ctx context.Context, in *input.StorageDeleteInp) (err error)
	// View 查看存储详情
	View(ctx context.Context, in *input.StorageViewInp) (out *input.StorageViewOut, err error)
	// List 获取存储列表
	List(ctx context.Context, in *input.StorageListInp) (out *input.StorageListModel, err error)
	// GetContent 获取存储内容
	GetContent(ctx context.Context, in *input.StorageGetContentInp) (out *input.StorageGetContentOut, err error)
	// Sync 同步存储信息
	Sync(ctx context.Context, in *input.StorageListInp) (out *input.StorageSyncOut, err error)
	// GetBackupList 获取备份列表
	GetBackupList(ctx context.Context, in *input.BackupListInp) (out *input.StorageBackupListOut, err error)
	// CreateBackup 创建备份
	CreateBackup(ctx context.Context, in *input.BackupCreateInp) (out *input.StorageCreateBackupOut, err error)
	// RestoreBackup 恢复备份
	RestoreBackup(ctx context.Context, in *input.BackupRestoreInp) (out *input.StorageRestoreBackupOut, err error)
	// DeleteBackup 删除备份
	DeleteBackup(ctx context.Context, in *input.BackupDeleteInp) (err error)
}

// IPveCore PVE核心服务接口
type IPveCore interface {
	// GetPVEClient 获取PVE客户端
	GetPVEClient(ctx context.Context, nodeID uint64) (client interface{}, err error)
	// ValidateConfig 验证PVE配置
	ValidateConfig(config interface{}) error
	// SyncAllNodes 同步所有节点状态
	SyncAllNodes(ctx context.Context) error
	// SyncAllInstances 同步所有实例状态
	SyncAllInstances(ctx context.Context) error
}

// IFirewall PVE防火墙管理接口
type IFirewall interface {
	// GetRules 获取防火墙规则列表
	GetRules(ctx context.Context, in *input.FirewallRulesInp) (out *input.FirewallRulesOut, err error)
	// CreateRule 创建防火墙规则
	CreateRule(ctx context.Context, in *input.FirewallCreateRuleInp) (out *input.FirewallCreateRuleModel, err error)
	// EditRule 编辑防火墙规则
	EditRule(ctx context.Context, in *input.FirewallEditRuleInp) (out *input.FirewallEditRuleModel, err error)
	// DeleteRule 删除防火墙规则
	DeleteRule(ctx context.Context, in *input.FirewallDeleteRuleInp) (err error)
}

// IHA PVE高可用管理接口
type IHA interface {
	// GetResources 获取HA资源列表
	GetResources(ctx context.Context, in *input.HAResourcesInp) (out *input.HAResourcesOut, err error)
	// GetGroups 获取HA组列表
	GetGroups(ctx context.Context, in *input.HAGroupsInp) (out *input.HAGroupsOut, err error)
	// CreateGroup 创建HA组
	CreateGroup(ctx context.Context, in *input.HACreateGroupInp) (out *input.HAGroupCreateOut, err error)
	// EditGroup 编辑HA组
	EditGroup(ctx context.Context, in *input.HAEditGroupInp) (out *input.HAEditGroupModel, err error)
	// DeleteGroup 删除HA组
	DeleteGroup(ctx context.Context, in *input.HADeleteGroupInp) (err error)
}

var (
	// 节点管理服务
	localPveNodes IPveNodes
	// 实例管理服务
	localPveInstances IPveInstances
	// 模板管理服务
	localPveTemplates IPveTemplates
	// 存储管理服务
	localPveStorage IPveStorage
	// 核心服务
	localPveCore IPveCore
	// 防火墙管理服务
	localFirewall IFirewall
	// 高可用管理服务
	localHA IHA
	// 网络管理服务
	localNetwork INetwork
)

// PveNodes 获取节点管理服务
func PveNodes() IPveNodes {
	if localPveNodes == nil {
		panic("implement not found for interface IPveNodes, forgot register?")
	}
	return localPveNodes
}

// RegisterPveNodes 注册节点管理服务
func RegisterPveNodes(i IPveNodes) {
	localPveNodes = i
}

// PveInstances 获取实例管理服务
func PveInstances() IPveInstances {
	if localPveInstances == nil {
		panic("implement not found for interface IPveInstances, forgot register?")
	}
	return localPveInstances
}

// RegisterPveInstances 注册实例管理服务
func RegisterPveInstances(i IPveInstances) {
	localPveInstances = i
}

// PveTemplates 获取模板管理服务
func PveTemplates() IPveTemplates {
	if localPveTemplates == nil {
		panic("implement not found for interface IPveTemplates, forgot register?")
	}
	return localPveTemplates
}

// RegisterPveTemplates 注册模板管理服务
func RegisterPveTemplates(i IPveTemplates) {
	localPveTemplates = i
}

// PveStorage 获取存储管理服务
func PveStorage() IPveStorage {
	if localPveStorage == nil {
		panic("implement not found for interface IPveStorage, forgot register?")
	}
	return localPveStorage
}

// RegisterPveStorage 注册存储管理服务
func RegisterPveStorage(i IPveStorage) {
	localPveStorage = i
}

// PveCore 获取核心服务
func PveCore() IPveCore {
	if localPveCore == nil {
		panic("implement not found for interface IPveCore, forgot register?")
	}
	return localPveCore
}

// RegisterPveCore 注册核心服务
func RegisterPveCore(i IPveCore) {
	localPveCore = i
}

// Firewall 获取防火墙管理服务
func Firewall() IFirewall {
	if localFirewall == nil {
		panic("implement not found for interface IFirewall, forgot register?")
	}
	return localFirewall
}

// RegisterFirewall 注册防火墙管理服务
func RegisterFirewall(i IFirewall) {
	localFirewall = i
}

// HA 获取高可用管理服务
func HA() IHA {
	if localHA == nil {
		panic("implement not found for interface IHA, forgot register?")
	}
	return localHA
}

// RegisterHA 注册高可用管理服务
func RegisterHA(i IHA) {
	localHA = i
}

// INetwork PVE网络管理接口
type INetwork interface {
	// Create 创建网络
	Create(ctx context.Context, in *input.NetworkCreateInp) (out *input.NetworkCreateOut, err error)
	// Edit 编辑网络
	Edit(ctx context.Context, in *input.NetworkEditInp) (err error)
	// Delete 删除网络
	Delete(ctx context.Context, in *input.NetworkDeleteInp) (err error)
	// View 查看网络详情
	View(ctx context.Context, in *input.NetworkViewInp) (out *input.NetworkViewOut, err error)
	// List 获取网络列表
	List(ctx context.Context, in *input.NetworkListInp) (out *input.NetworkListModel, err error)
}

// PveNetwork 获取网络管理服务
func PveNetwork() INetwork {
	if localNetwork == nil {
		panic("implement not found for interface INetwork, forgot register?")
	}
	return localNetwork
}

// RegisterPveNetwork 注册网络管理服务
func RegisterPveNetwork(i INetwork) {
	localNetwork = i
}
