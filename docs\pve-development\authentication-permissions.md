# PVE API 认证和权限系统

> 基于官方 API 文档：https://pve.proxmox.com/pve-docs/api-viewer/

## 概述

Proxmox VE 提供了完整的认证和权限系统，支持多种认证方式和细粒度的权限控制。本文档详细介绍了如何在应用程序中正确实现 PVE 的认证和权限管理。

## 🔐 认证机制详解

### 1. API Token 认证（推荐）

API Token 是 PVE 提供的最安全、最便捷的自动化认证方式，特别适合应用程序集成：

**特点：**
- 无需存储用户密码
- 可以设置特定的权限范围
- 支持过期时间管理
- 便于权限审计和管理

**创建 API Token：**

```bash
# 通过 Web UI 创建：
# 1. 登录 PVE Web 界面
# 2. 数据中心 -> 权限 -> API Token
# 3. 添加新的 API Token
# 4. 设置权限和过期时间

# 通过命令行创建：
pveum token add <userid> <tokenid> --expire <timestamp> --comment "Application Token"
```

**使用示例：**

```go
// 使用 API Token 的客户端配置
config := &Config{
    Host:        "pve.example.com",
    Port:        8006,
    TokenID:     "root@pam!myapp",           // 用户@域!Token名称
    TokenSecret: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx", // Token密钥
    Insecure:    true,  // 在测试环境中忽略SSL证书
    Timeout:     30 * time.Second,
    Retries:     3,
}

client, err := NewPVEClient(config)
if err != nil {
    log.Fatalf("创建PVE客户端失败: %v", err)
}

// API Token 在请求头中的格式
// Authorization: PVEAPIToken=root@pam!myapp=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
```

### 2. 票据认证（Ticket Authentication）

基于用户名/密码的传统认证方式，通过获取临时票据来维持会话：

**认证流程：**

```go
type TicketAuthRequest struct {
    Username string `json:"username"`  // 用户名@域，如 root@pam
    Password string `json:"password"`  // 用户密码
}

type TicketAuthResponse struct {
    Data struct {
        Username            string `json:"username"`
        Ticket              string `json:"ticket"`                // 认证票据
        CSRFPreventionToken string `json:"CSRFPreventionToken"`   // CSRF防护令牌
        Cap struct {
            Access  map[string]interface{} `json:"access"`   // 用户权限
            Nodes   []string              `json:"nodes"`    // 可访问的节点
        } `json:"cap"`
    } `json:"data"`
}

// 票据认证实现
func (c *PVEClient) authenticateTicket(ctx context.Context) error {
    authData := map[string]string{
        "username": c.username,
        "password": c.password,
    }
    
    var response TicketAuthResponse
    err := c.request(ctx, "POST", "/access/ticket", authData, &response)
    if err != nil {
        return gerror.Wrap(err, "获取认证票据失败")
    }
    
    // 保存认证信息
    c.ticket = response.Data.Ticket
    c.csrfToken = response.Data.CSRFPreventionToken
    
    // 票据有效期为2小时，需要定时刷新
    c.startTicketRefresh(ctx)
    
    return nil
}

// 票据自动刷新机制
func (c *PVEClient) startTicketRefresh(ctx context.Context) {
    go func() {
        ticker := time.NewTicker(90 * time.Minute) // 每90分钟刷新一次
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                if err := c.authenticateTicket(ctx); err != nil {
                    g.Log().Errorf(ctx, "票据刷新失败: %v", err)
                }
            case <-ctx.Done():
                return
            }
        }
    }()
}
```

### 3. Cookie 认证

主要用于 Web 界面，应用程序集成不推荐使用：

```go
// Cookie认证请求配置
func (c *PVEClient) addCookieAuth(req *http.Request) {
    if c.ticket != "" {
        cookie := &http.Cookie{
            Name:  "PVEAuthCookie",
            Value: c.ticket,
        }
        req.AddCookie(cookie)
        
        // 添加CSRF Token到请求头
        if c.csrfToken != "" {
            req.Header.Set("CSRFPreventionToken", c.csrfToken)
        }
    }
}
```

## 🔒 权限系统详解

PVE 采用基于路径的权限控制系统，支持细粒度的权限管理：

### 权限路径结构

```text
/                           # 根路径，超级管理员权限
├── /nodes                  # 节点管理权限
│   ├── /nodes/{node}      # 特定节点权限
│   └── /nodes/{node}/qemu # 特定节点的虚拟机权限
├── /vms                    # 全局虚拟机权限
│   └── /vms/{vmid}        # 特定虚拟机权限
├── /pool                   # 资源池权限
│   └── /pool/{poolid}     # 特定资源池权限
├── /storage               # 存储权限
│   └── /storage/{storage} # 特定存储权限
├── /datacenter            # 数据中心配置权限
│   ├── /datacenter/firewall # 防火墙配置
│   └── /datacenter/storage  # 存储配置
└── /access                # 用户和权限管理
    ├── /access/users      # 用户管理
    ├── /access/groups     # 组管理
    └── /access/roles      # 角色管理
```

### 内置角色和权限

```go
// PVE内置角色定义
const (
    // 系统管理员角色
    RoleAdministrator = "Administrator"  // 完全管理权限
    RoleAuditor      = "Auditor"        // 只读审计权限
    
    // 用户管理角色
    RoleUserAdmin    = "UserAdmin"      // 用户管理权限
    
    // 虚拟机管理角色
    RolePVEVMAdmin   = "PVEVMAdmin"     // 虚拟机完全管理
    RolePVEVMUser    = "PVEVMUser"      // 虚拟机基本操作
    
    // 数据存储角色
    RolePVEDatastoreAdmin = "PVEDatastoreAdmin" // 存储管理
    RolePVEDatastoreUser  = "PVEDatastoreUser"  // 存储使用
    
    // 系统操作角色
    RolePVESysAdmin      = "PVESysAdmin"      // 系统管理
    RolePVETemplateUser  = "PVETemplateUser"  // 模板使用
)

// 权限定义
const (
    // 系统权限
    PermSysAudit     = "Sys.Audit"      // 系统审计
    PermSysConsole   = "Sys.Console"    // 控制台访问  
    PermSysModify    = "Sys.Modify"     // 系统修改
    PermSysPowerMgmt = "Sys.PowerMgmt"  // 电源管理
    PermSysSyslog    = "Sys.Syslog"     // 系统日志
    
    // 数据存储权限
    PermDatastoreAllocate     = "Datastore.Allocate"      // 存储分配
    PermDatastoreAllocateSpace = "Datastore.AllocateSpace" // 存储空间分配
    PermDatastoreAudit        = "Datastore.Audit"         // 存储审计
    
    // 用户管理权限
    PermUserModify = "User.Modify"      // 用户修改
    
    // 虚拟机权限
    PermVMAudit     = "VM.Audit"        // 虚拟机审计
    PermVMBackup    = "VM.Backup"       // 虚拟机备份
    PermVMClone     = "VM.Clone"        // 虚拟机克隆
    PermVMConfig    = "VM.Config"       // 虚拟机配置
    PermVMConsole   = "VM.Console"      // 虚拟机控制台
    PermVMMigrate   = "VM.Migrate"      // 虚拟机迁移
    PermVMMonitor   = "VM.Monitor"      // 虚拟机监控
    PermVMPowerMgmt = "VM.PowerMgmt"    // 虚拟机电源管理
    PermVMSnapshot  = "VM.Snapshot"     // 虚拟机快照
)
```

### 权限检查实现

```go
// 权限验证客户端
type PermissionChecker struct {
    client   *PVEClient
    userPerms map[string][]string  // 用户权限缓存
}

// CheckPermission 检查用户权限
func (p *PermissionChecker) CheckPermission(ctx context.Context, user, path, permission string) (bool, error) {
    // 1. 从缓存获取用户权限
    if perms, ok := p.userPerms[user]; ok {
        return p.hasPermission(perms, path, permission), nil
    }
    
    // 2. 从PVE API获取用户权限
    perms, err := p.getUserPermissions(ctx, user)
    if err != nil {
        return false, err
    }
    
    // 3. 缓存权限信息
    p.userPerms[user] = perms
    
    return p.hasPermission(perms, path, permission), nil
}

// getUserPermissions 获取用户权限
func (p *PermissionChecker) getUserPermissions(ctx context.Context, user string) ([]string, error) {
    path := fmt.Sprintf("/access/permissions?userid=%s", user)
    var response APIResponse[map[string]interface{}]
    
    err := p.client.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    // 解析权限数据
    var permissions []string
    for path, perms := range response.Data {
        if permMap, ok := perms.(map[string]interface{}); ok {
            for perm := range permMap {
                permissions = append(permissions, fmt.Sprintf("%s:%s", path, perm))
            }
        }
    }
    
    return permissions, nil
}

// hasPermission 检查是否具有特定权限
func (p *PermissionChecker) hasPermission(userPerms []string, path, permission string) bool {
    // 检查完全匹配
    exactPerm := fmt.Sprintf("%s:%s", path, permission)
    for _, perm := range userPerms {
        if perm == exactPerm {
            return true
        }
    }
    
    // 检查父路径权限继承
    for _, perm := range userPerms {
        if p.isParentPermission(perm, path, permission) {
            return true
        }
    }
    
    return false
}
```

### 自定义权限角色

```go
// CustomRole 自定义角色管理
type CustomRole struct {
    ID          string   `json:"roleid"`
    Privileges  []string `json:"privs"`
    Comment     string   `json:"comment,omitempty"`
}

// CreateRole 创建自定义角色
func (c *PVEClient) CreateRole(ctx context.Context, role *CustomRole) error {
    data := map[string]interface{}{
        "roleid": role.ID,
        "privs":  strings.Join(role.Privileges, ","),
    }
    if role.Comment != "" {
        data["comment"] = role.Comment
    }
    
    return c.request(ctx, "POST", "/access/roles", data, nil)
}

// AssignRole 分配角色给用户
func (c *PVEClient) AssignRole(ctx context.Context, user, path, role string) error {
    data := map[string]interface{}{
        "users": user,
        "roles": role,
        "path":  path,
    }
    
    return c.request(ctx, "PUT", "/access/acl", data, nil)
}

// 应用级别的权限管理示例
func setupApplicationPermissions(ctx context.Context, client *PVEClient) error {
    // 1. 创建应用特定的角色
    appVMRole := &CustomRole{
        ID: "AppVMManager", 
        Privileges: []string{
            PermVMAudit,
            PermVMConfig,
            PermVMConsole,
            PermVMPowerMgmt,
            PermVMSnapshot,
        },
        Comment: "应用程序虚拟机管理角色",
    }
    
    if err := client.CreateRole(ctx, appVMRole); err != nil {
        return err
    }
    
    // 2. 创建应用服务账号
    appUser := map[string]interface{}{
        "userid":   "app@pve",
        "password": "secure_password",
        "comment":  "应用程序服务账号",
    }
    
    if err := client.request(ctx, "POST", "/access/users", appUser, nil); err != nil {
        return err
    }
    
    // 3. 为服务账号分配权限
    if err := client.AssignRole(ctx, "app@pve", "/vms", "AppVMManager"); err != nil {
        return err
    }
    
    return nil
}
```

## 📋 权限最佳实践

### 1. 最小权限原则

```go
// 根据功能需求分配最小必要权限
type ApplicationPermissions struct {
    // VM监控应用只需要审计权限
    MonitoringApp []string = []string{PermVMAudit, PermSysAudit}
    
    // VM管理应用需要操作权限  
    ManagementApp []string = []string{
        PermVMAudit, PermVMConfig, PermVMPowerMgmt, PermVMConsole,
    }
    
    // 备份应用需要备份和快照权限
    BackupApp []string = []string{
        PermVMAudit, PermVMBackup, PermVMSnapshot,
    }
}
```

### 2. 权限分级管理

```go
// 多级权限管理结构
type PermissionHierarchy struct {
    // 系统级别 - 完全管理权限
    SystemAdmin map[string][]string
    
    // 租户级别 - 租户内资源管理
    TenantAdmin map[string][]string
    
    // 项目级别 - 项目内资源操作
    ProjectUser map[string][]string
    
    // 资源级别 - 特定资源权限
    ResourceUser map[string][]string
}

func (p *PermissionHierarchy) InitializeHierarchy() {
    p.SystemAdmin = map[string][]string{
        "/": {"Administrator"},
    }
    
    p.TenantAdmin = map[string][]string{
        "/pool/{tenant_pool}": {"PVEVMAdmin"},
        "/storage/{tenant_storage}": {"PVEDatastoreUser"},
    }
    
    p.ProjectUser = map[string][]string{
        "/vms/{project_vm_range}": {"PVEVMUser"},
    }
    
    p.ResourceUser = map[string][]string{
        "/vms/{specific_vmid}": {"VM.Console", "VM.PowerMgmt"},
    }
}
```

### 3. 安全审计

```go
// 权限审计日志
type PermissionAuditLog struct {
    Timestamp   time.Time `json:"timestamp"`
    UserID      string    `json:"userId"`
    Action      string    `json:"action"`
    Resource    string    `json:"resource"`
    Permission  string    `json:"permission"`
    Result      bool      `json:"result"`
    IPAddress   string    `json:"ipAddress"`
}

// LogPermissionCheck 记录权限检查
func (p *PermissionChecker) LogPermissionCheck(ctx context.Context, log *PermissionAuditLog) {
    // 记录到审计日志系统
    g.Log().Info(ctx, "权限检查", g.Map{
        "user":       log.UserID,
        "action":     log.Action,
        "resource":   log.Resource,
        "permission": log.Permission,
        "result":     log.Result,
        "ip":         log.IPAddress,
    })
}
```

## 总结

通过以上详细的 PVE API 认证和权限系统说明，开发者可以：

1. **选择适合的认证方式**：根据应用场景选择 API Token 或票据认证
2. **实现细粒度权限控制**：基于路径和角色的权限管理
3. **遵循安全最佳实践**：最小权限原则和权限分级管理
4. **建立完整的审计机制**：记录所有权限相关操作

这套权限系统确保了 PVE 资源的安全访问和精确的权限控制。