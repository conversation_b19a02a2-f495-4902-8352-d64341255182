// Package core
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package core

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/addons/pve/service"
)

type sPveCore struct{}

func init() {
	service.RegisterPveCore(NewPveCore())
}

func NewPveCore() service.IPveCore {
	return &sPveCore{}
}

// GetPVEClient 获取PVE客户端
func (s *sPveCore) GetPVEClient(ctx context.Context, nodeID uint64) (client interface{}, err error) {
	// 获取节点信息
	var nodeData g.Map
	err = g.DB().Model("hg_pve_nodes").Where("id = ? AND status = ?", nodeID, 1).Scan(&nodeData)
	if err != nil {
		return nil, err
	}
	if nodeData == nil {
		return nil, fmt.Errorf("节点不存在或状态异常")
	}

	// 创建 PVE客户端配置
	config := &pveclient.Config{
		Host:        gconv.String(nodeData["host"]),
		Port:        gconv.Int(nodeData["port"]),
		Username:    gconv.String(nodeData["username"]),
		Password:    gconv.String(nodeData["password"]),
		TokenID:     gconv.String(nodeData["token_id"]),
		TokenSecret: gconv.String(nodeData["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	return pveclient.NewClient(config)
}

// ValidateConfig 验证PVE配置
func (s *sPveCore) ValidateConfig(config interface{}) error {
	pveConfig, ok := config.(*pveclient.Config)
	if !ok {
		return fmt.Errorf("无效的PVE配置类型")
	}

	if pveConfig.Host == "" {
		return fmt.Errorf("PVE主机地址不能为空")
	}
	if pveConfig.Port <= 0 {
		return fmt.Errorf("PVE端口必须大于0")
	}
	if pveConfig.Username == "" && pveConfig.TokenID == "" {
		return fmt.Errorf("必须提供用户名或API Token")
	}

	return nil
}

// SyncAllNodes 同步所有节点状态
func (s *sPveCore) SyncAllNodes(ctx context.Context) error {
	// 获取所有启用的节点
	var nodes []g.Map
	err := g.DB().Model("hg_pve_nodes").Where("status = ?", 1).Scan(&nodes)
	if err != nil {
		return err
	}

	for _, nodeData := range nodes {
		nodeID := gconv.Uint64(nodeData["id"])
		
		// 创建PVE客户端
		client, err := s.GetPVEClient(ctx, nodeID)
		if err != nil {
			g.Log().Warningf(ctx, "创建节点%d的PVE客户端失败: %v", nodeID, err)
			continue
		}

		pveClient, ok := client.(*pveclient.Client)
		if !ok {
			g.Log().Warningf(ctx, "节点%d的PVE客户端类型转换失败", nodeID)
			continue
		}

		// 获取节点状态
		pveNodes, err := pveClient.GetNodes(ctx)
		if err != nil {
			g.Log().Warningf(ctx, "获取节点%d状态失败: %v", nodeID, err)
			// 更新状态为异常
			g.DB().Model("hg_pve_nodes").Data(g.Map{
				"status":     2,
				"updated_at": time.Now(),
			}).Where("id", nodeID).Update()
			continue
		}

		if len(pveNodes) > 0 {
			node := pveNodes[0]
			// 更新节点状态信息
			g.DB().Model("hg_pve_nodes").Data(g.Map{
				"status":       1,
				"cpu_usage":    node.CPU * 100,
				"memory_usage": node.MemUsage * 100,
				"disk_usage":   node.DiskUsage * 100,
				"uptime":       node.Uptime,
				"updated_at":   time.Now(),
			}).Where("id", nodeID).Update()
		}
	}

	return nil
}

// SyncAllInstances 同步所有实例状态
func (s *sPveCore) SyncAllInstances(ctx context.Context) error {
	// 获取所有实例
	var instances []g.Map
	err := g.DB().Model("hg_pve_instances i").
		LeftJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.id, i.vmid, i.node_id, n.name as node_name, n.host, n.port, n.username, n.password, n.token_id, n.token_secret").
		Where("n.status = ?", 1).
		Scan(&instances)
	if err != nil {
		return err
	}

	for _, instanceData := range instances {
		instanceID := gconv.Uint64(instanceData["id"])
		nodeID := gconv.Uint64(instanceData["node_id"])
		
		// 创建PVE客户端
		client, err := s.GetPVEClient(ctx, nodeID)
		if err != nil {
			g.Log().Warningf(ctx, "同步实例%d时创建PVE客户端失败: %v", instanceID, err)
			continue
		}

		pveClient, ok := client.(*pveclient.Client)
		if !ok {
			continue
		}

		// 获取虚拟机状态
		vmStatus, err := pveClient.GetVMStatus(ctx, gconv.String(instanceData["node_name"]), gconv.Int(instanceData["vmid"]))
		if err != nil {
			g.Log().Warningf(ctx, "获取实例%d状态失败: %v", instanceID, err)
			continue
		}

		// 更新实例状态
		updateData := g.Map{
			"status":       vmStatus.Status,
			"cpu_usage":    vmStatus.CPU * 100,
			"memory_usage": float64(vmStatus.Memory) / float64(vmStatus.MaxMem) * 100,
			"updated_at":   time.Now(),
		}

		// 根据虚拟机运行状态更新QMP状态
		if vmStatus.RunningMachine != "" || vmStatus.RunningQemu != "" {
			updateData["qmp_status"] = "running"
		} else {
			updateData["qmp_status"] = vmStatus.Status
		}

		g.DB().Model("hg_pve_instances").Data(updateData).Where("id", instanceID).Update()
	}

	return nil
}