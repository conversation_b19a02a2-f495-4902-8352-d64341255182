// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

// VersionInfo 版本信息
type VersionInfo struct {
	Version string `json:"version"`
	Release string `json:"release"`
	RepoID  string `json:"repoid"`
}

// NodeInfo 节点信息
type NodeInfo struct {
	ID             string  `json:"id"`
	Node           string  `json:"node"`
	NodeType       string  `json:"type"`
	Status         string  `json:"status"`
	CPU            float64 `json:"cpu"`
	Disk           int64   `json:"disk"`
	DiskUsage      float64 `json:"diskusage"`
	MaxCPU         int     `json:"maxcpu"`
	MaxDisk        int64   `json:"maxdisk"`
	MaxMem         int64   `json:"maxmem"`
	Mem            int64   `json:"mem"`
	MemUsage       float64 `json:"memusage"`
	SSLFingerprint string  `json:"ssl_fingerprint"`
	Uptime         int64   `json:"uptime"`
	Level          string  `json:"level"`
}

// NodeStatus 节点状态
type NodeStatus struct {
	CPU            float64 `json:"cpu"`
	CPUInfo        CPUInfo `json:"cpuinfo"`
	CurrentKernel  Kernel  `json:"current-kernel"`
	Idle           int     `json:"idle"`
	KSM            KSM     `json:"ksm"`
	KVersion       string  `json:"kversion"`
	LoadAvg        []string `json:"loadavg"`
	Memory         Memory  `json:"memory"`
	PVEVersion     string  `json:"pveversion"`
	RootFS         RootFS  `json:"rootfs"`
	Swap           Swap    `json:"swap"`
	Uptime         int64   `json:"uptime"`
	Wait           float64 `json:"wait"`
}

// CPUInfo CPU信息
type CPUInfo struct {
	CPUs    int    `json:"cpus"`
	HVM     string `json:"hvm"`
	Model   string `json:"model"`
	Sockets int    `json:"sockets"`
	UserHz  int    `json:"user_hz"`
}

// Kernel 内核信息
type Kernel struct {
	Machine string `json:"machine"`
	Release string `json:"release"`
	SysName string `json:"sysname"`
	Version string `json:"version"`
}

// KSM Kernel Samepage Merging
type KSM struct {
	Shared int `json:"shared"`
}

// Memory 内存信息
type Memory struct {
	Free  int64 `json:"free"`
	Total int64 `json:"total"`
	Used  int64 `json:"used"`
}

// RootFS 根文件系统
type RootFS struct {
	Avail int64 `json:"avail"`
	Free  int64 `json:"free"`
	Total int64 `json:"total"`
	Used  int64 `json:"used"`
}

// Swap 交换空间
type Swap struct {
	Free  int64 `json:"free"`
	Total int64 `json:"total"`
	Used  int64 `json:"used"`
}

// VMInfo 虚拟机信息
type VMInfo struct {
	VMID      int     `json:"vmid"`
	Name      string  `json:"name"`
	Status    string  `json:"status"`
	CPU       float64 `json:"cpu"`
	CPUs      int     `json:"cpus"`
	Disk      int64   `json:"disk"`
	DiskRead  int64   `json:"diskread"`
	DiskWrite int64   `json:"diskwrite"`
	Lock      string  `json:"lock"`
	MaxDisk   int64   `json:"maxdisk"`
	MaxMem    int64   `json:"maxmem"`
	Mem       int64   `json:"mem"`
	NetIn     int64   `json:"netin"`
	NetOut    int64   `json:"netout"`
	PID       int     `json:"pid"`
	QMPStatus string  `json:"qmpstatus"`
	Running   int64   `json:"running_machine"`
	Running2  int64   `json:"running_qemu"`
	Template  int     `json:"template"`
	Uptime    int64   `json:"uptime"`
	Tags      string  `json:"tags"`
}

// VMStatus 虚拟机状态
type VMStatus struct {
	VMID       int         `json:"vmid"`
	Agent      int         `json:"agent"`
	Arch       string      `json:"arch"`
	Args       string      `json:"args"`
	Autostart  int         `json:"autostart"`
	Balloon    int64       `json:"balloon"`
	Boot       string      `json:"boot"`
	Bootdisk   string      `json:"bootdisk"`
	CPU        float64     `json:"cpu"`
	CPUs       int         `json:"cpus"`
	CPUType    string      `json:"cputype"`
	Description string     `json:"description"`
	Digest     string      `json:"digest"`
	FreezeFS   int         `json:"freeze"`
	HotPlug    string      `json:"hotplug"`
	IDE2       string      `json:"ide2"`
	KVM        int         `json:"kvm"`
	Machine    string      `json:"machine"`
	MaxMem     int64       `json:"maxmem"`
	Memory     int64       `json:"memory"`
	Name       string      `json:"name"`
	Net0       string      `json:"net0"`
	NumA       int         `json:"numa"`
	OSType     string      `json:"ostype"`
	Protection int         `json:"protection"`
	RunningMachine string  `json:"running-machine"`
	RunningQemu    string  `json:"running-qemu"`
	SCSI0      string      `json:"scsi0"`
	SMBios1    string      `json:"smbios1"`
	Sockets    int         `json:"sockets"`
	StartDate  string      `json:"startdate"`
	Startup    string      `json:"startup"`
	Status     string      `json:"status"`
	Tags       string      `json:"tags"`
	Template   int         `json:"template"`
	Uptime     int64       `json:"uptime"`
	VGA        string      `json:"vga"`
	VMGENID    string      `json:"vmgenid"`
}

// CreateVMParams 创建虚拟机参数
type CreateVMParams struct {
	VMID     int    `json:"vmid"`
	Name     string `json:"name"`
	Cores    int    `json:"cores"`
	Memory   int    `json:"memory"`
	Template string `json:"template,omitempty"`
	OSType   string `json:"ostype,omitempty"`
	Storage  string `json:"storage,omitempty"`
	Net0     string `json:"net0,omitempty"`
	SCSI0    string `json:"scsi0,omitempty"`
}

// TaskResponse 任务响应
type TaskResponse struct {
	Data string `json:"data"`
}

// TaskStatus 任务状态
type TaskStatus struct {
	ExitStatus string `json:"exitstatus"`
	ID         string `json:"id"`
	Node       string `json:"node"`
	PID        int    `json:"pid"`
	StartTime  int64  `json:"starttime"`
	Status     string `json:"status"`
	Type       string `json:"type"`
	UPid       string `json:"upid"`
	User       string `json:"user"`
}

// Template 模板信息
type Template struct {
	VMID        int    `json:"vmid"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Node        string `json:"node"`
	OSType      string `json:"ostype"`
	Template    int    `json:"template"`
}

// Snapshot 快照信息
type Snapshot struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	SnapTime    int64  `json:"snaptime,omitempty"`
	VMGENID     string `json:"vmgenid,omitempty"`
	Running     int    `json:"running,omitempty"`
	Parent      string `json:"parent,omitempty"`
}

// BackupInfo 备份信息
type BackupInfo struct {
	VolID    string `json:"volid"`
	Format   string `json:"format"`
	Size     int64  `json:"size"`
	CTime    int64  `json:"ctime"`
	VMID     int    `json:"vmid"`
	Content  string `json:"content"`
	Notes    string `json:"notes"`
	Protected int   `json:"protected"`
}

// StorageInfo 存储信息
type StorageInfo struct {
	Storage   string  `json:"storage"`
	Content   string  `json:"content"`
	Type      string  `json:"type"`
	Active    int     `json:"active"`
	Avail     int64   `json:"avail"`
	Enabled   int     `json:"enabled"`
	Shared    int     `json:"shared"`
	Total     int64   `json:"total"`
	Used      int64   `json:"used"`
	UsedFraction float64 `json:"used_fraction"`
}

// CloneVMParams 克隆虚拟机参数
type CloneVMParams struct {
	NewID       int    `json:"newid"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Target      string `json:"target,omitempty"`
	Storage     string `json:"storage,omitempty"`
	Full        bool   `json:"full,omitempty"`
}

// VMConfig 虚拟机配置
type VMConfig struct {
	VMID        int                    `json:"vmid"`
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	OSType      string                 `json:"ostype,omitempty"`
	Cores       int                    `json:"cores"`
	Sockets     int                    `json:"sockets"`
	Memory      int                    `json:"memory"`
	Boot        string                 `json:"boot,omitempty"`
	Bootdisk    string                 `json:"bootdisk,omitempty"`
	Agent       interface{}            `json:"agent,omitempty"`
	Options     map[string]interface{} `json:"-"` // 其他动态配置选项
}

// VNCProxy VNC代理信息
type VNCProxy struct {
	User     string `json:"user"`
	Ticket   string `json:"ticket"`
	Cert     string `json:"cert"`
	Port     int    `json:"port"`
	Upid     string `json:"upid"`
}

// ClusterInfo 集群信息
type ClusterInfo struct {
	Nodes    []*ClusterNode    `json:"nodes"`
	Resources []*ClusterResource `json:"resources"`
	Status   *ClusterStatus    `json:"status"`
	Config   *ClusterConfig    `json:"config"`
}

// ClusterNode 集群节点
type ClusterNode struct {
	ID       string  `json:"id"`
	Name     string  `json:"name"`
	Type     string  `json:"type"`
	Level    string  `json:"level"`
	Local    int     `json:"local"`
	NodeID   int     `json:"nodeid"`
	Online   int     `json:"online"`
	IP       string  `json:"ip"`
}

// ClusterResource 集群资源
type ClusterResource struct {
	ID          string  `json:"id"`
	Type        string  `json:"type"`
	Node        string  `json:"node"`
	Storage     string  `json:"storage"`
	Pool        string  `json:"pool"`
	VMID        int     `json:"vmid,omitempty"`
	Name        string  `json:"name,omitempty"`
	Status      string  `json:"status,omitempty"`
	Template    int     `json:"template,omitempty"`
	CPU         float64 `json:"cpu,omitempty"`
	CPUs        int     `json:"cpus,omitempty"`
	Disk        int64   `json:"disk,omitempty"`
	MaxDisk     int64   `json:"maxdisk,omitempty"`
	Mem         int64   `json:"mem,omitempty"`
	MaxMem      int64   `json:"maxmem,omitempty"`
	NetIn       int64   `json:"netin,omitempty"`
	NetOut      int64   `json:"netout,omitempty"`
	DiskRead    int64   `json:"diskread,omitempty"`
	DiskWrite   int64   `json:"diskwrite,omitempty"`
	Uptime      int64   `json:"uptime,omitempty"`
	Level       string  `json:"level,omitempty"`
}

// ClusterStatus 集群状态
type ClusterStatus struct {
	Name    string               `json:"name"`
	Version int                  `json:"version"`
	Quorate int                  `json:"quorate"`
	Nodes   map[string]*NodeInfo `json:"nodes"`
}

// ClusterConfig 集群配置
type ClusterConfig struct {
	TotemInterface map[string]interface{} `json:"totem"`
	NodeList       map[string]interface{} `json:"nodelist"`
	Quorum         map[string]interface{} `json:"quorum"`
}

// Pool 资源池
type Pool struct {
	PoolID  string               `json:"poolid"`
	Comment string               `json:"comment,omitempty"`
	Members []*PoolMember        `json:"members,omitempty"`
}

// PoolMember 资源池成员
type PoolMember struct {
	ID       string `json:"id"`
	Node     string `json:"node"`
	Type     string `json:"type"`
	VMID     int    `json:"vmid,omitempty"`
	Storage  string `json:"storage,omitempty"`
}

// User 用户信息
type User struct {
	UserID    string            `json:"userid"`
	Comment   string            `json:"comment,omitempty"`
	Email     string            `json:"email,omitempty"`
	Enable    int               `json:"enable"`
	Expire    int64             `json:"expire,omitempty"`
	Firstname string            `json:"firstname,omitempty"`
	Groups    []string          `json:"groups,omitempty"`
	Keys      string            `json:"keys,omitempty"`
	Lastname  string            `json:"lastname,omitempty"`
	Realm     string            `json:"realm"`
	Tokens    map[string]*Token `json:"tokens,omitempty"`
}

// Token API令牌
type Token struct {
	TokenID string `json:"tokenid"`
	Comment string `json:"comment,omitempty"`
	Expire  int64  `json:"expire,omitempty"`
	Privsep int    `json:"privsep"`
}

// Group 用户组
type Group struct {
	GroupID string `json:"groupid"`
	Comment string `json:"comment,omitempty"`
	Users   []string `json:"users,omitempty"`
}

// Role 角色
type Role struct {
	RoleID string            `json:"roleid"`
	Privs  map[string]bool   `json:"privs"`
}

// ACL 访问控制列表
type ACL struct {
	Path     string `json:"path"`
	Type     string `json:"type"`
	UGid     string `json:"ugid"`
	RoleID   string `json:"roleid"`
	Propagate int   `json:"propagate,omitempty"`
}

// LXCContainer LXC容器信息
type LXCContainer struct {
	VMID      int     `json:"vmid"`
	Name      string  `json:"name,omitempty"`
	Status    string  `json:"status"`
	Lock      string  `json:"lock,omitempty"`
	CPU       float64 `json:"cpu,omitempty"`
	CPUs      int     `json:"cpus,omitempty"`
	Disk      int64   `json:"disk,omitempty"`
	MaxDisk   int64   `json:"maxdisk,omitempty"`
	Mem       int64   `json:"mem,omitempty"`
	MaxMem    int64   `json:"maxmem,omitempty"`
	NetIn     int64   `json:"netin,omitempty"`
	NetOut    int64   `json:"netout,omitempty"`
	SwapIn    int64   `json:"swapin,omitempty"`
	SwapOut   int64   `json:"swapout,omitempty"`
	Template  int     `json:"template,omitempty"`
	Type      string  `json:"type"`
	Uptime    int64   `json:"uptime,omitempty"`
}

// LXCConfig LXC容器配置
type LXCConfig struct {
	VMID         int                    `json:"vmid"`
	OSTemplate   string                 `json:"ostemplate,omitempty"`
	Arch         string                 `json:"arch,omitempty"`
	CMode        string                 `json:"cmode,omitempty"`
	Console      int                    `json:"console,omitempty"`
	Cores        int                    `json:"cores,omitempty"`
	CPULimit     int                    `json:"cpulimit,omitempty"`
	CPUUnits     int                    `json:"cpuunits,omitempty"`
	Description  string                 `json:"description,omitempty"`
	Hostname     string                 `json:"hostname,omitempty"`
	Memory       int                    `json:"memory,omitempty"`
	NameServer   string                 `json:"nameserver,omitempty"`
	OnBoot       int                    `json:"onboot,omitempty"`
	Protection   int                    `json:"protection,omitempty"`
	RootFS       string                 `json:"rootfs,omitempty"`
	SearchDomain string                 `json:"searchdomain,omitempty"`
	Startup      string                 `json:"startup,omitempty"`
	Swap         int                    `json:"swap,omitempty"`
	Tags         string                 `json:"tags,omitempty"`
	Template     int                    `json:"template,omitempty"`
	TTY          int                    `json:"tty,omitempty"`
	Unprivileged int                    `json:"unprivileged,omitempty"`
	Options      map[string]interface{} `json:"-"` // 其他动态配置选项
}

// CreateLXCParams 创建LXC容器参数
type CreateLXCParams struct {
	VMID       int    `json:"vmid"`
	OSTemplate string `json:"ostemplate"`
	Storage    string `json:"storage,omitempty"`
	Memory     int    `json:"memory,omitempty"`
	Swap       int    `json:"swap,omitempty"`
	Cores      int    `json:"cores,omitempty"`
	RootFS     string `json:"rootfs,omitempty"`
	Net0       string `json:"net0,omitempty"`
	Password   string `json:"password,omitempty"`
	SSHKeys    string `json:"ssh-public-keys,omitempty"`
	Hostname   string `json:"hostname,omitempty"`
}

// NetworkInterface 网络接口
type NetworkInterface struct {
	Iface     string  `json:"iface"`
	Type      string  `json:"type"`
	Method    string  `json:"method,omitempty"`
	Method6   string  `json:"method6,omitempty"`
	Families  []string `json:"families,omitempty"`
	Priority  int     `json:"priority,omitempty"`
	Active    int     `json:"active,omitempty"`
	Autostart int     `json:"autostart,omitempty"`
	BridgePorts string `json:"bridge_ports,omitempty"`
	BridgeSTP string `json:"bridge_stp,omitempty"`
	BridgeFD  string `json:"bridge_fd,omitempty"`
	CIDR      string `json:"cidr,omitempty"`
	Gateway   string `json:"gateway,omitempty"`
	Gateway6  string `json:"gateway6,omitempty"`
	Netmask   string `json:"netmask,omitempty"`
	Netmask6  string `json:"netmask6,omitempty"`
	Comments  string `json:"comments,omitempty"`
}

// FirewallRule 防火墙规则
type FirewallRule struct {
	Pos     int    `json:"pos"`
	Action  string `json:"action"`
	Type    string `json:"type"`
	Enable  int    `json:"enable,omitempty"`
	Source  string `json:"source,omitempty"`
	Dest    string `json:"dest,omitempty"`
	Proto   string `json:"proto,omitempty"`
	DPort   string `json:"dport,omitempty"`
	Sport   string `json:"sport,omitempty"`
	IFace   string `json:"iface,omitempty"`
	Log     string `json:"log,omitempty"`
	Comment string `json:"comment,omitempty"`
}

// BackupJob 备份任务
type BackupJob struct {
	ID          string `json:"id"`
	StartTime   string `json:"starttime"`
	Dow         string `json:"dow"`
	Node        string `json:"node,omitempty"`
	All         int    `json:"all,omitempty"`
	VMID        string `json:"vmid,omitempty"`
	Storage     string `json:"storage"`
	MailTo      string `json:"mailto,omitempty"`
	MailNotification string `json:"mailnotification,omitempty"`
	Compress    string `json:"compress,omitempty"`
	Mode        string `json:"mode,omitempty"`
	Enabled     int    `json:"enabled"`
	Comment     string `json:"comment,omitempty"`
}

// Certificate 证书信息
type Certificate struct {
	Filename    string   `json:"filename"`
	Fingerprint string   `json:"fingerprint"`
	Issuer      string   `json:"issuer"`
	NotAfter    int64    `json:"notafter"`
	NotBefore   int64    `json:"notbefore"`
	PEM         string   `json:"pem"`
	PublicKeyBits int    `json:"public-key-bits"`
	PublicKeyType string `json:"public-key-type"`
	San         []string `json:"san,omitempty"`
	Subject     string   `json:"subject"`
}

// Subscription 订阅信息
type Subscription struct {
	Status        string `json:"status"`
	CheckTime     int64  `json:"checktime"`
	Key           string `json:"key,omitempty"`
	Level         string `json:"level,omitempty"`
	NextDueDate   string `json:"nextduedate,omitempty"`
	ProductName   string `json:"productname,omitempty"`
	RegDate       string `json:"regdate,omitempty"`
	ServerID      string `json:"serverid,omitempty"`
	SupportLevel  string `json:"supportlevel,omitempty"`
	URL           string `json:"url,omitempty"`
}

// ReplicationJob 复制任务
type ReplicationJob struct {
	ID         string `json:"id"`
	Type       string `json:"type"`
	Target     string `json:"target"`
	Enabled    int    `json:"enabled"`
	Comment    string `json:"comment,omitempty"`
	Rate       int    `json:"rate,omitempty"`
	Schedule   string `json:"schedule,omitempty"`
	Source     string `json:"source,omitempty"`
	RemoveJob  int    `json:"remove_job,omitempty"`
}

// Metrics 指标配置
type Metrics struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Server   string `json:"server"`
	Port     int    `json:"port"`
	Enabled  int    `json:"enabled"`
	MTU      int    `json:"mtu,omitempty"`
	Timeout  int    `json:"timeout,omitempty"`
	Disable  int    `json:"disable,omitempty"`
}

// Realm 认证域
type Realm struct {
	Realm    string `json:"realm"`
	Type     string `json:"type"`
	Comment  string `json:"comment,omitempty"`
	Default  int    `json:"default,omitempty"`
	Domain   string `json:"domain,omitempty"`
	Port     int    `json:"port,omitempty"`
	Secure   int    `json:"secure,omitempty"`
	Server1  string `json:"server1,omitempty"`
	Server2  string `json:"server2,omitempty"`
	TFA      string `json:"tfa,omitempty"`
}