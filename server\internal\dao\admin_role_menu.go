// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalAdminRoleMenuDao is internal type for wrapping internal DAO implements.
type internalAdminRoleMenuDao = *internal.AdminRoleMenuDao

// adminRoleMenuDao is the data access object for table hg_admin_role_menu.
// You can define custom methods on it to extend its functionality as you wish.
type adminRoleMenuDao struct {
	internalAdminRoleMenuDao
}

var (
	// AdminRoleMenu is globally public accessible object for table hg_admin_role_menu operations.
	AdminRoleMenu = adminRoleMenuDao{
		internal.NewAdminRoleMenuDao(),
	}
)

// Fill with you ideas below.
