# PVE Cloud Platform Plugin

这是一个基于HotGo框架开发的PVE(Proxmox VE)云平台管理插件，提供完整的虚拟化基础设施管理功能。

## 功能特性

### 核心功能
- **节点管理**: 管理PVE集群节点，支持连接测试、状态同步和监控
- **实例管理**: 虚拟机生命周期管理，包括创建、启停、删除、监控、控制台访问
- **模板管理**: PVE模板同步、导入和配置管理
- **仪表盘**: 集中显示资源使用情况、系统状态和告警信息

### 高级功能
- **自动化运维**: 定时任务自动同步节点和实例状态
- **消息队列**: 异步处理虚拟机操作，提升系统性能
- **监控告警**: 实时监控资源使用情况和系统状态
- **快照管理**: 虚拟机快照的创建、恢复和删除
- **用户权限**: 基于角色的访问控制

## 技术架构

### 后端技术栈
- **框架**: GoFrame v2
- **数据库**: MySQL/PostgreSQL
- **认证**: JWT + Casbin权限控制
- **通信**: PVE REST API客户端
- **队列**: 内置消息队列系统

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Naive UI
- **图表**: ECharts
- **构建工具**: Vite
- **状态管理**: Pinia

## 安装部署

### 1. 前置条件
- Go 1.19+
- Node.js 16+
- MySQL 5.7+ 或 PostgreSQL 9.6+
- HotGo v2.17.8+

### 2. 插件安装
```bash
# 1. 将插件复制到HotGo项目的addons目录
cp -r pve /path/to/hotgo/server/addons/

# 2. 安装前端依赖
cd /path/to/hotgo/web && npm install

# 3. 编译项目
cd /path/to/hotgo/server && make build

# 4. 启动服务
make all
```

### 3. 插件激活
1. 登录HotGo管理后台
2. 进入"系统管理" > "插件管理"
3. 找到"PVE云平台"插件并点击"安装"
4. 安装完成后会自动创建数据库表和菜单

## 使用指南

### 1. 节点配置
首次使用需要先配置PVE节点：
- 进入"PVE管理" > "节点管理"
- 点击"添加节点"填写PVE服务器信息
- 支持密码认证和API Token认证
- 测试连接成功后保存

### 2. 模板同步
- 进入"PVE管理" > "模板管理"
- 点击"同步模板"选择节点
- 配置模板信息后导入到系统

### 3. 创建实例
- 进入"PVE管理" > "实例管理"
- 点击"创建实例"
- 选择节点、模板、配置资源
- 设置购买周期后创建

### 4. 监控管理
- 仪表盘可查看整体状态
- 节点监控显示资源使用情况
- 实例监控显示虚拟机性能数据

## 数据库结构

### 主要数据表
- `hg_pve_nodes`: PVE节点信息
- `hg_pve_instances`: 虚拟机实例
- `hg_pve_templates`: 系统模板

### 配置参数
- 支持多种认证方式
- 灵活的资源配置
- 完整的审计日志

## API接口

### 节点管理
- `POST /admin/addons/pve/nodes/create` - 创建节点
- `GET /admin/addons/pve/nodes/list` - 节点列表
- `POST /admin/addons/pve/nodes/test` - 测试连接
- `POST /admin/addons/pve/nodes/sync` - 同步状态

### 实例管理
- `POST /admin/addons/pve/instances/create` - 创建实例
- `GET /admin/addons/pve/instances/list` - 实例列表
- `POST /admin/addons/pve/instances/action` - 实例操作
- `GET /admin/addons/pve/instances/console` - 控制台访问

### 模板管理
- `POST /admin/addons/pve/templates/sync` - 同步模板
- `POST /admin/addons/pve/templates/import` - 导入模板
- `GET /admin/addons/pve/templates/select` - 选择列表

## 定时任务

### 自动任务
- **节点状态同步**: 每5分钟同步节点状态
- **实例状态同步**: 每10分钟同步实例状态  
- **到期检查**: 每天检查实例到期时间

### 消息队列
- **实例操作队列**: 异步处理虚拟机操作
- **节点同步队列**: 异步同步节点数据
- **通知队列**: 发送邮件和消息通知

## 安全特性

- **权限控制**: 基于Casbin的细粒度权限控制
- **操作审计**: 完整记录所有操作日志
- **数据加密**: 敏感数据加密存储
- **安全连接**: 支持SSL/TLS连接PVE

## 扩展开发

### 插件结构
```
server/addons/pve/
├── api/           # API接口定义
├── controller/    # 控制器层
├── logic/         # 业务逻辑层
├── model/         # 数据模型
├── service/       # 服务接口
├── library/       # 工具库
├── crons/         # 定时任务
├── queues/        # 消息队列
└── router/        # 路由配置
```

### 自定义扩展
- 支持自定义模板类型
- 支持自定义资源配置
- 支持自定义监控指标
- 支持自定义告警规则

## 故障排除

### 常见问题
1. **连接失败**: 检查PVE服务器网络和认证配置
2. **创建失败**: 检查模板是否存在和资源是否充足
3. **同步异常**: 检查API权限和网络连接
4. **前端报错**: 检查浏览器控制台和网络请求

### 日志查看
- 后端日志: `server/logs/`
- 前端错误: 浏览器开发者工具
- PVE日志: `/var/log/pve/`

## 更新日志

### v1.0.0 (2024-08-28)
- ✨ 初始版本发布
- ✨ 完整的PVE管理功能
- ✨ 响应式Web界面
- ✨ 自动化运维支持
- ✨ 监控告警系统

## 技术支持

- 项目地址: https://github.com/bufanyun/hotgo
- 文档地址: https://docs.hotgo.com
- 问题反馈: GitHub Issues
- 技术交流: QQ群/微信群

## 开源协议

本项目基于 MIT 协议开源，详见 [LICENSE](LICENSE) 文件。