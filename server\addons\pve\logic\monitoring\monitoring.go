// Package monitoring
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package monitoring

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/library/pveclient"
	"hotgo/internal/consts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/adminin"
	adminService "hotgo/internal/service"
)

// MetricType 监控指标类型
type MetricType string

const (
	MetricTypeCPU     MetricType = "cpu"     // CPU使用率
	MetricTypeMemory  MetricType = "memory"  // 内存使用率
	MetricTypeDisk    MetricType = "disk"    // 磁盘使用率
	MetricTypeNetwork MetricType = "network" // 网络流量
	MetricTypeLoad    MetricType = "load"    // 系统负载
)

// AlertLevel 告警级别
type AlertLevel string

const (
	AlertLevelInfo     AlertLevel = "info"     // 信息
	AlertLevelWarning  AlertLevel = "warning"  // 警告
	AlertLevelCritical AlertLevel = "critical" // 严重
)

// MonitoringMetric 监控指标
type MonitoringMetric struct {
	ID          uint64      `json:"id"`
	InstanceID  uint64      `json:"instanceId"`
	NodeID      uint64      `json:"nodeId"`
	MetricType  MetricType  `json:"metricType"`
	Value       float64     `json:"value"`
	Unit        string      `json:"unit"`
	Timestamp   *gtime.Time `json:"timestamp"`
	CreatedAt   *gtime.Time `json:"createdAt"`
}

// AlertRule 告警规则
type AlertRule struct {
	ID         uint64     `json:"id"`
	Name       string     `json:"name"`
	MetricType MetricType `json:"metricType"`
	Operator   string     `json:"operator"`   // >, <, >=, <=, ==
	Threshold  float64    `json:"threshold"`  // 阈值
	Level      AlertLevel `json:"level"`      // 告警级别
	Duration   int        `json:"duration"`   // 持续时间（秒）
	Enabled    bool       `json:"enabled"`    // 是否启用
	CreatedAt  *gtime.Time `json:"createdAt"`
}

// Alert 告警记录
type Alert struct {
	ID           uint64      `json:"id"`
	RuleID       uint64      `json:"ruleId"`
	InstanceID   uint64      `json:"instanceId"`
	NodeID       uint64      `json:"nodeId"`
	MetricType   MetricType  `json:"metricType"`
	Level        AlertLevel  `json:"level"`
	Message      string      `json:"message"`
	Value        float64     `json:"value"`
	Threshold    float64     `json:"threshold"`
	Status       string      `json:"status"`  // active, resolved
	TriggeredAt  *gtime.Time `json:"triggeredAt"`
	ResolvedAt   *gtime.Time `json:"resolvedAt"`
	CreatedAt    *gtime.Time `json:"createdAt"`
}

// CollectInstanceMetrics 收集实例监控指标
func CollectInstanceMetrics(ctx context.Context, instanceID uint64) error {
	// 获取实例和节点信息
	instance, err := g.DB().Model("hg_pve_instances i").
		InnerJoin("hg_pve_nodes n", "i.node_id = n.id").
		Fields("i.*, n.host, n.port, n.username, n.password, n.token_id, n.token_secret, n.name as node_name").
		Where("i.id", instanceID).
		One()
	if err != nil {
		return fmt.Errorf("获取实例信息失败: %v", err)
	}
	if instance == nil {
		return fmt.Errorf("实例 %d 不存在", instanceID)
	}

	// 创建PVE客户端
	config := &pveclient.Config{
		Host:        gconv.String(instance["host"]),
		Port:        gconv.Int(instance["port"]),
		Username:    gconv.String(instance["username"]),
		Password:    gconv.String(instance["password"]),
		TokenID:     gconv.String(instance["token_id"]),
		TokenSecret: gconv.String(instance["token_secret"]),
		Insecure:    true,
		Timeout:     30 * time.Second,
	}

	client, err := pveclient.NewClient(config)
	if err != nil {
		return fmt.Errorf("创建PVE客户端失败: %v", err)
	}

	// 获取虚拟机状态
	nodeName := gconv.String(instance["node_name"])
	vmid := gconv.Int(instance["vmid"])
	nodeID := gconv.Uint64(instance["node_id"])

	vmStatus, err := client.GetVMStatus(ctx, nodeName, vmid)
	if err != nil {
		return fmt.Errorf("获取虚拟机状态失败: %v", err)
	}

	now := gtime.Now()
	
	// 保存监控指标
	metrics := []MonitoringMetric{
		{
			InstanceID: instanceID,
			NodeID:     nodeID,
			MetricType: MetricTypeCPU,
			Value:      vmStatus.CPU * 100,
			Unit:       "%",
			Timestamp:  now,
			CreatedAt:  now,
		},
		{
			InstanceID: instanceID,
			NodeID:     nodeID,
			MetricType: MetricTypeMemory,
			Value:      float64(vmStatus.Memory) / float64(vmStatus.MaxMem) * 100,
			Unit:       "%",
			Timestamp:  now,
			CreatedAt:  now,
		},
	}

	// 批量插入监控数据
	for _, metric := range metrics {
		_, err := g.DB().Model("hg_pve_monitoring_metrics").Data(metric).Insert()
		if err != nil {
			g.Log().Error(ctx, "保存监控指标失败:", err)
		}
	}

	// 检查告警规则
	err = checkAlertRules(ctx, instanceID, nodeID, metrics)
	if err != nil {
		g.Log().Error(ctx, "检查告警规则失败:", err)
	}

	return nil
}

// checkAlertRules 检查告警规则
func checkAlertRules(ctx context.Context, instanceID, nodeID uint64, metrics []MonitoringMetric) error {
	// 获取启用的告警规则
	rules, err := g.DB().Model("hg_pve_alert_rules").Where("enabled", 1).All()
	if err != nil {
		return err
	}

	for _, rule := range rules {
		ruleID := gconv.Uint64(rule["id"])
		ruleName := gconv.String(rule["name"])
		metricType := MetricType(gconv.String(rule["metric_type"]))
		operator := gconv.String(rule["operator"])
		threshold := gconv.Float64(rule["threshold"])
		level := AlertLevel(gconv.String(rule["level"]))

		// 找到对应的监控指标
		for _, metric := range metrics {
			if metric.MetricType != metricType {
				continue
			}

			// 判断是否触发告警
			triggered := false
			switch operator {
			case ">":
				triggered = metric.Value > threshold
			case ">=":
				triggered = metric.Value >= threshold
			case "<":
				triggered = metric.Value < threshold
			case "<=":
				triggered = metric.Value <= threshold
			case "==":
				triggered = metric.Value == threshold
			}

			if triggered {
				// 创建告警记录
				err := createAlert(ctx, &Alert{
					RuleID:      ruleID,
					InstanceID:  instanceID,
					NodeID:      nodeID,
					MetricType:  metricType,
					Level:       level,
					Message:     fmt.Sprintf("规则 [%s] 触发告警: %s %.2f%s (阈值: %.2f)", ruleName, metricType, metric.Value, metric.Unit, threshold),
					Value:       metric.Value,
					Threshold:   threshold,
					Status:      "active",
					TriggeredAt: metric.Timestamp,
					CreatedAt:   gtime.Now(),
				})
				if err != nil {
					g.Log().Error(ctx, "创建告警记录失败:", err)
				}
			}
		}
	}

	return nil
}

// createAlert 创建告警记录
func createAlert(ctx context.Context, alert *Alert) error {
	// 检查是否已存在相同的活跃告警
	count, err := g.DB().Model("hg_pve_alerts").
		Where("rule_id", alert.RuleID).
		Where("instance_id", alert.InstanceID).
		Where("status", "active").
		Count()
	if err != nil {
		return err
	}
	if count > 0 {
		// 已存在活跃告警，不重复创建
		return nil
	}

	// 插入告警记录
	alertID, err := g.DB().Model("hg_pve_alerts").Data(alert).InsertAndGetId()
	if err != nil {
		return err
	}

	// 发送告警通知
	err = sendAlertNotification(ctx, gconv.Uint64(alertID), alert)
	if err != nil {
		g.Log().Error(ctx, "发送告警通知失败:", err)
	}

	g.Log().Warning(ctx, "PVE告警触发", g.Map{
		"alertId":    alertID,
		"instanceId": alert.InstanceID,
		"level":      alert.Level,
		"message":    alert.Message,
	})

	return nil
}

// sendAlertNotification 发送告警通知
func sendAlertNotification(ctx context.Context, alertID uint64, alert *Alert) error {
	// 获取实例创建者信息
	instance, err := g.DB().Model("hg_pve_instances").
		Fields("created_by, name").
		Where("id", alert.InstanceID).
		One()
	if err != nil {
		return err
	}
	if instance == nil {
		return fmt.Errorf("实例 %d 不存在", alert.InstanceID)
	}

	createdBy := gconv.Int64(instance["created_by"])
	instanceName := gconv.String(instance["name"])

	// 构建通知内容
	title := fmt.Sprintf("PVE监控告警 - %s", alert.Level)
	content := fmt.Sprintf(`
<div>
	<h3 style="color: %s;">%s级别告警</h3>
	<p><strong>实例名称：</strong>%s</p>
	<p><strong>告警类型：</strong>%s</p>
	<p><strong>告警信息：</strong>%s</p>
	<p><strong>当前值：</strong>%.2f</p>
	<p><strong>阈值：</strong>%.2f</p>
	<p><strong>触发时间：</strong>%s</p>
	<p style="color: red;"><strong>请及时处理此告警！</strong></p>
</div>
`, getLevelColor(alert.Level), alert.Level, instanceName, alert.MetricType, alert.Message, alert.Value, alert.Threshold, alert.TriggeredAt.Format("2006-01-02 15:04:05"))

	// 发送系统通知
	return adminService.AdminNotice().Edit(ctx, &adminin.NoticeEditInp{
		AdminNotice: entity.AdminNotice{
			Title:   title,
			Content: content,
			Type:    consts.NoticeTypeLetter,
		},
		Receiver: []int64{createdBy},
	})
}

// getLevelColor 获取告警级别对应的颜色
func getLevelColor(level AlertLevel) string {
	switch level {
	case AlertLevelInfo:
		return "#1890ff"
	case AlertLevelWarning:
		return "#faad14"
	case AlertLevelCritical:
		return "#ff4d4f"
	default:
		return "#666"
	}
}

// GetInstanceMetrics 获取实例监控指标历史数据
func GetInstanceMetrics(ctx context.Context, instanceID uint64, metricType MetricType, startTime, endTime *gtime.Time, limit int) ([]*MonitoringMetric, error) {
	model := g.DB().Model("hg_pve_monitoring_metrics").
		Where("instance_id", instanceID).
		Where("metric_type", string(metricType)).
		Order("timestamp DESC")

	if startTime != nil {
		model = model.Where("timestamp >= ?", startTime)
	}
	if endTime != nil {
		model = model.Where("timestamp <= ?", endTime)
	}
	if limit > 0 {
		model = model.Limit(limit)
	}

	var metrics []*MonitoringMetric
	err := model.Scan(&metrics)
	return metrics, err
}

// ResolveAlert 解决告警
func ResolveAlert(ctx context.Context, alertID uint64) error {
	_, err := g.DB().Model("hg_pve_alerts").
		Data(g.Map{
			"status":      "resolved",
			"resolved_at": gtime.Now(),
		}).
		Where("id", alertID).
		Update()
	if err != nil {
		return err
	}

	g.Log().Info(ctx, "PVE告警已解决", g.Map{
		"alertId": alertID,
	})

	return nil
}

// CleanupOldMetrics 清理旧的监控数据
func CleanupOldMetrics(ctx context.Context, retentionDays int) error {
	cutoffTime := gtime.Now().Add(-time.Duration(retentionDays) * 24 * time.Hour)
	
	result, err := g.DB().Model("hg_pve_monitoring_metrics").
		Where("created_at < ?", cutoffTime).
		Delete()
	if err != nil {
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	g.Log().Info(ctx, "清理旧的监控数据完成", g.Map{
		"deletedCount": rowsAffected,
		"cutoffTime":   cutoffTime,
	})

	return nil
}