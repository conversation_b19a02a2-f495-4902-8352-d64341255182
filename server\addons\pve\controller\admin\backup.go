// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
)

var (
	Backup = cBackup{}
)

type cBackup struct{}

// List 获取备份列表
func (c *cBackup) List(ctx context.Context, req *admin.BackupListReq) (res *admin.BackupListRes, err error) {
	// TODO: 实现获取备份列表逻辑
	return &admin.BackupListRes{
		List: []*admin.BackupViewModel{},
	}, nil
}

// Create 创建备份
func (c *cBackup) Create(ctx context.Context, req *admin.BackupCreateReq) (res *admin.BackupCreateRes, err error) {
	// TODO: 实现创建备份逻辑
	return &admin.BackupCreateRes{
		TaskID: "tmp-backup-task",
	}, nil
}

// Delete 删除备份
func (c *cBackup) Delete(ctx context.Context, req *admin.BackupDeleteReq) (res *admin.BackupDeleteRes, err error) {
	// TODO: 实现删除备份逻辑
	return &admin.BackupDeleteRes{
		TaskID: "tmp-delete-task",
	}, nil
}

// Restore 恢复备份
func (c *cBackup) Restore(ctx context.Context, req *admin.BackupRestoreReq) (res *admin.BackupRestoreRes, err error) {
	// TODO: 实现恢复备份逻辑
	return &admin.BackupRestoreRes{
		TaskID: "tmp-restore-task",
	}, nil
}
