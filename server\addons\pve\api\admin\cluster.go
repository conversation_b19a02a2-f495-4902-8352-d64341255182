// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

// ClusterStatusReq 获取集群状态请求
type ClusterStatusReq struct{}

// ClusterStatusRes 获取集群状态响应
type ClusterStatusRes struct {
	Status    string `json:"status" dc:"集群状态"`
	Nodes     int    `json:"nodes" dc:"节点数量"`
	Instances int    `json:"instances" dc:"实例数量"`
	Storage   int    `json:"storage" dc:"存储数量"`
}

// ClusterResourcesReq 获取集群资源请求
type ClusterResourcesReq struct{}

// ClusterResourcesRes 获取集群资源响应
type ClusterResourcesRes struct {
	TotalCPU    int `json:"totalCpu" dc:"总CPU核心数"`
	TotalMemory int `json:"totalMemory" dc:"总内存大小(MB)"`
	TotalDisk   int `json:"totalDisk" dc:"总磁盘大小(GB)"`
	UsedCPU     int `json:"usedCpu" dc:"已使用CPU核心数"`
	UsedMemory  int `json:"usedMemory" dc:"已使用内存大小(MB)"`
	UsedDisk    int `json:"usedDisk" dc:"已使用磁盘大小(GB)"`
}

// ClusterNodesReq 获取集群节点请求
type ClusterNodesReq struct{}

// ClusterNodesRes 获取集群节点响应
type ClusterNodesRes struct {
	Nodes []*ClusterNode `json:"nodes" dc:"节点列表"`
}

// ClusterNode 集群节点信息
type ClusterNode struct {
	ID     int    `json:"id" dc:"节点ID"`
	Name   string `json:"name" dc:"节点名称"`
	Status string `json:"status" dc:"节点状态"`
	CPU    int    `json:"cpu" dc:"CPU核心数"`
	Memory int    `json:"memory" dc:"内存大小(MB)"`
	Disk   int    `json:"disk" dc:"磁盘大小(GB)"`
	Uptime int64  `json:"uptime" dc:"运行时间(秒)"`
	Load   string `json:"load" dc:"系统负载"`
}

// ClusterNextIDReq 获取下一个可用ID请求
type ClusterNextIDReq struct{}

// ClusterNextIDRes 获取下一个可用ID响应
type ClusterNextIDRes struct {
	NextID int `json:"nextId" dc:"下一个可用ID"`
}

// ClusterLogReq 获取集群日志请求
type ClusterLogReq struct {
	Lines int `json:"lines" dc:"日志行数"`
}

// ClusterLogRes 获取集群日志响应
type ClusterLogRes struct {
	Logs []string `json:"logs" dc:"日志内容"`
}
