# 虚拟机生命周期管理

> 基于 PVE API 的完整虚拟机生命周期管理实现

## 概述

虚拟机生命周期管理是 PVE 云平台的核心功能，涵盖虚拟机的创建、启动、停止、重启、删除、快照管理、迁移和资源调整等操作。本文档详细介绍如何实现完整的虚拟机生命周期管理服务。

## 📋 服务架构

### 服务结构定义

```go
package service

import (
    "context"
    "fmt"
    "time"

    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/errors/gerror"
    "hotgo/addons/pve/library/pveclient"
    "hotgo/addons/pve/model/entity"
)

type sVMLifecycle struct {
    client *pveclient.PVEClient
}

func VMLifecycle() *sVMLifecycle {
    return &sVMLifecycle{}
}

// SetClient 设置PVE客户端
func (s *sVMLifecycle) SetClient(client *pveclient.PVEClient) {
    s.client = client
}
```

## 🚀 虚拟机创建

### 从模板创建虚拟机

```go
// CreateVMFromTemplate 从模板创建虚拟机
func (s *sVMLifecycle) CreateVMFromTemplate(ctx context.Context, req *CreateVMFromTemplateReq) (*CreateVMResult, error) {
    // 1. 验证输入参数
    if err := s.validateCreateRequest(ctx, req); err != nil {
        return nil, err
    }

    // 2. 分配VMID
    vmid, err := s.allocateVMID(ctx, req.NodeName)
    if err != nil {
        return nil, gerror.Wrap(err, "分配VMID失败")
    }

    // 3. 从模板克隆虚拟机
    cloneReq := &pveclient.CloneVMRequest{
        NewID:       vmid,
        Name:        req.Name,
        Description: req.Description,
        Full:        true, // 完整克隆
        Target:      req.NodeName,
        Storage:     req.Storage,
    }

    task, err := s.client.CloneVM(ctx, req.TemplateNode, req.TemplateVMID, cloneReq)
    if err != nil {
        return nil, gerror.Wrap(err, "克隆虚拟机失败")
    }

    // 4. 等待克隆任务完成
    err = s.client.WaitForTask(ctx, task, 5*time.Second, 10*time.Minute)
    if err != nil {
        return nil, gerror.Wrap(err, "等待虚拟机克隆完成失败")
    }

    // 5. 配置虚拟机参数
    err = s.configureVM(ctx, req.NodeName, vmid, req.Config)
    if err != nil {
        return nil, gerror.Wrap(err, "配置虚拟机参数失败")
    }

    // 6. 更新数据库记录
    err = s.createVMRecord(ctx, req, vmid)
    if err != nil {
        return nil, gerror.Wrap(err, "创建虚拟机记录失败")
    }

    return &CreateVMResult{
        VMID:     vmid,
        Name:     req.Name,
        NodeName: req.NodeName,
        Status:   "stopped",
    }, nil
}

// 辅助方法：验证创建请求
func (s *sVMLifecycle) validateCreateRequest(ctx context.Context, req *CreateVMFromTemplateReq) error {
    if req.Name == "" {
        return gerror.New("虚拟机名称不能为空")
    }
    if req.NodeName == "" {
        return gerror.New("节点名称不能为空")
    }
    if req.TemplateVMID <= 0 {
        return gerror.New("模板VMID无效")
    }
    if req.Config.Cores <= 0 {
        return gerror.New("CPU核心数必须大于0")
    }
    if req.Config.Memory <= 0 {
        return gerror.New("内存大小必须大于0")
    }
    return nil
}

// 辅助方法：分配VMID
func (s *sVMLifecycle) allocateVMID(ctx context.Context, nodeName string) (int, error) {
    // 从数据库中查找可用的VMID
    var maxVMID int
    err := g.DB().Model("pve_instances").Fields("MAX(vmid)").Value(&maxVMID)
    if err != nil {
        return 0, err
    }
    
    // 从100开始分配VMID（避免与已有的冲突）
    if maxVMID < 100 {
        maxVMID = 100
    }
    
    return maxVMID + 1, nil
}

// 辅助方法：配置虚拟机参数
func (s *sVMLifecycle) configureVM(ctx context.Context, nodeName string, vmid int, config *VMConfig) error {
    updateReq := map[string]interface{}{
        "cores":  config.Cores,
        "memory": config.Memory,
    }
    
    if config.OSType != "" {
        updateReq["ostype"] = config.OSType
    }
    if config.Agent {
        updateReq["agent"] = 1
    }
    
    return s.client.UpdateVMConfig(ctx, nodeName, vmid, updateReq)
}
```

## ⚡ 虚拟机电源管理

### 启动虚拟机

```go
// StartVM 启动虚拟机
func (s *sVMLifecycle) StartVM(ctx context.Context, instanceID uint64) error {
    // 1. 获取虚拟机信息
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 2. 检查当前状态
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    if vmStatus.Status == "running" {
        return gerror.New("虚拟机已经在运行中")
    }

    // 3. 启动虚拟机
    task, err := s.client.StartVM(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "启动虚拟机失败")
    }

    // 4. 等待启动完成
    err = s.client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机启动完成失败")
    }

    // 5. 更新数据库状态
    err = s.updateInstanceStatus(ctx, instanceID, "running")
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机状态失败")
    }

    // 6. 记录操作日志
    s.logVMOperation(ctx, instanceID, "start", "虚拟机启动成功")

    return nil
}
```

### 停止虚拟机

```go
// StopVM 停止虚拟机
func (s *sVMLifecycle) StopVM(ctx context.Context, instanceID uint64, force bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 检查当前状态
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    if vmStatus.Status == "stopped" {
        return gerror.New("虚拟机已经停止")
    }

    var task *pveclient.TaskInfo
    if force {
        // 强制停止
        task, err = s.client.StopVM(ctx, instance.NodeName, instance.VMID)
    } else {
        // 优雅关闭
        task, err = s.client.ShutdownVM(ctx, instance.NodeName, instance.VMID)
    }

    if err != nil {
        return gerror.Wrap(err, "停止虚拟机失败")
    }

    // 等待停止完成
    err = s.client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机停止完成失败")
    }

    // 更新状态
    err = s.updateInstanceStatus(ctx, instanceID, "stopped")
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机状态失败")
    }

    operation := "stop"
    if force {
        operation = "force_stop"
    }
    s.logVMOperation(ctx, instanceID, operation, "虚拟机停止成功")

    return nil
}
```

### 重启虚拟机

```go
// RebootVM 重启虚拟机
func (s *sVMLifecycle) RebootVM(ctx context.Context, instanceID uint64, force bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    var task *pveclient.TaskInfo
    if force {
        // 强制重启
        task, err = s.client.ResetVM(ctx, instance.NodeName, instance.VMID)
    } else {
        // 优雅重启
        task, err = s.client.RebootVM(ctx, instance.NodeName, instance.VMID)
    }

    if err != nil {
        return gerror.Wrap(err, "重启虚拟机失败")
    }

    err = s.client.WaitForTask(ctx, task, 2*time.Second, 5*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机重启完成失败")
    }

    operation := "reboot"
    if force {
        operation = "reset"
    }
    s.logVMOperation(ctx, instanceID, operation, "虚拟机重启成功")

    return nil
}
```

## 🗑️ 虚拟机删除

```go
// DeleteVM 删除虚拟机
func (s *sVMLifecycle) DeleteVM(ctx context.Context, instanceID uint64, purge bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 1. 检查虚拟机状态，如果运行中则先停止
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    if vmStatus.Status == "running" {
        // 强制停止虚拟机
        err = s.StopVM(ctx, instanceID, true)
        if err != nil {
            return gerror.Wrap(err, "停止虚拟机失败")
        }
    }

    // 2. 删除虚拟机
    task, err := s.client.DeleteVM(ctx, instance.NodeName, instance.VMID, purge)
    if err != nil {
        return gerror.Wrap(err, "删除虚拟机失败")
    }

    err = s.client.WaitForTask(ctx, task, 2*time.Second, 10*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机删除完成失败")
    }

    // 3. 更新数据库状态
    err = s.updateInstanceStatus(ctx, instanceID, "deleted")
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机状态失败")
    }

    s.logVMOperation(ctx, instanceID, "delete", "虚拟机删除成功")

    return nil
}
```

## 📸 快照管理

### 创建快照

```go
// CreateSnapshot 创建快照
func (s *sVMLifecycle) CreateSnapshot(ctx context.Context, instanceID uint64, snapname, description string) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    task, err := s.client.CreateSnapshot(ctx, instance.NodeName, instance.VMID, snapname, description)
    if err != nil {
        return gerror.Wrap(err, "创建快照失败")
    }

    err = s.client.WaitForTask(ctx, task, 5*time.Second, 15*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待快照创建完成失败")
    }

    s.logVMOperation(ctx, instanceID, "snapshot", fmt.Sprintf("创建快照: %s", snapname))

    return nil
}

// RestoreSnapshot 恢复快照
func (s *sVMLifecycle) RestoreSnapshot(ctx context.Context, instanceID uint64, snapname string) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    task, err := s.client.RollbackSnapshot(ctx, instance.NodeName, instance.VMID, snapname)
    if err != nil {
        return gerror.Wrap(err, "恢复快照失败")
    }

    err = s.client.WaitForTask(ctx, task, 5*time.Second, 15*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待快照恢复完成失败")
    }

    s.logVMOperation(ctx, instanceID, "restore", fmt.Sprintf("恢复快照: %s", snapname))

    return nil
}

// DeleteSnapshot 删除快照
func (s *sVMLifecycle) DeleteSnapshot(ctx context.Context, instanceID uint64, snapname string) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    task, err := s.client.DeleteSnapshot(ctx, instance.NodeName, instance.VMID, snapname)
    if err != nil {
        return gerror.Wrap(err, "删除快照失败")
    }

    err = s.client.WaitForTask(ctx, task, 5*time.Second, 10*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待快照删除完成失败")
    }

    s.logVMOperation(ctx, instanceID, "delete_snapshot", fmt.Sprintf("删除快照: %s", snapname))

    return nil
}
```

## 🔄 虚拟机迁移

```go
// MigrateVM 迁移虚拟机
func (s *sVMLifecycle) MigrateVM(ctx context.Context, instanceID uint64, targetNode string, online bool) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    if instance.NodeName == targetNode {
        return gerror.New("目标节点与当前节点相同")
    }

    // 检查目标节点是否可用
    nodes, err := s.client.GetNodes(ctx)
    if err != nil {
        return gerror.Wrap(err, "获取节点列表失败")
    }

    targetNodeExists := false
    for _, node := range nodes {
        if node.Node == targetNode && node.Status == "online" {
            targetNodeExists = true
            break
        }
    }

    if !targetNodeExists {
        return gerror.New("目标节点不存在或不在线")
    }

    // 执行迁移
    task, err := s.client.MigrateVM(ctx, instance.NodeName, instance.VMID, targetNode, online)
    if err != nil {
        return gerror.Wrap(err, "虚拟机迁移失败")
    }

    err = s.client.WaitForTask(ctx, task, 10*time.Second, 30*time.Minute)
    if err != nil {
        return gerror.Wrap(err, "等待虚拟机迁移完成失败")
    }

    // 更新数据库中的节点信息
    err = s.updateInstanceNode(ctx, instanceID, targetNode)
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机节点信息失败")
    }

    migrationType := "offline"
    if online {
        migrationType = "online"
    }
    s.logVMOperation(ctx, instanceID, "migrate", 
        fmt.Sprintf("虚拟机%s迁移成功: %s -> %s", migrationType, instance.NodeName, targetNode))

    return nil
}
```

## ⚙️ 虚拟机配置调整

```go
// ResizeVM 调整虚拟机配置
func (s *sVMLifecycle) ResizeVM(ctx context.Context, instanceID uint64, config *ResizeVMConfig) error {
    instance, err := s.getInstanceInfo(ctx, instanceID)
    if err != nil {
        return err
    }

    // 检查虚拟机状态
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }

    // 准备配置更新参数
    updateReq := map[string]interface{}{}
    
    if config.Cores > 0 {
        updateReq["cores"] = config.Cores
    }
    if config.Memory > 0 {
        updateReq["memory"] = config.Memory
    }
    if config.Balloon > 0 {
        updateReq["balloon"] = config.Balloon
    }

    // 热调整检查（运行中的虚拟机）
    if vmStatus.Status == "running" {
        // 检查是否支持热调整
        vmConfig, err := s.client.GetVMConfig(ctx, instance.NodeName, instance.VMID)
        if err != nil {
            return gerror.Wrap(err, "获取虚拟机配置失败")
        }

        // CPU热插拔需要特定条件
        if config.Cores > 0 && config.Cores != vmConfig.Cores {
            if vmConfig.Hotplug == "" || !strings.Contains(vmConfig.Hotplug, "cpu") {
                return gerror.New("虚拟机不支持CPU热插拔，请先关闭虚拟机")
            }
        }

        // 内存热插拔需要特定条件
        if config.Memory > 0 && config.Memory != vmConfig.Memory {
            if vmConfig.Hotplug == "" || !strings.Contains(vmConfig.Hotplug, "memory") {
                return gerror.New("虚拟机不支持内存热插拔，请先关闭虚拟机")
            }
        }
    }

    // 执行配置更新
    err = s.client.UpdateVMConfig(ctx, instance.NodeName, instance.VMID, updateReq)
    if err != nil {
        return gerror.Wrap(err, "更新虚拟机配置失败")
    }

    // 更新数据库记录
    if config.Cores > 0 || config.Memory > 0 {
        updateData := g.Map{}
        if config.Cores > 0 {
            updateData["cpu_cores"] = config.Cores
        }
        if config.Memory > 0 {
            updateData["memory_mb"] = config.Memory
        }
        
        _, err = g.DB().Model("pve_instances").Where("id", instanceID).Update(updateData)
        if err != nil {
            return gerror.Wrap(err, "更新虚拟机数据库记录失败")
        }
    }

    s.logVMOperation(ctx, instanceID, "resize", 
        fmt.Sprintf("调整虚拟机配置: CPU=%d, Memory=%dMB", config.Cores, config.Memory))

    return nil
}
```

## 🛠️ 辅助方法

### 数据库操作辅助方法

```go
// getInstanceInfo 获取实例信息
func (s *sVMLifecycle) getInstanceInfo(ctx context.Context, instanceID uint64) (*InstanceInfo, error) {
    var instance InstanceInfo
    err := g.DB().Model("pve_instances").
        Fields("id, vmid, name, node_name, status").
        Where("id", instanceID).
        Scan(&instance)
    
    if err != nil {
        return nil, gerror.Wrap(err, "获取虚拟机实例信息失败")
    }
    
    if instance.ID == 0 {
        return nil, gerror.New("虚拟机实例不存在")
    }
    
    return &instance, nil
}

// updateInstanceStatus 更新实例状态
func (s *sVMLifecycle) updateInstanceStatus(ctx context.Context, instanceID uint64, status string) error {
    _, err := g.DB().Model("pve_instances").
        Where("id", instanceID).
        Update(g.Map{
            "status":     status,
            "updated_at": time.Now(),
        })
    return err
}

// updateInstanceNode 更新实例节点
func (s *sVMLifecycle) updateInstanceNode(ctx context.Context, instanceID uint64, nodeName string) error {
    // 获取新节点ID
    var nodeID uint64
    err := g.DB().Model("pve_nodes").Where("name", nodeName).Value("id", &nodeID)
    if err != nil {
        return err
    }

    _, err = g.DB().Model("pve_instances").
        Where("id", instanceID).
        Update(g.Map{
            "node_id":    nodeID,
            "updated_at": time.Now(),
        })
    return err
}

func (s *sVMLifecycle) createVMRecord(ctx context.Context, req *CreateVMFromTemplateReq, vmid int) error {
    // 获取节点ID
    var nodeID uint64
    err := g.DB().Model("pve_nodes").Where("name", req.NodeName).Value("id", &nodeID)
    if err != nil {
        return err
    }

    data := g.Map{
        "vmid":        vmid,
        "node_id":     nodeID,
        "user_id":     req.UserID,
        "name":        req.Name,
        "description": req.Description,
        "cpu_cores":   req.Config.Cores,
        "memory_mb":   req.Config.Memory,
        "status":      "stopped",
    }

    _, err = g.DB().Model("pve_instances").Insert(data)
    return err
}

func (s *sVMLifecycle) logVMOperation(ctx context.Context, instanceID uint64, operation, description string) {
    // 记录虚拟机操作日志
    g.Log().Infof(ctx, "虚拟机[%d]操作: %s - %s", instanceID, operation, description)
}
```

## 📝 数据结构定义

```go
// 请求结构
type CreateVMFromTemplateReq struct {
    UserID       uint64    `json:"userId"`
    Name         string    `json:"name"`
    Description  string    `json:"description"`
    NodeName     string    `json:"nodeName"`
    TemplateNode string    `json:"templateNode"`
    TemplateVMID int       `json:"templateVMID"`
    Storage      string    `json:"storage"`
    Config       *VMConfig `json:"config"`
}

type VMConfig struct {
    Cores  int    `json:"cores"`
    Memory int    `json:"memory"`
    OSType string `json:"ostype"`
    Agent  bool   `json:"agent"`
}

type CreateVMResult struct {
    VMID     int    `json:"vmid"`
    Name     string `json:"name"`
    NodeName string `json:"nodeName"`
    Status   string `json:"status"`
}

type ResizeVMConfig struct {
    Cores   int `json:"cores"`
    Memory  int `json:"memory"`
    Balloon int `json:"balloon"`
}

type InstanceInfo struct {
    ID       uint64 `json:"id"`
    VMID     int    `json:"vmid"`
    Name     string `json:"name"`
    NodeName string `json:"nodeName"`
    Status   string `json:"status"`
}
```

## 🎯 使用示例

### 完整的虚拟机生命周期操作

```go
func ExampleVMLifecycleOperations() {
    ctx := context.Background()
    
    // 初始化服务
    vmService := VMLifecycle()
    vmService.SetClient(pveClient) // 假设已初始化PVE客户端
    
    // 1. 从模板创建虚拟机
    createReq := &CreateVMFromTemplateReq{
        UserID:       1001,
        Name:         "test-vm-001",
        Description:  "测试虚拟机",
        NodeName:     "node1",
        TemplateNode: "node1",
        TemplateVMID: 9000,
        Storage:      "local-lvm",
        Config: &VMConfig{
            Cores:  2,
            Memory: 2048,
            OSType: "l26",
            Agent:  true,
        },
    }
    
    result, err := vmService.CreateVMFromTemplate(ctx, createReq)
    if err != nil {
        log.Fatalf("创建虚拟机失败: %v", err)
    }
    
    instanceID := uint64(result.VMID) // 假设instance ID等于VMID
    
    // 2. 启动虚拟机
    err = vmService.StartVM(ctx, instanceID)
    if err != nil {
        log.Fatalf("启动虚拟机失败: %v", err)
    }
    
    // 3. 创建快照
    err = vmService.CreateSnapshot(ctx, instanceID, "initial-snapshot", "初始状态快照")
    if err != nil {
        log.Printf("创建快照失败: %v", err)
    }
    
    // 4. 调整虚拟机配置
    resizeConfig := &ResizeVMConfig{
        Cores:  4,
        Memory: 4096,
    }
    err = vmService.ResizeVM(ctx, instanceID, resizeConfig)
    if err != nil {
        log.Printf("调整虚拟机配置失败: %v", err)
    }
    
    // 5. 重启虚拟机
    err = vmService.RebootVM(ctx, instanceID, false)
    if err != nil {
        log.Printf("重启虚拟机失败: %v", err)
    }
    
    fmt.Printf("虚拟机生命周期操作完成: %s (VMID: %d)\n", result.Name, result.VMID)
}
```

## 📋 最佳实践

### 1. 状态检查和验证

```go
// 在每个操作前检查虚拟机状态
func (s *sVMLifecycle) validateVMState(ctx context.Context, instance *InstanceInfo, allowedStates []string) error {
    vmStatus, err := s.client.GetVMStatus(ctx, instance.NodeName, instance.VMID)
    if err != nil {
        return gerror.Wrap(err, "获取虚拟机状态失败")
    }
    
    for _, state := range allowedStates {
        if vmStatus.Status == state {
            return nil
        }
    }
    
    return gerror.Newf("虚拟机当前状态 (%s) 不允许此操作", vmStatus.Status)
}
```

### 2. 异步任务处理

```go
// 对于长时间操作，支持异步处理
func (s *sVMLifecycle) CreateVMFromTemplateAsync(ctx context.Context, req *CreateVMFromTemplateReq) (string, error) {
    taskID := generateTaskID()
    
    // 异步执行创建操作
    go func() {
        result, err := s.CreateVMFromTemplate(ctx, req)
        // 更新任务状态到数据库或缓存
        s.updateTaskStatus(taskID, result, err)
    }()
    
    return taskID, nil
}
```

### 3. 操作锁定机制

```go
// 防止并发操作同一虚拟机
func (s *sVMLifecycle) lockVM(ctx context.Context, instanceID uint64) error {
    // 使用Redis或数据库实现分布式锁
    lockKey := fmt.Sprintf("vm_lock_%d", instanceID)
    // 实现锁定逻辑...
    return nil
}
```

## 总结

虚拟机生命周期管理服务提供了完整的虚拟机操作功能：

1. **虚拟机创建**：支持从模板创建和自定义配置
2. **电源管理**：启动、停止、重启操作
3. **快照管理**：创建、恢复、删除快照
4. **迁移功能**：在线和离线迁移
5. **配置调整**：动态调整CPU和内存
6. **状态管理**：完整的状态跟踪和验证

通过这个服务架构，可以构建稳定、高效的虚拟机管理系统。