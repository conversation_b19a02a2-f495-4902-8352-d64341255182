// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalSysGenCodesDao is internal type for wrapping internal DAO implements.
type internalSysGenCodesDao = *internal.SysGenCodesDao

// sysGenCodesDao is the data access object for table hg_sys_gen_codes.
// You can define custom methods on it to extend its functionality as you wish.
type sysGenCodesDao struct {
	internalSysGenCodesDao
}

var (
	// SysGenCodes is globally public accessible object for table hg_sys_gen_codes operations.
	SysGenCodes = sysGenCodesDao{
		internal.NewSysGenCodesDao(),
	}
)

// Fill with you ideas below.
