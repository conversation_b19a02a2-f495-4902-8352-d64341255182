// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"

	"github.com/gogf/gf/v2/util/gconv"
	"hotgo/addons/pve/api/admin"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

var (
	Storage = cPveStorage{}
)

type cPveStorage struct{}

// List 获取存储列表
func (c *cPveStorage) List(ctx context.Context, req *admin.StorageListReq) (res *admin.StorageListRes, err error) {
	list, err := service.PveStorage().List(ctx, &input.StorageListInp{
		NodeID:  int(req.NodeID),
		Type:    req.Type,
		Keyword: req.Keyword,
		PageReq: req.PageReq,
	})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageListRes{
		List:    list.List,
		PageRes: list.PageRes,
	}

	return
}

// View 查看存储详情
func (c *cPveStorage) View(ctx context.Context, req *admin.StorageViewReq) (res *admin.StorageViewRes, err error) {
	data, err := service.PveStorage().View(ctx, &input.StorageViewInp{
		ID: gconv.String(req.ID),
	})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageViewRes{
		StorageViewOut: data,
	}
	return
}

// Create 创建存储
func (c *cPveStorage) Create(ctx context.Context, req *admin.StorageCreateReq) (res *admin.StorageCreateRes, err error) {
	out, err := service.PveStorage().Create(ctx, &input.StorageCreateInp{
		Storage:  req.Storage,
		Type:     req.Type,
		Content:  req.Content,
		Path:     req.Path,
		Server:   req.Server,
		Username: req.Username,
		Password: req.Password,
	})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageCreateRes{
		StorageCreateOut: out,
	}
	return
}

// Edit 编辑存储
func (c *cPveStorage) Edit(ctx context.Context, req *admin.StorageEditReq) (res *admin.StorageEditRes, err error) {
	err = service.PveStorage().Edit(ctx, &input.StorageEditInp{
		ID:      gconv.String(req.ID),
		Content: req.Content,
		Enabled: true, // 默认启用
	})
	if err != nil {
		return nil, err
	}

	return
}

// Delete 删除存储
func (c *cPveStorage) Delete(ctx context.Context, req *admin.StorageDeleteReq) (res *admin.StorageDeleteRes, err error) {
	err = service.PveStorage().Delete(ctx, &input.StorageDeleteInp{
		ID: gconv.String(req.ID),
	})
	if err != nil {
		return nil, err
	}

	return
}

// GetContent 获取存储内容
func (c *cPveStorage) GetContent(ctx context.Context, req *admin.StorageGetContentReq) (res *admin.StorageGetContentRes, err error) {
	// 调用存储服务
	data, err := service.PveStorage().GetContent(ctx, &input.StorageGetContentInp{
		NodeID:  int(req.NodeID),
		Storage: req.Storage,
		Path:    req.Path,
	})
	if err != nil {
		return nil, err
	}

	// 转换数据格式
	var contents []*admin.StorageContent
	for _, item := range data.Contents {
		content := &admin.StorageContent{
			VolID:    item.VolID,
			Name:     item.Name,
			Size:     item.Size,
			Format:   item.Format,
			Type:     item.Type,
			Modified: item.Modified,
		}
		contents = append(contents, content)
	}

	res = &admin.StorageGetContentRes{
		Contents: contents,
	}

	return
}

// Sync 同步存储信息
func (c *cPveStorage) Sync(ctx context.Context, req *admin.StorageSyncReq) (res *admin.StorageSyncRes, err error) {
	out, err := service.PveStorage().Sync(ctx, &input.StorageListInp{})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageSyncRes{
		StorageSyncOut: out,
	}
	return
}

// GetBackupList 获取备份列表
func (c *cPveStorage) GetBackupList(ctx context.Context, req *admin.StorageBackupListReq) (res *admin.StorageBackupListRes, err error) {
	list, err := service.PveStorage().GetBackupList(ctx, &input.BackupListInp{
		NodeID:  int(req.NodeID),
		Storage: req.Storage,
		VMID:    int(req.InstanceID), // Map InstanceID to VMID
	})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageBackupListRes{
		StorageBackupListOut: list,
	}
	return
}

// CreateBackup 创建备份
func (c *cPveStorage) CreateBackup(ctx context.Context, req *admin.StorageCreateBackupReq) (res *admin.StorageCreateBackupRes, err error) {
	out, err := service.PveStorage().CreateBackup(ctx, &input.BackupCreateInp{
		VMID:     int(req.InstanceID), // Map InstanceID to VMID
		Storage:  req.Storage,
		Mode:     req.Mode,
		Compress: req.Compress,
		Notes:    req.Notes,
	})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageCreateBackupRes{
		StorageCreateBackupOut: out,
	}
	return
}

// RestoreBackup 恢复备份
func (c *cPveStorage) RestoreBackup(ctx context.Context, req *admin.StorageRestoreBackupReq) (res *admin.StorageRestoreBackupRes, err error) {
	out, err := service.PveStorage().RestoreBackup(ctx, &input.BackupRestoreInp{
		VolID:   req.BackupFile, // Map BackupFile to VolID
		VMID:    req.NewVMID,    // Map NewVMID to VMID
		Storage: req.Storage,
	})
	if err != nil {
		return nil, err
	}

	res = &admin.StorageRestoreBackupRes{
		StorageRestoreBackupOut: out,
	}
	return
}

// DeleteBackup 删除备份
func (c *cPveStorage) DeleteBackup(ctx context.Context, req *admin.StorageDeleteBackupReq) (res *admin.StorageDeleteBackupRes, err error) {
	err = service.PveStorage().DeleteBackup(ctx, &input.BackupDeleteInp{
		ID:    int(req.NodeID), // Map NodeID to ID for backup deletion context
		VolID: req.BackupFile,  // Map BackupFile to VolID
	})
	if err != nil {
		return nil, err
	}

	return
}