// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
)

var (
	HA = cHA{}
)

type cHA struct{}

// GetResources 获取高可用资源
func (c *cHA) GetResources(ctx context.Context, req *admin.HAResourcesReq) (res *admin.HAResourcesRes, err error) {
	// TODO: 实现获取高可用资源逻辑
	return &admin.HAResourcesRes{
		List: []*admin.HAResourceViewModel{},
	}, nil
}

// CreateResource 创建高可用资源
func (c *cHA) CreateResource(ctx context.Context, req *admin.HACreateResourceReq) (res *admin.HACreateResourceRes, err error) {
	// TODO: 实现创建高可用资源逻辑
	return &admin.HACreateResourceRes{
		ID: 1,
	}, nil
}

// EditResource 编辑高可用资源
func (c *cHA) EditResource(ctx context.Context, req *admin.HAEditResourceReq) (res *admin.HAEditResourceRes, err error) {
	// TODO: 实现编辑高可用资源逻辑
	return &admin.HAEditResourceRes{}, nil
}

// DeleteResource 删除高可用资源
func (c *cHA) DeleteResource(ctx context.Context, req *admin.HADeleteResourceReq) (res *admin.HADeleteResourceRes, err error) {
	// TODO: 实现删除高可用资源逻辑
	return &admin.HADeleteResourceRes{}, nil
}

// GetGroups 获取高可用组
func (c *cHA) GetGroups(ctx context.Context, req *admin.HAGroupsReq) (res *admin.HAGroupsRes, err error) {
	// TODO: 实现获取高可用组逻辑
	return &admin.HAGroupsRes{
		List: []*admin.HAGroupViewModel{},
	}, nil
}

// CreateGroup 创建高可用组
func (c *cHA) CreateGroup(ctx context.Context, req *admin.HACreateGroupReq) (res *admin.HACreateGroupRes, err error) {
	// TODO: 实现创建高可用组逻辑
	return &admin.HACreateGroupRes{
		ID: 1,
	}, nil
}

// EditGroup 编辑高可用组
func (c *cHA) EditGroup(ctx context.Context, req *admin.HAEditGroupReq) (res *admin.HAEditGroupRes, err error) {
	// TODO: 实现编辑高可用组逻辑
	return &admin.HAEditGroupRes{}, nil
}

// DeleteGroup 删除高可用组
func (c *cHA) DeleteGroup(ctx context.Context, req *admin.HADeleteGroupReq) (res *admin.HADeleteGroupRes, err error) {
	// TODO: 实现删除高可用组逻辑
	return &admin.HADeleteGroupRes{}, nil
}
