// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/internal/model/input/form"
)

// ContainerListReq 获取容器列表请求
type ContainerListReq struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// ContainerListRes 获取容器列表响应
type ContainerListRes struct {
	List []*ContainerViewModel `json:"list" dc:"容器列表"`
	form.PageRes
}

// ContainerViewModel 容器视图模型
type ContainerViewModel struct {
	ID          uint64 `json:"id" dc:"容器ID"`
	NodeID      int    `json:"nodeId" dc:"节点ID"`
	NodeName    string `json:"nodeName" dc:"节点名称"`
	Name        string `json:"name" dc:"容器名称"`
	Status      string `json:"status" dc:"容器状态"`
	CPU         int    `json:"cpu" dc:"CPU核心数"`
	Memory      int    `json:"memory" dc:"内存大小(MB)"`
	Disk        int    `json:"disk" dc:"磁盘大小(GB)"`
	CreatedAt   string `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string `json:"updatedAt" dc:"更新时间"`
}

// ContainerViewReq 查看容器详情请求
type ContainerViewReq struct {
	ID uint64 `json:"id" v:"required#容器ID不能为空" dc:"容器ID"`
}

// ContainerViewRes 查看容器详情响应
type ContainerViewRes struct {
	ContainerViewModel
}

// ContainerCreateReq 创建容器请求
type ContainerCreateReq struct {
	NodeID      int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Name        string `json:"name" v:"required#容器名称不能为空" dc:"容器名称"`
	TemplateID  string `json:"templateId" v:"required#模板ID不能为空" dc:"模板ID"`
	CPU         int    `json:"cpu" v:"required#CPU核心数不能为空" dc:"CPU核心数"`
	Memory      int    `json:"memory" v:"required#内存大小不能为空" dc:"内存大小(MB)"`
	Disk        int    `json:"disk" v:"required#磁盘大小不能为空" dc:"磁盘大小(GB)"`
	Description string `json:"description" dc:"容器描述"`
}

// ContainerCreateRes 创建容器响应
type ContainerCreateRes struct {
	ID     uint64 `json:"id" dc:"容器ID"`
	TaskID string `json:"taskId" dc:"任务ID"`
}

// ContainerEditReq 编辑容器请求
type ContainerEditReq struct {
	ID          uint64 `json:"id" v:"required#容器ID不能为空" dc:"容器ID"`
	Name        string `json:"name" dc:"容器名称"`
	CPU         int    `json:"cpu" dc:"CPU核心数"`
	Memory      int    `json:"memory" dc:"内存大小(MB)"`
	Disk        int    `json:"disk" dc:"磁盘大小(GB)"`
	Description string `json:"description" dc:"容器描述"`
}

// ContainerEditRes 编辑容器响应
type ContainerEditRes struct{}

// ContainerDeleteReq 删除容器请求
type ContainerDeleteReq struct {
	ID    uint64 `json:"id" v:"required#容器ID不能为空" dc:"容器ID"`
	Force bool   `json:"force" dc:"是否强制删除"`
}

// ContainerDeleteRes 删除容器响应
type ContainerDeleteRes struct {
	TaskID string `json:"taskId" dc:"任务ID"`
}

// ContainerActionReq 容器操作请求
type ContainerActionReq struct {
	ID     uint64 `json:"id" v:"required#容器ID不能为空" dc:"容器ID"`
	Action string `json:"action" v:"required#操作类型不能为空" dc:"操作类型(start/stop/restart/pause/resume)"`
}

// ContainerActionRes 容器操作响应
type ContainerActionRes struct {
	TaskID string `json:"taskId" dc:"任务ID"`
}

// ContainerConsoleReq 容器控制台请求
type ContainerConsoleReq struct {
	ID uint64 `json:"id" v:"required#容器ID不能为空" dc:"容器ID"`
}

// ContainerConsoleRes 容器控制台响应
type ContainerConsoleRes struct {
	ConsoleURL string `json:"consoleUrl" dc:"控制台访问URL"`
	Token      string `json:"token" dc:"访问令牌"`
}

// ContainerMonitorReq 容器监控请求
type ContainerMonitorReq struct {
	ID     uint64 `json:"id" v:"required#容器ID不能为空" dc:"容器ID"`
	Period string `json:"period" dc:"监控周期(1h/24h/7d/30d)"`
}

// ContainerMonitorRes 容器监控响应
type ContainerMonitorRes struct {
	CPUData     []*ContainerMonitorPoint `json:"cpuData" dc:"CPU使用率数据"`
	MemoryData  []*ContainerMonitorPoint `json:"memoryData" dc:"内存使用率数据"`
	DiskData    []*ContainerMonitorPoint `json:"diskData" dc:"磁盘使用率数据"`
	NetworkData []*ContainerMonitorPoint `json:"networkData" dc:"网络流量数据"`
}

// ContainerMonitorPoint 容器监控数据点
type ContainerMonitorPoint struct {
	Timestamp int64   `json:"timestamp" dc:"时间戳"`
	Value     float64 `json:"value" dc:"数值"`
}
