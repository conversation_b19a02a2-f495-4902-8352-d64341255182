// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== 网络管理 API ==========

// ApplyNetworkConfig 应用网络配置
func (c *Client) ApplyNetworkConfig(ctx context.Context, node string) error {
	path := fmt.Sprintf("/nodes/%s/network", node)
	_, err := c.request(ctx, "PUT", path, nil)
	return err
}

// ReloadNetworkConfig 重新加载网络配置
func (c *Client) ReloadNetworkConfig(ctx context.Context, node string) error {
	path := fmt.Sprintf("/nodes/%s/network", node)
	_, err := c.request(ctx, "POST", path, nil)
	return err
}

// CreateNetwork 创建网络配置
func (c *Client) CreateNetwork(ctx context.Context, node, networkName string, config map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/network", node)

	params := map[string]interface{}{
		"iface": networkName,
	}

	// 合并配置参数
	for k, v := range config {
		params[k] = v
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// UpdateNetwork 更新网络配置
func (c *Client) UpdateNetwork(ctx context.Context, node, networkName string, config map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/network/%s", node, networkName)
	_, err := c.request(ctx, "PUT", path, config)
	return err
}

// DeleteNetwork 删除网络配置
func (c *Client) DeleteNetwork(ctx context.Context, node, networkName string) error {
	path := fmt.Sprintf("/nodes/%s/network/%s", node, networkName)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 防火墙管理 API ==========

// GetClusterFirewallGroups 获取集群防火墙安全组
func (c *Client) GetClusterFirewallGroups(ctx context.Context) ([]map[string]interface{}, error) {
	body, err := c.request(ctx, "GET", "/cluster/firewall/groups", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var groups []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &groups); err != nil {
		return nil, err
	}

	return groups, nil
}

// CreateFirewallGroup 创建防火墙安全组
func (c *Client) CreateFirewallGroup(ctx context.Context, group, comment string) error {
	params := map[string]interface{}{
		"group": group,
	}
	if comment != "" {
		params["comment"] = comment
	}

	_, err := c.request(ctx, "POST", "/cluster/firewall/groups", params)
	return err
}

// DeleteFirewallGroup 删除防火墙安全组
func (c *Client) DeleteFirewallGroup(ctx context.Context, group string) error {
	path := fmt.Sprintf("/cluster/firewall/groups/%s", group)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetFirewallGroupRules 获取防火墙安全组规则
func (c *Client) GetFirewallGroupRules(ctx context.Context, group string) ([]*FirewallRule, error) {
	path := fmt.Sprintf("/cluster/firewall/groups/%s", group)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var rules []*FirewallRule
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &rules); err != nil {
		return nil, err
	}

	return rules, nil
}

// CreateFirewallGroupRule 创建防火墙安全组规则
func (c *Client) CreateFirewallGroupRule(ctx context.Context, group string, rule *FirewallRule) error {
	path := fmt.Sprintf("/cluster/firewall/groups/%s", group)

	params := map[string]interface{}{
		"action": rule.Action,
		"type":   rule.Type,
	}

	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// GetClusterFirewallRules 获取集群防火墙规则
func (c *Client) GetClusterFirewallRules(ctx context.Context) ([]*FirewallRule, error) {
	body, err := c.request(ctx, "GET", "/cluster/firewall/rules", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var rules []*FirewallRule
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &rules); err != nil {
		return nil, err
	}

	return rules, nil
}

// CreateClusterFirewallRule 创建集群防火墙规则
func (c *Client) CreateClusterFirewallRule(ctx context.Context, rule *FirewallRule) error {
	params := map[string]interface{}{
		"action": rule.Action,
		"type":   rule.Type,
	}

	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "POST", "/cluster/firewall/rules", params)
	return err
}

// UpdateClusterFirewallRule 更新集群防火墙规则
func (c *Client) UpdateClusterFirewallRule(ctx context.Context, pos int, rule *FirewallRule) error {
	path := fmt.Sprintf("/cluster/firewall/rules/%d", pos)

	params := map[string]interface{}{}

	if rule.Action != "" {
		params["action"] = rule.Action
	}
	if rule.Type != "" {
		params["type"] = rule.Type
	}
	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteClusterFirewallRule 删除集群防火墙规则
func (c *Client) DeleteClusterFirewallRule(ctx context.Context, pos int) error {
	path := fmt.Sprintf("/cluster/firewall/rules/%d", pos)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetNodeFirewallRules 获取节点防火墙规则
func (c *Client) GetNodeFirewallRules(ctx context.Context, node string) ([]*FirewallRule, error) {
	path := fmt.Sprintf("/nodes/%s/firewall/rules", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var rules []*FirewallRule
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &rules); err != nil {
		return nil, err
	}

	return rules, nil
}

// CreateNodeFirewallRule 创建节点防火墙规则
func (c *Client) CreateNodeFirewallRule(ctx context.Context, node string, rule *FirewallRule) error {
	path := fmt.Sprintf("/nodes/%s/firewall/rules", node)

	params := map[string]interface{}{
		"action": rule.Action,
		"type":   rule.Type,
	}

	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// GetFirewallOptions 获取防火墙选项
func (c *Client) GetFirewallOptions(ctx context.Context) (map[string]interface{}, error) {
	body, err := c.request(ctx, "GET", "/cluster/firewall/options", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	options, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的防火墙选项数据")
	}

	return options, nil
}

// UpdateFirewallOptions 更新防火墙选项
func (c *Client) UpdateFirewallOptions(ctx context.Context, options map[string]interface{}) error {
	_, err := c.request(ctx, "PUT", "/cluster/firewall/options", options)
	return err
}

// GetFirewallAliases 获取防火墙别名
func (c *Client) GetFirewallAliases(ctx context.Context) ([]map[string]interface{}, error) {
	body, err := c.request(ctx, "GET", "/cluster/firewall/aliases", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var aliases []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &aliases); err != nil {
		return nil, err
	}

	return aliases, nil
}

// CreateFirewallAlias 创建防火墙别名
func (c *Client) CreateFirewallAlias(ctx context.Context, name, cidr, comment string) error {
	params := map[string]interface{}{
		"name": name,
		"cidr": cidr,
	}
	if comment != "" {
		params["comment"] = comment
	}

	_, err := c.request(ctx, "POST", "/cluster/firewall/aliases", params)
	return err
}

// UpdateFirewallAlias 更新防火墙别名
func (c *Client) UpdateFirewallAlias(ctx context.Context, name, cidr, comment string) error {
	path := fmt.Sprintf("/cluster/firewall/aliases/%s", name)

	params := map[string]interface{}{
		"cidr": cidr,
	}
	if comment != "" {
		params["comment"] = comment
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteFirewallAlias 删除防火墙别名
func (c *Client) DeleteFirewallAlias(ctx context.Context, name string) error {
	path := fmt.Sprintf("/cluster/firewall/aliases/%s", name)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}
