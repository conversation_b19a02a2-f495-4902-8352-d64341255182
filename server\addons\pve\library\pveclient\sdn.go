// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== 软件定义网络(SDN)管理 API ==========

// VNet 虚拟网络定义
// SDN中的虚拟网络，可以跨越多个物理节点
type VNet struct {
	VNet    string `json:"vnet"`    // 虚拟网络名称，集群内唯一标识
	Type    string `json:"type"`    // 网络类型：vnet
	Zone    string `json:"zone"`    // 所属区域名称
	Tag     int    `json:"tag"`     // VLAN标签ID，用于网络隔离
	Alias   string `json:"alias,omitempty"`   // 网络别名，便于识别
	Vlanaware int  `json:"vlanaware,omitempty"` // 是否启用VLAN感知模式
}

// Subnet 子网配置
// 虚拟网络中的子网定义，包含IP地址分配和路由信息
type Subnet struct {
	Subnet    string `json:"subnet"`     // 子网标识，格式：vnet-subnet
	Type      string `json:"type"`       // 类型：subnet
	VNet      string `json:"vnet"`       // 所属虚拟网络名称
	Gateway   string `json:"gateway,omitempty"`   // 网关IP地址
	Snat      int    `json:"snat,omitempty"`      // 是否启用源NAT转换
	DhcpRange string `json:"dhcp-range,omitempty"` // DHCP地址池范围
	DnsDomain string `json:"dnsdomain,omitempty"`  // DNS域名
	DnsServer string `json:"dnsserver,omitempty"`  // DNS服务器地址
}

// Zone SDN区域配置
// 定义不同的网络区域类型和配置参数
type Zone struct {
	Zone        string                 `json:"zone"`         // 区域名称，集群内唯一
	Type        string                 `json:"type"`         // 区域类型：simple/vlan/qinq/vxlan/evpn
	Bridge      string                 `json:"bridge,omitempty"`      // 关联的Linux桥接设备
	Nodes       string                 `json:"nodes,omitempty"`       // 参与的节点列表，逗号分隔
	Tag         int                    `json:"tag,omitempty"`         // VLAN标签
	VlanProtocol string                `json:"vlan-protocol,omitempty"` // VLAN协议版本
	Mtu         int                    `json:"mtu,omitempty"`         // 最大传输单元大小
	Options     map[string]interface{} `json:"-"`            // 其他特定类型的配置选项
}

// Controller SDN控制器配置
// 管理SDN网络的控制器，如BGP、EVPN等
type Controller struct {
	Controller string                 `json:"controller"`   // 控制器名称，集群内唯一
	Type       string                 `json:"type"`         // 控制器类型：bgp/evpn/faucet
	ASN        int                    `json:"asn,omitempty"`        // BGP自治系统号
	Peers      string                 `json:"peers,omitempty"`      // BGP对等体列表
	Ebgp       int                    `json:"ebgp,omitempty"`       // 是否使用外部BGP
	EbgpMultihop int                  `json:"ebgp-multihop,omitempty"` // 外部BGP多跳数
	Options    map[string]interface{} `json:"-"`            // 其他控制器特定配置
}

// SDNStatus SDN整体状态信息
// 包含SDN配置的应用状态和运行状态
type SDNStatus struct {
	Status string                 `json:"status"`  // 总体状态：OK/ERROR
	Errors []string               `json:"errors,omitempty"`  // 错误信息列表
	Config map[string]interface{} `json:"config,omitempty"`  // 当前配置信息
}

// GetSDNZones 获取SDN区域列表
// 功能说明：
// - 获取集群中配置的所有SDN区域信息
// - 包含不同类型区域的详细配置参数
// - 支持simple、vlan、qinq、vxlan、evpn等类型
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   []*Zone: SDN区域配置列表
//   error: 获取失败时返回错误信息
// 使用示例：
//   zones, err := client.GetSDNZones(ctx)
func (c *Client) GetSDNZones(ctx context.Context) ([]*Zone, error) {
	body, err := c.request(ctx, "GET", "/cluster/sdn/zones", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var zones []*Zone
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &zones); err != nil {
		return nil, err
	}

	return zones, nil
}

// CreateSDNZone 创建SDN区域
// 功能说明：
// - 创建新的SDN网络区域
// - 支持多种区域类型：simple（简单桥接）、vlan（VLAN隔离）、vxlan（VXLAN隧道）等
// - 区域创建后需要应用配置才能生效
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   zone: SDN区域配置信息
// 返回：
//   error: 创建失败时返回错误信息
// 注意事项：
//   - 区域名称在集群内必须唯一
//   - 不同类型区域需要不同的配置参数
//   - 创建后需要调用ApplySDNConfig应用配置
func (c *Client) CreateSDNZone(ctx context.Context, zone *Zone) error {
	params := map[string]interface{}{
		"zone": zone.Zone,
		"type": zone.Type,
	}

	if zone.Bridge != "" {
		params["bridge"] = zone.Bridge
	}
	if zone.Nodes != "" {
		params["nodes"] = zone.Nodes
	}
	if zone.Tag > 0 {
		params["tag"] = zone.Tag
	}
	if zone.VlanProtocol != "" {
		params["vlan-protocol"] = zone.VlanProtocol
	}
	if zone.Mtu > 0 {
		params["mtu"] = zone.Mtu
	}

	// 添加其他特定类型的配置选项
	for k, v := range zone.Options {
		params[k] = v
	}

	_, err := c.request(ctx, "POST", "/cluster/sdn/zones", params)
	return err
}

// UpdateSDNZone 更新SDN区域配置
// 功能说明：
// - 修改现有SDN区域的配置参数
// - 可以更新网络类型、节点分配、VLAN标签等
// - 配置更新后需要重新应用才能生效
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   zoneName: 要更新的区域名称
//   zone: 新的区域配置信息
// 返回：
//   error: 更新失败时返回错误信息
func (c *Client) UpdateSDNZone(ctx context.Context, zoneName string, zone *Zone) error {
	path := fmt.Sprintf("/cluster/sdn/zones/%s", zoneName)

	params := map[string]interface{}{}

	if zone.Type != "" {
		params["type"] = zone.Type
	}
	if zone.Bridge != "" {
		params["bridge"] = zone.Bridge
	}
	if zone.Nodes != "" {
		params["nodes"] = zone.Nodes
	}
	if zone.Tag > 0 {
		params["tag"] = zone.Tag
	}
	if zone.VlanProtocol != "" {
		params["vlan-protocol"] = zone.VlanProtocol
	}
	if zone.Mtu > 0 {
		params["mtu"] = zone.Mtu
	}

	// 添加其他配置选项
	for k, v := range zone.Options {
		params[k] = v
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteSDNZone 删除SDN区域
// 功能说明：
// - 删除指定的SDN网络区域
// - 删除前需要确保没有虚拟网络使用该区域
// - 删除后需要重新应用配置
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   zoneName: 要删除的区域名称
// 返回：
//   error: 删除失败时返回错误信息
// 注意事项：
//   - 删除前需要先删除该区域下的所有虚拟网络
//   - 删除操作不可逆，请谨慎操作
func (c *Client) DeleteSDNZone(ctx context.Context, zoneName string) error {
	path := fmt.Sprintf("/cluster/sdn/zones/%s", zoneName)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetSDNVNets 获取虚拟网络列表
// 功能说明：
// - 获取集群中配置的所有虚拟网络信息
// - 虚拟网络是SDN中的逻辑网络单元
// - 可以跨越多个物理节点提供统一的网络服务
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   []*VNet: 虚拟网络配置列表
//   error: 获取失败时返回错误信息
func (c *Client) GetSDNVNets(ctx context.Context) ([]*VNet, error) {
	body, err := c.request(ctx, "GET", "/cluster/sdn/vnets", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var vnets []*VNet
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &vnets); err != nil {
		return nil, err
	}

	return vnets, nil
}

// CreateSDNVNet 创建虚拟网络
// 功能说明：
// - 在指定SDN区域中创建新的虚拟网络
// - 虚拟网络提供跨节点的统一网络环境
// - 支持VLAN标签进行网络隔离
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   vnet: 虚拟网络配置信息
// 返回：
//   error: 创建失败时返回错误信息
func (c *Client) CreateSDNVNet(ctx context.Context, vnet *VNet) error {
	params := map[string]interface{}{
		"vnet": vnet.VNet,
		"zone": vnet.Zone,
	}

	if vnet.Tag > 0 {
		params["tag"] = vnet.Tag
	}
	if vnet.Alias != "" {
		params["alias"] = vnet.Alias
	}
	if vnet.Vlanaware > 0 {
		params["vlanaware"] = vnet.Vlanaware
	}

	_, err := c.request(ctx, "POST", "/cluster/sdn/vnets", params)
	return err
}

// DeleteSDNVNet 删除虚拟网络
// 功能说明：
// - 删除指定的虚拟网络
// - 删除前需要确保没有虚拟机使用该网络
// - 同时会删除相关的子网配置
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   vnetName: 要删除的虚拟网络名称
// 返回：
//   error: 删除失败时返回错误信息
func (c *Client) DeleteSDNVNet(ctx context.Context, vnetName string) error {
	path := fmt.Sprintf("/cluster/sdn/vnets/%s", vnetName)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetSDNSubnets 获取子网列表
// 功能说明：
// - 获取指定虚拟网络中的子网配置
// - 子网定义了IP地址分配和路由规则
// - 包含DHCP、DNS、网关等网络服务配置
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   vnetName: 虚拟网络名称，为空则获取所有子网
// 返回：
//   []*Subnet: 子网配置列表
//   error: 获取失败时返回错误信息
func (c *Client) GetSDNSubnets(ctx context.Context, vnetName string) ([]*Subnet, error) {
	var path string
	if vnetName != "" {
		path = fmt.Sprintf("/cluster/sdn/vnets/%s/subnets", vnetName)
	} else {
		path = "/cluster/sdn/subnets"
	}

	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var subnets []*Subnet
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &subnets); err != nil {
		return nil, err
	}

	return subnets, nil
}

// CreateSDNSubnet 创建子网
// 功能说明：
// - 在虚拟网络中创建新的子网
// - 配置IP地址段、网关、DHCP服务等
// - 支持IPv4和IPv6双栈网络
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   vnetName: 所属虚拟网络名称
//   subnet: 子网配置信息
// 返回：
//   error: 创建失败时返回错误信息
func (c *Client) CreateSDNSubnet(ctx context.Context, vnetName string, subnet *Subnet) error {
	path := fmt.Sprintf("/cluster/sdn/vnets/%s/subnets", vnetName)

	params := map[string]interface{}{
		"subnet": subnet.Subnet,
		"type":   "subnet",
	}

	if subnet.Gateway != "" {
		params["gateway"] = subnet.Gateway
	}
	if subnet.Snat > 0 {
		params["snat"] = subnet.Snat
	}
	if subnet.DhcpRange != "" {
		params["dhcp-range"] = subnet.DhcpRange
	}
	if subnet.DnsDomain != "" {
		params["dnsdomain"] = subnet.DnsDomain
	}
	if subnet.DnsServer != "" {
		params["dnsserver"] = subnet.DnsServer
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// DeleteSDNSubnet 删除子网
// 功能说明：
// - 删除虚拟网络中的指定子网
// - 删除前需要确保没有虚拟机使用该子网的IP地址
// - 相关的DHCP租约信息也会被清理
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   vnetName: 所属虚拟网络名称
//   subnetName: 要删除的子网名称
// 返回：
//   error: 删除失败时返回错误信息
func (c *Client) DeleteSDNSubnet(ctx context.Context, vnetName, subnetName string) error {
	path := fmt.Sprintf("/cluster/sdn/vnets/%s/subnets/%s", vnetName, subnetName)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetSDNControllers 获取SDN控制器列表
// 功能说明：
// - 获取集群中配置的SDN控制器信息
// - 控制器负责管理复杂的网络拓扑和路由
// - 支持BGP、EVPN、Faucet等不同类型的控制器
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   []*Controller: SDN控制器配置列表
//   error: 获取失败时返回错误信息
func (c *Client) GetSDNControllers(ctx context.Context) ([]*Controller, error) {
	body, err := c.request(ctx, "GET", "/cluster/sdn/controllers", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var controllers []*Controller
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &controllers); err != nil {
		return nil, err
	}

	return controllers, nil
}

// CreateSDNController 创建SDN控制器
// 功能说明：
// - 创建新的SDN网络控制器
// - 控制器类型包括：bgp（边界网关协议）、evpn（以太网VPN）、faucet（OpenFlow控制器）
// - 配置网络路由和策略分发
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   controller: SDN控制器配置信息
// 返回：
//   error: 创建失败时返回错误信息
func (c *Client) CreateSDNController(ctx context.Context, controller *Controller) error {
	params := map[string]interface{}{
		"controller": controller.Controller,
		"type":       controller.Type,
	}

	if controller.ASN > 0 {
		params["asn"] = controller.ASN
	}
	if controller.Peers != "" {
		params["peers"] = controller.Peers
	}
	if controller.Ebgp > 0 {
		params["ebgp"] = controller.Ebgp
	}
	if controller.EbgpMultihop > 0 {
		params["ebgp-multihop"] = controller.EbgpMultihop
	}

	// 添加其他控制器特定配置
	for k, v := range controller.Options {
		params[k] = v
	}

	_, err := c.request(ctx, "POST", "/cluster/sdn/controllers", params)
	return err
}

// DeleteSDNController 删除SDN控制器
// 功能说明：
// - 删除指定的SDN控制器
// - 删除前需要确保没有区域依赖该控制器
// - 删除后相关的路由规则会失效
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
//   controllerName: 要删除的控制器名称
// 返回：
//   error: 删除失败时返回错误信息
func (c *Client) DeleteSDNController(ctx context.Context, controllerName string) error {
	path := fmt.Sprintf("/cluster/sdn/controllers/%s", controllerName)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ApplySDNConfig 应用SDN配置
// 功能说明：
// - 将当前的SDN配置应用到所有节点
// - 同步区域、虚拟网络、子网等配置到物理网络设备
// - 重启相关的网络服务使配置生效
// - 这是一个重要的操作，所有SDN配置变更后都需要执行
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   error: 应用失败时返回错误信息
// 注意事项：
//   - 配置应用过程中可能会短暂中断网络服务
//   - 建议在维护窗口期间执行
//   - 失败时可能需要手动修复网络配置
func (c *Client) ApplySDNConfig(ctx context.Context) error {
	_, err := c.request(ctx, "PUT", "/cluster/sdn", nil)
	return err
}

// ReloadSDNConfig 重新加载SDN配置
// 功能说明：
// - 从配置文件重新加载SDN配置
// - 不会重新应用配置，仅刷新内存中的配置信息
// - 用于配置文件手动修改后的同步
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   error: 重新加载失败时返回错误信息
func (c *Client) ReloadSDNConfig(ctx context.Context) error {
	_, err := c.request(ctx, "POST", "/cluster/sdn", nil)
	return err
}

// GetSDNStatus 获取SDN运行状态
// 功能说明：
// - 获取SDN系统的整体运行状态
// - 检查配置是否正确应用
// - 显示配置错误和警告信息
// 参数：
//   ctx: 上下文，用于请求取消和超时控制
// 返回：
//   *SDNStatus: SDN状态信息，包含错误列表和配置状态
//   error: 获取失败时返回错误信息
func (c *Client) GetSDNStatus(ctx context.Context) (*SDNStatus, error) {
	body, err := c.request(ctx, "GET", "/cluster/sdn/status", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var status SDNStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, err
	}

	return &status, nil
}