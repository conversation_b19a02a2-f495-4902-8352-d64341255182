// Package common
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package common

import (
	"context"
	"hotgo/api/admin/common"
)

var Console = cConsole{}

type cConsole struct{}

// Stat 综合数据统计
func (c *cConsole) Stat(_ context.Context, _ *common.ConsoleStatReq) (res *common.ConsoleStatRes, err error) {
	res = new(common.ConsoleStatRes)

	// 此处均为模拟数据，可以根据实际业务情况替换成真实数据

	res.Visits.DayVisits = 12010
	res.Visits.Rise = 13501
	res.Visits.Decline = 10502
	res.Visits.Amount = 10403

	res.Saleroom.WeekSaleroom = 20501
	res.Saleroom.Amount = 21002
	res.Saleroom.Degree = 83.66

	res.OrderLarge.WeekLarge = 39901
	res.OrderLarge.Rise = 31012
	res.OrderLarge.Decline = 30603
	res.OrderLarge.Amount = 36084

	res.Volume.WeekLarge = 40021
	res.Volume.Rise = 40202
	res.Volume.Decline = 45003
	res.Volume.Amount = 49004
	return
}
