{"permissions": {"allow": ["WebFetch(domain:pve.proxmox.com)", "Bash(git checkout:*)", "Bash(git add:*)", "*", "Bash(git commit:*)", "Bash(git reset:*)", "Bash(go mod:*)", "Bash(go build:*)", "Bash(npm run build:*)", "Bash(npm run type:check:*)", "Bash(grep:*)", "<PERSON><PERSON>(del:*)", "Bash(find:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(mysql:*)", "Bash(pnpm list:*)", "WebSearch", "WebFetch(domain:xicons.org)", "<PERSON><PERSON>(make:*)", "mcp__ide__getDiagnostics", "Bash(pnpm run:*)", "Ba<PERSON>(go vet:*)", "Bash(npm run lint:*)", "WebFetch(domain:i12bretro.github.io)", "<PERSON><PERSON>(dir)", "<PERSON><PERSON>(dir:*)", "Bash(gf gen:*)"], "deny": [], "ask": []}}