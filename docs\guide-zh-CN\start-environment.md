## 环境搭建

目录

- 前端环境
- 后端环境
- 使用说明

### 前端环境

1. 前往https://nodejs.org/zh-cn/下载当前版本node
2. 命令行运行 `node -v` 若控制台输出版本号则node安装成功
3. node 版本需大于等于 `16.0`
4. 安装pnpm：`npm install -g pnpm`
5. 命令行运行 `pnpm -v` 若控制台输出版本号则前端环境搭建成功

### 后端环境
1. 下载golang安装 版本号需>=1.23
2. 国际: https://golang.org/dl/
3. 国内: https://golang.google.cn/dl/
4. 命令行运行 go 若控制台输出各类提示命令 则安装成功 输入 `go version` 确认版本大于1.23
5. 开发工具推荐 [Goland](https://www.jetbrains.com/go/)

### 使用说明

> 需要本地具有 git node golang 环境

- node版本 >= 20.0.0
- golang版本 >= 1.23
- mysql版本 >= 5.7，引擎需要是 innoDB
- IDE推荐：Goland
