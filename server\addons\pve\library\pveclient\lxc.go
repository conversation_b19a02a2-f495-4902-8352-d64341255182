// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== LXC容器管理 API ==========

// GetLXCContainers 获取LXC容器列表
func (c *Client) GetLXCContainers(ctx context.Context, node string) ([]*LXCContainer, error) {
	path := fmt.Sprintf("/nodes/%s/lxc", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var containers []*LXCContainer
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &containers); err != nil {
		return nil, err
	}

	return containers, nil
}

// GetLXCStatus 获取LXC容器状态
func (c *Client) GetLXCStatus(ctx context.Context, node string, vmid int) (*LXCContainer, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/current", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var container LXCContainer
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &container); err != nil {
		return nil, err
	}

	return &container, nil
}

// GetLXCConfig 获取LXC容器配置
func (c *Client) GetLXCConfig(ctx context.Context, node string, vmid int) (*LXCConfig, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/config", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var config LXCConfig
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// CreateLXCContainer 创建LXC容器
func (c *Client) CreateLXCContainer(ctx context.Context, node string, params *CreateLXCParams) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc", node)
	
	reqParams := map[string]interface{}{
		"vmid":       params.VMID,
		"ostemplate": params.OSTemplate,
	}

	if params.Storage != "" {
		reqParams["storage"] = params.Storage
	}
	if params.Memory > 0 {
		reqParams["memory"] = params.Memory
	}
	if params.Swap > 0 {
		reqParams["swap"] = params.Swap
	}
	if params.Cores > 0 {
		reqParams["cores"] = params.Cores
	}
	if params.RootFS != "" {
		reqParams["rootfs"] = params.RootFS
	}
	if params.Net0 != "" {
		reqParams["net0"] = params.Net0
	}
	if params.Password != "" {
		reqParams["password"] = params.Password
	}
	if params.SSHKeys != "" {
		reqParams["ssh-public-keys"] = params.SSHKeys
	}
	if params.Hostname != "" {
		reqParams["hostname"] = params.Hostname
	}

	body, err := c.request(ctx, "POST", path, reqParams)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// UpdateLXCConfig 更新LXC容器配置
func (c *Client) UpdateLXCConfig(ctx context.Context, node string, vmid int, config map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/config", node, vmid)
	_, err := c.request(ctx, "PUT", path, config)
	return err
}

// StartLXC 启动LXC容器
func (c *Client) StartLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/start", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// StopLXC 停止LXC容器
func (c *Client) StopLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/stop", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// RestartLXC 重启LXC容器
func (c *Client) RestartLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/restart", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// ShutdownLXC 关闭LXC容器
func (c *Client) ShutdownLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/shutdown", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// SuspendLXC 暂停LXC容器
func (c *Client) SuspendLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/suspend", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// ResumeLXC 恢复LXC容器
func (c *Client) ResumeLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/status/resume", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// DeleteLXC 删除LXC容器
func (c *Client) DeleteLXC(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d", node, vmid)
	body, err := c.request(ctx, "DELETE", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// CloneLXC 克隆LXC容器
func (c *Client) CloneLXC(ctx context.Context, node string, vmid int, params *CloneVMParams) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/clone", node, vmid)
	
	reqParams := map[string]interface{}{
		"newid": params.NewID,
	}

	if params.Name != "" {
		reqParams["hostname"] = params.Name
	}
	if params.Description != "" {
		reqParams["description"] = params.Description
	}
	if params.Target != "" {
		reqParams["target"] = params.Target
	}
	if params.Storage != "" {
		reqParams["storage"] = params.Storage
	}
	if params.Full {
		reqParams["full"] = 1
	}

	body, err := c.request(ctx, "POST", path, reqParams)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// CreateLXCSnapshot 创建LXC容器快照
func (c *Client) CreateLXCSnapshot(ctx context.Context, node string, vmid int, snapname, description string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/snapshot", node, vmid)
	
	params := map[string]interface{}{
		"snapname": snapname,
	}
	if description != "" {
		params["description"] = description
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// GetLXCSnapshots 获取LXC容器快照列表
func (c *Client) GetLXCSnapshots(ctx context.Context, node string, vmid int) ([]*Snapshot, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/snapshot", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var snapshots []*Snapshot
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &snapshots); err != nil {
		return nil, err
	}

	return snapshots, nil
}

// RollbackLXCSnapshot 恢复LXC容器快照
func (c *Client) RollbackLXCSnapshot(ctx context.Context, node string, vmid int, snapname string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/snapshot/%s/rollback", node, vmid, snapname)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// DeleteLXCSnapshot 删除LXC容器快照
func (c *Client) DeleteLXCSnapshot(ctx context.Context, node string, vmid int, snapname string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/snapshot/%s", node, vmid, snapname)
	body, err := c.request(ctx, "DELETE", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// GetLXCRRDData 获取LXC容器RRD监控数据
func (c *Client) GetLXCRRDData(ctx context.Context, node string, vmid int, timeframe string) (map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/rrddata", node, vmid)
	
	params := map[string]interface{}{}
	if timeframe != "" {
		params["timeframe"] = timeframe
	}

	body, err := c.request(ctx, "GET", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	data, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的RRD数据格式")
	}

	return data, nil
}

// GetLXCFirewallRules 获取LXC容器防火墙规则
func (c *Client) GetLXCFirewallRules(ctx context.Context, node string, vmid int) ([]*FirewallRule, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/firewall/rules", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var rules []*FirewallRule
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &rules); err != nil {
		return nil, err
	}

	return rules, nil
}

// CreateLXCFirewallRule 创建LXC容器防火墙规则
func (c *Client) CreateLXCFirewallRule(ctx context.Context, node string, vmid int, rule *FirewallRule) error {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/firewall/rules", node, vmid)
	
	params := map[string]interface{}{
		"action": rule.Action,
		"type":   rule.Type,
	}
	
	if rule.Enable != 0 {
		params["enable"] = rule.Enable
	}
	if rule.Source != "" {
		params["source"] = rule.Source
	}
	if rule.Dest != "" {
		params["dest"] = rule.Dest
	}
	if rule.Proto != "" {
		params["proto"] = rule.Proto
	}
	if rule.DPort != "" {
		params["dport"] = rule.DPort
	}
	if rule.Sport != "" {
		params["sport"] = rule.Sport
	}
	if rule.IFace != "" {
		params["iface"] = rule.IFace
	}
	if rule.Log != "" {
		params["log"] = rule.Log
	}
	if rule.Comment != "" {
		params["comment"] = rule.Comment
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}