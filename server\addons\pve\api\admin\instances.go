// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/model/input/form"
)

// InstanceCreateReq 创建实例请求
type InstanceCreateReq struct {
	g.Meta      `path:"/instances" method:"post" summary:"创建虚拟机实例" tags:"PVE管理"`
	NodeID      uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"PVE节点ID"`
	UserID      uint64 `json:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	Name        string `json:"name" v:"required|length:1,100#实例名称不能为空|名称长度为1-100个字符" dc:"实例名称"`
	Description string `json:"description" dc:"实例描述"`
	TemplateID  uint64 `json:"templateId" v:"required#模板ID不能为空" dc:"系统模板ID"`
	CpuCores    int    `json:"cpuCores" v:"required|min:1|max:64#CPU核心数不能为空|CPU核心数最少1个|CPU核心数最多64个" dc:"CPU核心数"`
	MemoryMb    int    `json:"memoryMb" v:"required|min:128|max:131072#内存大小不能为空|内存最少128MB|内存最多131072MB" dc:"内存大小(MB)"`
	DiskGb      int    `json:"diskGb" v:"required|min:1|max:1024#磁盘大小不能为空|磁盘最少1GB|磁盘最多1024GB" dc:"磁盘大小(GB)"`
	Period      int    `json:"period" v:"required|min:1#购买时长不能为空|购买时长最少1个周期" dc:"购买时长"`
	PeriodType  string `json:"periodType" v:"required|in:month,year#计费周期不能为空|计费周期只能是month或year" dc:"计费周期"`
	AutoRenew   bool   `json:"autoRenew" dc:"是否自动续费"`
}

type InstanceCreateRes struct {
	InstanceID uint64 `json:"instanceId" dc:"实例ID"`
	VMID       int    `json:"vmid" dc:"PVE虚拟机ID"`
	TaskID     string `json:"taskId" dc:"创建任务ID"`
}

// InstanceEditReq 编辑实例请求
type InstanceEditReq struct {
	g.Meta      `path:"/instances/{id}" method:"put" summary:"编辑虚拟机实例" tags:"PVE管理"`
	ID          uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	Name        string `json:"name" v:"required|length:1,100#实例名称不能为空|名称长度为1-100个字符" dc:"实例名称"`
	Description string `json:"description" dc:"实例描述"`
	CpuCores    int    `json:"cpuCores" v:"required|min:1|max:64#CPU核心数不能为空|CPU核心数最少1个|CPU核心数最多64个" dc:"CPU核心数"`
	MemoryMb    int    `json:"memoryMb" v:"required|min:128|max:131072#内存大小不能为空|内存最少128MB|内存最多131072MB" dc:"内存大小(MB)"`
	DiskGb      int    `json:"diskGb" v:"required|min:1|max:1024#磁盘大小不能为空|磁盘最少1GB|磁盘最多1024GB" dc:"磁盘大小(GB)"`
}

type InstanceEditRes struct{}

// InstanceDeleteReq 删除实例请求
type InstanceDeleteReq struct {
	g.Meta `path:"/instances/{id}" method:"delete" summary:"删除虚拟机实例" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	Force  bool   `json:"force" dc:"是否强制删除"`
}

type InstanceDeleteRes struct {
	TaskID string `json:"taskId" dc:"删除任务ID"`
}

// InstanceViewReq 查看实例请求
type InstanceViewReq struct {
	g.Meta `path:"/instances/{id}" method:"get" summary:"查看虚拟机实例" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
}

type InstanceViewRes struct {
	*InstanceViewModel
}

type InstanceViewModel struct {
	ID           uint64  `json:"id" dc:"实例ID"`
	VMID         int     `json:"vmid" dc:"PVE虚拟机ID"`
	NodeID       uint64  `json:"nodeId" dc:"节点ID"`
	NodeName     string  `json:"nodeName" dc:"节点名称"`
	UserID       uint64  `json:"userId" dc:"用户ID"`
	Username     string  `json:"username" dc:"用户名"`
	Name         string  `json:"name" dc:"实例名称"`
	Description  string  `json:"description" dc:"实例描述"`
	OsTemplate   string  `json:"osTemplate" dc:"操作系统模板"`
	CpuCores     int     `json:"cpuCores" dc:"CPU核心数"`
	MemoryMb     int     `json:"memoryMb" dc:"内存大小(MB)"`
	DiskGb       int     `json:"diskGb" dc:"磁盘大小(GB)"`
	IpAddress    string  `json:"ipAddress" dc:"IP地址"`
	MacAddress   string  `json:"macAddress" dc:"MAC地址"`
	Status       string  `json:"status" dc:"实例状态"`
	CpuUsage     float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage  float64 `json:"memoryUsage" dc:"内存使用率"`
	NetworkIn    int64   `json:"networkIn" dc:"网络入流量"`
	NetworkOut   int64   `json:"networkOut" dc:"网络出流量"`
	DiskRead     int64   `json:"diskRead" dc:"磁盘读取"`
	DiskWrite    int64   `json:"diskWrite" dc:"磁盘写入"`
	ExpiredAt    string  `json:"expiredAt" dc:"到期时间"`
	CreatedAt    string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt    string  `json:"updatedAt" dc:"更新时间"`
}

// InstanceListReq 实例列表请求
type InstanceListReq struct {
	g.Meta `path:"/instances" method:"get" summary:"获取虚拟机实例列表" tags:"PVE管理"`
	form.PageReq
	UserID   uint64 `json:"userId" dc:"用户ID"`
	NodeID   uint64 `json:"nodeId" dc:"节点ID"`
	Status   string `json:"status" dc:"实例状态"`
	Keyword  string `json:"keyword" dc:"搜索关键词"`
	OrderBy  string `json:"orderBy" dc:"排序字段"`
	OrderDir string `json:"orderDir" dc:"排序方向"`
}

type InstanceListRes struct {
	form.PageRes
	List []*InstanceListModel `json:"list" dc:"实例列表"`
}

type InstanceListModel struct {
	ID          uint64  `json:"id" dc:"实例ID"`
	VMID        int     `json:"vmid" dc:"PVE虚拟机ID"`
	NodeID      uint64  `json:"nodeId" dc:"节点ID"`
	NodeName    string  `json:"nodeName" dc:"节点名称"`
	UserID      uint64  `json:"userId" dc:"用户ID"`
	Username    string  `json:"username" dc:"用户名"`
	Name        string  `json:"name" dc:"实例名称"`
	Description string  `json:"description" dc:"实例描述"`
	OsTemplate  string  `json:"osTemplate" dc:"操作系统模板"`
	CpuCores    int     `json:"cpuCores" dc:"CPU核心数"`
	MemoryMb    int     `json:"memoryMb" dc:"内存大小(MB)"`
	DiskGb      int     `json:"diskGb" dc:"磁盘大小(GB)"`
	Status      string  `json:"status" dc:"实例状态"`
	IpAddress   string  `json:"ipAddress" dc:"IP地址"`
	CpuUsage    float64 `json:"cpuUsage" dc:"CPU使用率"`
	MemoryUsage float64 `json:"memoryUsage" dc:"内存使用率"`
	ExpiredAt   string  `json:"expiredAt" dc:"到期时间"`
	CreatedAt   string  `json:"createdAt" dc:"创建时间"`
	UpdatedAt   string  `json:"updatedAt" dc:"更新时间"`
}

// InstanceActionReq 实例操作请求
type InstanceActionReq struct {
	g.Meta `path:"/instances/{id}/action" method:"post" summary:"执行虚拟机操作" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	Action string `json:"action" v:"required|in:start,stop,reboot,reset,suspend,resume#操作类型不能为空|操作类型无效" dc:"操作类型"`
	Force  bool   `json:"force" dc:"是否强制执行"`
}

type InstanceActionRes struct {
	TaskID string `json:"taskId" dc:"操作任务ID"`
	Status string `json:"status" dc:"执行状态"`
}

// InstanceRenewReq 续费实例请求
type InstanceRenewReq struct {
	g.Meta     `path:"/instances/{id}/renew" method:"post" summary:"续费虚拟机实例" tags:"PVE管理"`
	ID         uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	Period     int    `json:"period" v:"required|min:1#续费时长不能为空|续费时长最少1个周期" dc:"续费时长"`
	PeriodType string `json:"periodType" v:"required|in:month,year#计费周期不能为空|计费周期只能是month或year" dc:"计费周期"`
}

type InstanceRenewRes struct {
	OrderID   uint64 `json:"orderId" dc:"订单ID"`
	ExpiredAt string `json:"expiredAt" dc:"新的到期时间"`
}

// InstanceConsoleReq 获取控制台访问信息请求
type InstanceConsoleReq struct {
	g.Meta `path:"/instances/{id}/console" method:"get" summary:"获取虚拟机控制台访问信息" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
}

type InstanceConsoleRes struct {
	ConsoleURL string `json:"consoleUrl" dc:"控制台访问URL"`
	VncPort    int    `json:"vncPort" dc:"VNC端口"`
	VncTicket  string `json:"vncTicket" dc:"VNC访问票据"`
}

// InstanceSnapshotReq 创建快照请求
type InstanceSnapshotReq struct {
	g.Meta      `path:"/instances/{id}/snapshots" method:"post" summary:"创建虚拟机快照" tags:"PVE管理"`
	ID          uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	SnapName    string `json:"snapName" v:"required|length:1,50#快照名称不能为空|名称长度为1-50个字符" dc:"快照名称"`
	Description string `json:"description" dc:"快照描述"`
}

type InstanceSnapshotRes struct {
	TaskID string `json:"taskId" dc:"快照任务ID"`
}

// InstanceSnapshotListReq 快照列表请求
type InstanceSnapshotListReq struct {
	g.Meta `path:"/instances/{id}/snapshots" method:"get" summary:"获取虚拟机快照列表" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
}

type InstanceSnapshotListRes struct {
	List []*SnapshotModel `json:"list" dc:"快照列表"`
}

type SnapshotModel struct {
	Name        string `json:"name" dc:"快照名称"`
	Description string `json:"description" dc:"快照描述"`
	SnapTime    int64  `json:"snapTime" dc:"创建时间"`
	Running     bool   `json:"running" dc:"创建时是否运行中"`
	Size        int64  `json:"size" dc:"快照大小"`
}

// InstanceMonitorReq 实例监控数据请求
type InstanceMonitorReq struct {
	g.Meta `path:"/instances/{id}/monitor" method:"get" summary:"获取虚拟机实例监控数据" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	Period string `json:"period" v:"in:1h,24h,7d,30d#时间周期错误" dc:"时间周期：1h,24h,7d,30d"`
}

type InstanceMonitorRes struct {
	CPUData     []*MonitorPoint `json:"cpuData" dc:"CPU使用率数据"`
	MemoryData  []*MonitorPoint `json:"memoryData" dc:"内存使用率数据"`
	NetworkData []*MonitorPoint `json:"networkData" dc:"网络流量数据"`
	DiskIOData  []*MonitorPoint `json:"diskIOData" dc:"磁盘IO数据"`
}

// InstanceTaskReq 获取实例任务状态请求
type InstanceTaskReq struct {
	g.Meta `path:"/instances/tasks/{taskId}" method:"get" summary:"获取实例操作任务状态" tags:"PVE管理"`
	TaskID string `json:"taskId" v:"required#任务ID不能为空" dc:"任务ID"`
}

type InstanceTaskRes struct {
	TaskID     string `json:"taskId" dc:"任务ID"`
	Status     string `json:"status" dc:"任务状态"`
	ExitStatus string `json:"exitStatus" dc:"退出状态"`
	StartTime  int64  `json:"startTime" dc:"开始时间"`
	EndTime    int64  `json:"endTime" dc:"结束时间"`
	Log        string `json:"log" dc:"任务日志"`
}

// InstanceCloneReq 克隆虚拟机请求
type InstanceCloneReq struct {
	g.Meta      `path:"/instances/{id}/clone" method:"post" summary:"克隆虚拟机实例" tags:"PVE管理"`
	ID          uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	Name        string `json:"name" v:"required|length:1,100#实例名称不能为空|名称长度为1-100个字符" dc:"新实例名称"`
	Description string `json:"description" dc:"实例描述"`
	NodeID      uint64 `json:"nodeId" dc:"目标节点ID，为空则使用原节点"`
	Full        bool   `json:"full" dc:"是否完整克隆"`
}

type InstanceCloneRes struct {
	TaskID string `json:"taskId" dc:"克隆任务ID"`
	VMID   int    `json:"vmid" dc:"新的虚拟机ID"`
}

// InstanceMigrateReq 迁移虚拟机请求
type InstanceMigrateReq struct {
	g.Meta       `path:"/instances/{id}/migrate" method:"post" summary:"迁移虚拟机实例" tags:"PVE管理"`
	ID           uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	TargetNodeID uint64 `json:"targetNodeId" v:"required#目标节点ID不能为空" dc:"目标节点ID"`
	Online       bool   `json:"online" dc:"是否在线迁移"`
}

type InstanceMigrateRes struct {
	TaskID string `json:"taskId" dc:"迁移任务ID"`
}

// InstanceRRDReq 获取RRD监控数据请求
type InstanceRRDReq struct {
	g.Meta `path:"/instances/{id}/rrd" method:"get" summary:"获取虚拟机RRD监控数据" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	DS     string `json:"ds" v:"required|in:cpu,mem,netin,netout,diskread,diskwrite#数据源不能为空|数据源类型错误" dc:"数据源类型"`
	CF     string `json:"cf" v:"in:AVERAGE,MAX#聚合函数错误" dc:"聚合函数"`
}

type InstanceRRDRes struct {
	Data []*RRDPoint `json:"data" dc:"RRD数据点"`
}

type RRDPoint struct {
	Time  int64   `json:"time" dc:"时间戳"`
	Value float64 `json:"value" dc:"数值"`
}

// InstanceRRDDataReq RRD监控数据请求
type InstanceRRDDataReq struct {
	g.Meta `path:"/instances/{id}/rrddata" method:"get" summary:"获取虚拟机RRD监控数据" tags:"PVE管理"`
	ID     uint64 `json:"id" v:"required#实例ID不能为空" dc:"实例ID"`
	DS     string `json:"ds" v:"required|in:cpu,mem,netin,netout,diskread,diskwrite#数据源不能为空|数据源类型错误" dc:"数据源类型"`
	CF     string `json:"cf" v:"in:AVERAGE,MAX#聚合函数错误" dc:"聚合函数"`
}

type InstanceRRDDataRes struct {
	Data map[string]interface{} `json:"data" dc:"RRD数据"`
}