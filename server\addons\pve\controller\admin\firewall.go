// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
)

var (
	Firewall = cFirewall{}
)

type cFirewall struct{}

// GetRules 获取防火墙规则
func (c *cFirewall) GetRules(ctx context.Context, req *admin.FirewallRulesReq) (res *admin.FirewallRulesRes, err error) {
	// TODO: 实现获取防火墙规则逻辑
	return &admin.FirewallRulesRes{
		List: []*admin.FirewallRuleViewModel{},
	}, nil
}

// CreateRule 创建防火墙规则
func (c *cFirewall) CreateRule(ctx context.Context, req *admin.FirewallCreateRuleReq) (res *admin.FirewallCreateRuleRes, err error) {
	// TODO: 实现创建防火墙规则逻辑
	return &admin.FirewallCreateRuleRes{
		ID: 1,
	}, nil
}

// EditRule 编辑防火墙规则
func (c *cFirewall) EditRule(ctx context.Context, req *admin.FirewallEditRuleReq) (res *admin.FirewallEditRuleRes, err error) {
	// TODO: 实现编辑防火墙规则逻辑
	return &admin.FirewallEditRuleRes{}, nil
}

// DeleteRule 删除防火墙规则
func (c *cFirewall) DeleteRule(ctx context.Context, req *admin.FirewallDeleteRuleReq) (res *admin.FirewallDeleteRuleRes, err error) {
	// TODO: 实现删除防火墙规则逻辑
	return &admin.FirewallDeleteRuleRes{}, nil
}
