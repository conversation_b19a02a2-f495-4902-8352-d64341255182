// Package router
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package router

import (
	"context"
	"hotgo/addons/pve/controller/admin"
	"hotgo/addons/pve/global"
	"hotgo/internal/consts"
	"hotgo/internal/library/addons"

	"github.com/gogf/gf/v2/net/ghttp"
)

// Admin 管理后台路由
func Admin(ctx context.Context, group *ghttp.RouterGroup) {
	prefix := addons.RouterPrefix(ctx, consts.AppAdmin, global.GetSkeleton().Name)
	group.Group(prefix, func(group *ghttp.RouterGroup) {
		// 仪表盘路由
		group.GET("/dashboard/stats", admin.Dashboard.GetStats)
		group.GET("/dashboard/resource-usage", admin.Dashboard.GetResourceUsage)
		group.GET("/dashboard/system-alerts", admin.Dashboard.GetSystemAlerts)
		group.POST("/dashboard/resolve-alert", admin.Dashboard.ResolveAlert)

		// 节点管理路由
		group.POST("/nodes/create", admin.Nodes.Create)
		group.PUT("/nodes/edit", admin.Nodes.Edit)
		group.DELETE("/nodes/delete", admin.Nodes.Delete)
		group.GET("/nodes/view", admin.Nodes.View)
		group.GET("/nodes/list", admin.Nodes.List)
		group.POST("/nodes/test", admin.Nodes.Test)
		group.POST("/nodes/sync", admin.Nodes.Sync)
		group.GET("/nodes/monitor", admin.Nodes.Monitor)
		group.GET("/nodes/services", admin.Nodes.GetServices)
		group.POST("/nodes/control-service", admin.Nodes.ControlService)
		group.POST("/nodes/reboot", admin.Nodes.RebootNode)
		group.POST("/nodes/shutdown", admin.Nodes.ShutdownNode)

		// 实例管理路由
		group.POST("/instances/create", admin.Instances.Create)
		group.PUT("/instances/edit", admin.Instances.Edit)
		group.DELETE("/instances/delete", admin.Instances.Delete)
		group.GET("/instances/view", admin.Instances.View)
		group.GET("/instances/list", admin.Instances.List)
		group.POST("/instances/action", admin.Instances.Action)
		group.POST("/instances/renew", admin.Instances.Renew)
		group.GET("/instances/console", admin.Instances.Console)
		group.POST("/instances/snapshot", admin.Instances.Snapshot)
		group.GET("/instances/snapshot-list", admin.Instances.SnapshotList)
		group.GET("/instances/monitor", admin.Instances.Monitor)
		group.GET("/instances/task-status", admin.Instances.TaskStatus)
		group.POST("/instances/clone", admin.Instances.Clone)
		group.POST("/instances/migrate", admin.Instances.Migrate)
		group.GET("/instances/rrd-data", admin.Instances.GetRRDData)

		// 模板管理路由
		group.POST("/templates/create", admin.Templates.Create)
		group.PUT("/templates/edit", admin.Templates.Edit)
		group.DELETE("/templates/delete", admin.Templates.Delete)
		group.GET("/templates/view", admin.Templates.View)
		group.GET("/templates/list", admin.Templates.List)
		group.POST("/templates/sync", admin.Templates.Sync)
		group.POST("/templates/import", admin.Templates.Import)
		group.GET("/templates/select-list", admin.Templates.GetSelectList)

		// LXC容器管理路由
		group.POST("/containers/create", admin.Containers.Create)
		group.PUT("/containers/edit", admin.Containers.Edit)
		group.DELETE("/containers/delete", admin.Containers.Delete)
		group.GET("/containers/view", admin.Containers.View)
		group.GET("/containers/list", admin.Containers.List)
		group.POST("/containers/action", admin.Containers.Action)
		group.GET("/containers/console", admin.Containers.Console)
		group.GET("/containers/monitor", admin.Containers.Monitor)

		// 集群管理路由
		group.GET("/cluster/status", admin.Cluster.GetStatus)
		group.GET("/cluster/resources", admin.Cluster.GetResources)
		group.GET("/cluster/nodes", admin.Cluster.GetNodes)
		group.GET("/cluster/nextid", admin.Cluster.GetNextID)
		group.GET("/cluster/log", admin.Cluster.GetLog)

		// 存储管理路由
		group.GET("/storage/list", admin.Storage.List)
		group.GET("/storage/view", admin.Storage.View)
		group.POST("/storage/create", admin.Storage.Create)
		group.PUT("/storage/edit", admin.Storage.Edit)
		group.DELETE("/storage/delete", admin.Storage.Delete)
		group.GET("/storage/content", admin.Storage.GetContent)

		// 备份管理路由
		group.GET("/backup/list", admin.Backup.List)
		group.POST("/backup/create", admin.Backup.Create)
		group.DELETE("/backup/delete", admin.Backup.Delete)
		group.POST("/backup/restore", admin.Backup.Restore)

		// 网络管理路由
		group.GET("/network/list", admin.Network.List)
		group.POST("/network/create", admin.Network.Create)
		group.PUT("/network/edit", admin.Network.Edit)
		group.DELETE("/network/delete", admin.Network.Delete)

		// 防火墙管理路由
		group.GET("/firewall/rules", admin.Firewall.GetRules)
		group.POST("/firewall/rule", admin.Firewall.CreateRule)
		group.PUT("/firewall/rule", admin.Firewall.EditRule)
		group.DELETE("/firewall/rule", admin.Firewall.DeleteRule)

		// 高可用管理路由
		group.GET("/ha/resources", admin.HA.GetResources)
		group.POST("/ha/resource", admin.HA.CreateResource)
		group.PUT("/ha/resource", admin.HA.EditResource)
		group.DELETE("/ha/resource", admin.HA.DeleteResource)
		group.GET("/ha/groups", admin.HA.GetGroups)
		group.POST("/ha/group", admin.HA.CreateGroup)
		group.PUT("/ha/group", admin.HA.EditGroup)
		group.DELETE("/ha/group", admin.HA.DeleteGroup)

		// 用户权限管理路由
		group.GET("/pveusers/list", admin.PVEUsers.List)
		group.POST("/pveusers/create", admin.PVEUsers.Create)
		group.PUT("/pveusers/edit", admin.PVEUsers.Edit)
		group.DELETE("/pveusers/delete", admin.PVEUsers.Delete)
		group.GET("/pveusers/roles", admin.PVEUsers.GetRoles)
		group.POST("/pveusers/role", admin.PVEUsers.CreateRole)
		group.PUT("/pveusers/role", admin.PVEUsers.EditRole)
		group.DELETE("/pveusers/role", admin.PVEUsers.DeleteRole)
	})
}

// User 用户端路由
func User(ctx context.Context, group *ghttp.RouterGroup) {
	// 用户端路由暂时为空，可以后续添加用户自助服务功能
	prefix := addons.RouterPrefix(ctx, consts.AppHome, global.GetSkeleton().Name)
	group.Group(prefix+"/user", func(group *ghttp.RouterGroup) {
		// TODO: 添加用户端路由
	})
}

// Webhook WebHook路由
func Webhook(ctx context.Context, group *ghttp.RouterGroup) {
	// WebHook路由，用于接收PVE事件通知
	prefix := addons.RouterPrefix(ctx, consts.AppApi, global.GetSkeleton().Name)
	group.Group(prefix+"/webhook", func(group *ghttp.RouterGroup) {
		// TODO: 添加WebHook路由
	})
}
