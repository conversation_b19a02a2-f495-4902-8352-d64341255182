// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// 高级功能相关输入输出模型

// 防火墙相关
type FirewallRulesInp struct {
	NodeID int `json:"nodeId"`
	VMID   int `json:"vmid"`
}

type FirewallRule struct {
	Pos     int    `json:"pos"`
	Type    string `json:"type"`
	Action  string `json:"action"`
	Macro   string `json:"macro,omitempty"`
	IFace   string `json:"iface,omitempty"`
	Source  string `json:"source,omitempty"`
	Dest    string `json:"dest,omitempty"`
	Proto   string `json:"proto,omitempty"`
	DPort   string `json:"dport,omitempty"`
	Sport   string `json:"sport,omitempty"`
	Comment string `json:"comment,omitempty"`
	Enable  int    `json:"enable"`
}

type FirewallRulesModel struct {
	List []*FirewallRule `json:"list"`
}

type FirewallRulesOut struct {
	*FirewallRulesModel
}

type FirewallCreateRuleInp struct {
	NodeID  int    `json:"nodeId"`
	VMID    int    `json:"vmid"`
	Type    string `json:"type" v:"required"`
	Action  string `json:"action" v:"required"`
	Macro   string `json:"macro"`
	IFace   string `json:"iface"`
	Source  string `json:"source"`
	Dest    string `json:"dest"`
	Proto   string `json:"proto"`
	DPort   string `json:"dport"`
	Sport   string `json:"sport"`
	Comment string `json:"comment"`
	Enable  int    `json:"enable"`
}

type FirewallCreateRuleModel struct {
	Success bool `json:"success"`
}

type FirewallEditRuleInp struct {
	ID      int    `json:"id" v:"required"`
	NodeID  int    `json:"nodeId"`
	VMID    int    `json:"vmid"`
	Pos     int    `json:"pos"`
	Type    string `json:"type"`
	Action  string `json:"action"`
	Macro   string `json:"macro"`
	IFace   string `json:"iface"`
	Source  string `json:"source"`
	Dest    string `json:"dest"`
	Proto   string `json:"proto"`
	DPort   string `json:"dport"`
	Sport   string `json:"sport"`
	Comment string `json:"comment"`
	Enable  int    `json:"enable"`
}

type FirewallEditRuleModel struct {
	Success bool `json:"success"`
}

type FirewallDeleteRuleInp struct {
	ID     int `json:"id" v:"required"`
	NodeID int `json:"nodeId"`
	VMID   int `json:"vmid"`
	Pos    int `json:"pos"`
}

type FirewallDeleteRuleModel struct {
	ID int `json:"id"`
}

// HA (高可用) 相关
type HAResourcesInp struct {
}

type HAResource struct {
	SID          string `json:"sid"`
	Type         string `json:"type"`
	State        string `json:"state"`
	Node         string `json:"node"`
	Status       string `json:"status"`
	Comment      string `json:"comment,omitempty"`
	Group        string `json:"group,omitempty"`
	MaxRelocate  int    `json:"max_relocate,omitempty"`
	MaxRestart   int    `json:"max_restart,omitempty"`
}

type HAResourcesModel struct {
	List []*HAResource `json:"list"`
}

type HACreateResourceInp struct {
	SID         string `json:"sid" v:"required"`
	Type        string `json:"type" v:"required"`
	Comment     string `json:"comment"`
	Group       string `json:"group"`
	MaxRelocate int    `json:"max_relocate"`
	MaxRestart  int    `json:"max_restart"`
	State       string `json:"state"`
}

type HACreateResourceModel struct {
	Success bool `json:"success"`
}

type HAEditResourceInp struct {
	ID          int    `json:"id" v:"required"`
	SID         string `json:"sid" v:"required"`
	Comment     string `json:"comment"`
	Group       string `json:"group"`
	MaxRelocate int    `json:"max_relocate"`
	MaxRestart  int    `json:"max_restart"`
	State       string `json:"state"`
}

type HAEditResourceModel struct {
	Success bool `json:"success"`
}

type HADeleteResourceInp struct {
	ID  int    `json:"id" v:"required"`
	SID string `json:"sid" v:"required"`
}

type HADeleteResourceModel struct {
	ID int `json:"id"`
}

type HAGroupsInp struct {
}

type HAGroup struct {
	Group       string `json:"group"`
	Nodes       string `json:"nodes"`
	Comment     string `json:"comment,omitempty"`
	Nofailback  int    `json:"nofailback,omitempty"`
	Restricted  int    `json:"restricted,omitempty"`
}

type HAGroupsModel struct {
	List []*HAGroup `json:"list"`
}

type HACreateGroupInp struct {
	Group      string `json:"group" v:"required"`
	Nodes      string `json:"nodes" v:"required"`
	Comment    string `json:"comment"`
	Nofailback int    `json:"nofailback"`
	Restricted int    `json:"restricted"`
}

type HACreateGroupModel struct {
	Success bool `json:"success"`
}

type HAResourcesOut struct {
	*HAResourcesModel
}

type HAGroupsOut struct {
	*HAGroupsModel
}

type HAGroupCreateOut struct {
	*HACreateGroupModel
}

type HAEditGroupInp struct {
	ID         int    `json:"id" v:"required"`
	Group      string `json:"group" v:"required"`
	Nodes      string `json:"nodes" v:"required"`
	Comment    string `json:"comment"`
	Nofailback int    `json:"nofailback"`
	Restricted int    `json:"restricted"`
}

type HAEditGroupModel struct {
	Success bool `json:"success"`
}

type HADeleteGroupInp struct {
	ID    int    `json:"id" v:"required"`
	Group string `json:"group" v:"required"`
}

type HADeleteGroupModel struct {
	ID int `json:"id"`
}

// PVE 用户管理相关
type PVEUsersListInp struct {
	form.PageReq
	Keyword string `json:"keyword"`
}

type PVEUser struct {
	UserID     string `json:"userid"`
	Comment    string `json:"comment,omitempty"`
	Email      string `json:"email,omitempty"`
	Enable     int    `json:"enable"`
	Expire     int    `json:"expire,omitempty"`
	FirstName  string `json:"firstname,omitempty"`
	LastName   string `json:"lastname,omitempty"`
	Groups     string `json:"groups,omitempty"`
	Keys       string `json:"keys,omitempty"`
	Tokens     string `json:"tokens,omitempty"`
}

type PVEUsersListModel struct {
	List  []*PVEUser `json:"list"`
	Total int        `json:"total"`
	form.PageRes
}

type PVEUsersCreateInp struct {
	UserID    string `json:"userid" v:"required"`
	Password  string `json:"password" v:"required"`
	Comment   string `json:"comment"`
	Email     string `json:"email"`
	Enable    int    `json:"enable"`
	Expire    int    `json:"expire"`
	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
	Groups    string `json:"groups"`
}

type PVEUsersCreateModel struct {
	UserID string `json:"userid"`
}

type PVEUsersEditInp struct {
	ID        int    `json:"id" v:"required"`
	UserID    string `json:"userid" v:"required"`
	Comment   string `json:"comment"`
	Email     string `json:"email"`
	Enable    int    `json:"enable"`
	Expire    int    `json:"expire"`
	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
	Groups    string `json:"groups"`
}

type PVEUsersEditModel struct {
	UserID string `json:"userid"`
}

type PVEUsersDeleteInp struct {
	ID     int    `json:"id" v:"required"`
	UserID string `json:"userid" v:"required"`
}

type PVEUsersDeleteModel struct {
	ID int `json:"id"`
}

// PVE 角色管理
type PVERolesListInp struct {
	form.PageReq
}

type PVERole struct {
	RoleID string `json:"roleid"`
	Privs  string `json:"privs,omitempty"`
}

type PVERolesListModel struct {
	List  []*PVERole `json:"list"`
	Total int        `json:"total"`
	form.PageRes
}

type PVERolesCreateInp struct {
	RoleID string `json:"roleid" v:"required"`
	Privs  string `json:"privs"`
}

type PVERolesCreateModel struct {
	RoleID string `json:"roleid"`
}

type PVERolesEditInp struct {
	ID     int    `json:"id" v:"required"`
	RoleID string `json:"roleid" v:"required"`
	Privs  string `json:"privs"`
}

type PVERolesEditModel struct {
	RoleID string `json:"roleid"`
}

type PVERolesDeleteInp struct {
	ID     int    `json:"id" v:"required"`
	RoleID string `json:"roleid" v:"required"`
}

type PVERolesDeleteModel struct {
	ID int `json:"id"`
}