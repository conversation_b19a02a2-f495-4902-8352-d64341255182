// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Config PVE客户端配置结构体
// 支持两种认证方式：
// 1. 用户名密码认证（Username + Password）
// 2. API Token认证（TokenID + TokenSecret）
// API Token认证更安全，建议在生产环境使用
type Config struct {
	Host        string        // PVE服务器主机地址，如：*************
	Port        int           // PVE API端口，默认8006
	Username    string        // 用户名，格式：user@realm，如：root@pam
	Password    string        // 用户密码，用于用户名密码认证方式
	TokenID     string        // API Token ID，格式：user@realm!tokenname
	TokenSecret string        // API Token Secret，UUID格式的密钥
	Insecure    bool          // 是否跳过SSL证书验证，开发环境可设为true
	Timeout     time.Duration // HTTP请求超时时间，建议30秒
}

// Client PVE API客户端结构体
// 封装了与Proxmox VE REST API的所有交互逻辑
// 支持自动认证、请求重试、错误处理等功能
type Client struct {
	config     *Config      // 客户端配置信息
	httpClient *http.Client // HTTP客户端，支持SSL和超时配置
	baseURL    string       // API基础URL，格式：https://host:port/api2/json
	ticket     string       // 认证票据，用户名密码认证后获得，有效期2小时
	csrfToken  string       // CSRF防护令牌，POST/PUT/DELETE请求时需要
}

// Response PVE API响应
type Response struct {
	Data   interface{} `json:"data"`
	Errors interface{} `json:"errors"`
}

// NewClient 创建PVE客户端实例
// 功能说明：
// - 验证配置参数的有效性
// - 初始化HTTP客户端，配置SSL和超时
// - 构建API基础URL
// 参数：
//
//	config: PVE客户端配置，包含连接信息和认证凭据
//
// 返回：
//
//	*Client: PVE客户端实例
//	error: 配置验证失败时返回错误
func NewClient(config *Config) (*Client, error) {
	if config.Host == "" {
		return nil, fmt.Errorf("host不能为空")
	}

	if config.Port == 0 {
		config.Port = 8006
	}

	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	client := &Client{
		config:  config,
		baseURL: fmt.Sprintf("https://%s:%d/api2/json", config.Host, config.Port),
		httpClient: &http.Client{
			Timeout: config.Timeout,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: config.Insecure,
				},
				MaxIdleConns:        100,              // 最大空闲连接数
				MaxConnsPerHost:     10,               // 每个主机的最大连接数
				MaxIdleConnsPerHost: 10,               // 每个主机最大空闲连接数
				IdleConnTimeout:     90 * time.Second, // 空闲连接超时
			},
		},
	}

	return client, nil
}

// authenticate 执行用户认证流程
// 功能说明：
// - 支持API Token和用户名密码两种认证方式
// - API Token认证直接跳过，在请求头中使用
// - 用户名密码认证需要先获取ticket和CSRF token
// - 认证票据有效期为2小时，过期后需重新认证
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//
// 返回：
//
//	error: 认证失败时返回错误信息
func (c *Client) authenticate(ctx context.Context) error {
	// 如果配置了Token认证，直接返回
	if c.config.TokenID != "" && c.config.TokenSecret != "" {
		return nil
	}

	// 使用用户名密码认证
	if c.config.Username == "" || c.config.Password == "" {
		return fmt.Errorf("用户名或密码不能为空")
	}

	authURL := fmt.Sprintf("%s/access/ticket", c.baseURL)
	data := url.Values{}
	data.Set("username", c.config.Username)
	data.Set("password", c.config.Password)

	req, err := http.NewRequestWithContext(ctx, "POST", authURL, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("认证失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var authResp struct {
		Data struct {
			Ticket              string `json:"ticket"`
			CSRFPreventionToken string `json:"CSRFPreventionToken"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &authResp); err != nil {
		return err
	}

	c.ticket = authResp.Data.Ticket
	c.csrfToken = authResp.Data.CSRFPreventionToken

	return nil
}

// request 发送HTTP请求到PVE API
// 功能说明：
// - 自动处理认证（票据或API Token）
// - 支持GET、POST、PUT、DELETE等HTTP方法
// - GET请求参数通过URL传递，其他方法通过表单数据传递
// - 自动设置必要的HTTP头（认证、CSRF、内容类型）
// - 统一处理HTTP错误状态码
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//	method: HTTP方法，如GET、POST、PUT、DELETE
//	path: API路径，如/nodes/pve/qemu
//	params: 请求参数，键值对形式
//
// 返回：
//
//	[]byte: 响应体数据
//	error: 请求失败时返回错误信息
func (c *Client) request(ctx context.Context, method, path string, params map[string]interface{}) ([]byte, error) {
	// 确保已认证
	if c.ticket == "" && c.config.TokenID == "" {
		if err := c.authenticate(ctx); err != nil {
			return nil, err
		}
	}

	requestURL := fmt.Sprintf("%s%s", c.baseURL, path)

	var req *http.Request
	var err error

	if method == "GET" {
		if params != nil {
			values := url.Values{}
			for k, v := range params {
				values.Set(k, fmt.Sprintf("%v", v))
			}
			requestURL += "?" + values.Encode()
		}
		req, err = http.NewRequestWithContext(ctx, method, requestURL, nil)
	} else {
		data := url.Values{}
		if params != nil {
			for k, v := range params {
				data.Set(k, fmt.Sprintf("%v", v))
			}
		}
		req, err = http.NewRequestWithContext(ctx, method, requestURL, strings.NewReader(data.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	}

	if err != nil {
		return nil, err
	}

	// 设置认证头
	if c.config.TokenID != "" && c.config.TokenSecret != "" {
		// 使用Token认证
		req.Header.Set("Authorization", fmt.Sprintf("PVEAPIToken=%s=%s", c.config.TokenID, c.config.TokenSecret))
	} else {
		// 使用Ticket认证
		req.Header.Set("Cookie", fmt.Sprintf("PVEAuthCookie=%s", c.ticket))
		if method != "GET" {
			req.Header.Set("CSRFPreventionToken", c.csrfToken)
		}
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode >= 400 {
		// 解析错误响应
		var errorResp struct {
			Data struct {
				Message string `json:"message"`
			} `json:"data"`
		}
		if err := json.Unmarshal(body, &errorResp); err == nil && errorResp.Data.Message != "" {
			return nil, fmt.Errorf("PVE API错误 [%d]: %s", resp.StatusCode, errorResp.Data.Message)
		}
		return nil, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 记录成功的请求（调试模式）
	// TODO: 根据实际配置决定是否启用调试日志
	// if g.Config().Get("app.debug").Bool() {
	// 	g.Log().Debugf(ctx, "PVE API请求成功: %s %s (状态码: %d)", method, path, resp.StatusCode)
	// }

	return body, nil
}

// GetVersion 获取版本信息
func (c *Client) GetVersion(ctx context.Context) (*VersionInfo, error) {
	body, err := c.request(ctx, "GET", "/version", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var version VersionInfo
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &version); err != nil {
		return nil, err
	}

	return &version, nil
}

// GetNodes 获取节点列表
func (c *Client) GetNodes(ctx context.Context) ([]*NodeInfo, error) {
	body, err := c.request(ctx, "GET", "/nodes", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var nodes []*NodeInfo
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &nodes); err != nil {
		return nil, err
	}

	return nodes, nil
}

// GetNodeStatus 获取节点状态
func (c *Client) GetNodeStatus(ctx context.Context, node string) (*NodeStatus, error) {
	path := fmt.Sprintf("/nodes/%s/status", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var status NodeStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, err
	}

	return &status, nil
}

// GetVMs 获取指定节点的虚拟机列表
// 功能说明：
// - 获取指定PVE节点上所有QEMU虚拟机的基本信息
// - 包含虚拟机状态、资源使用情况、配置参数等
// - 支持显示模板虚拟机（template=1）
// - 返回数据包含实时的CPU、内存、网络、磁盘使用量
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//	node: PVE节点名称，如"pve"、"node1"
//
// 返回：
//
//	[]*VMInfo: 虚拟机信息列表，包含VMID、名称、状态等
//	error: 获取失败时返回错误信息
//
// 使用示例：
//
//	vms, err := client.GetVMs(ctx, "pve")
func (c *Client) GetVMs(ctx context.Context, node string) ([]*VMInfo, error) {
	path := fmt.Sprintf("/nodes/%s/qemu", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var vms []*VMInfo
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &vms); err != nil {
		return nil, err
	}

	return vms, nil
}

// GetVMStatus 获取虚拟机状态
func (c *Client) GetVMStatus(ctx context.Context, node string, vmid int) (*VMStatus, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var status VMStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, err
	}

	return &status, nil
}

// CreateVM 创建新的QEMU虚拟机
// 功能说明：
// - 根据提供的参数创建新的虚拟机实例
// - 支持从模板克隆或全新创建
// - 创建过程异步执行，返回任务ID用于状态追踪
// - 自动分配VMID或使用指定的VMID
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//	node: 目标PVE节点名称
//	params: 创建参数，包含VMID、名称、CPU、内存等配置
//
// 返回：
//
//	*TaskResponse: 任务响应，包含异步任务ID
//	error: 创建失败时返回错误信息
//
// 注意事项：
//   - VMID必须在集群中唯一
//   - 创建操作是异步的，需要通过GetTaskStatus监控进度
func (c *Client) CreateVM(ctx context.Context, node string, params *CreateVMParams) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu", node)

	reqParams := map[string]interface{}{
		"vmid":   params.VMID,
		"name":   params.Name,
		"cores":  params.Cores,
		"memory": params.Memory,
	}

	if params.Template != "" {
		reqParams["template"] = params.Template
	}
	if params.OSType != "" {
		reqParams["ostype"] = params.OSType
	}
	if params.Storage != "" {
		reqParams["storage"] = params.Storage
	}

	body, err := c.request(ctx, "POST", path, reqParams)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// StartVM 启动指定的虚拟机
// 功能说明：
// - 启动处于停止状态的虚拟机
// - 操作异步执行，返回任务ID用于监控启动进度
// - 如果虚拟机已经运行，操作会失败
// - 支持启动时的特殊参数（如从特定快照启动）
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//	node: 虚拟机所在的PVE节点名称
//	vmid: 虚拟机ID，必须存在且处于停止状态
//
// 返回：
//
//	*TaskResponse: 任务响应，包含异步任务ID
//	error: 启动失败时返回错误信息
//
// 常见错误：
//   - 虚拟机不存在
//   - 虚拟机已在运行
//   - 节点资源不足
func (c *Client) StartVM(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/status/start", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// StopVM 停止指定的虚拟机
// 功能说明：
// - 强制停止正在运行的虚拟机
// - 等同于物理机断电操作，可能导致数据丢失
// - 建议优先使用ShutdownVM进行优雅关机
// - 操作异步执行，返回任务ID用于监控停止进度
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//	node: 虚拟机所在的PVE节点名称
//	vmid: 虚拟机ID，必须存在且处于运行状态
//
// 返回：
//
//	*TaskResponse: 任务响应，包含异步任务ID
//	error: 停止失败时返回错误信息
//
// 安全提示：
//   - 强制停止可能导致文件系统损坏
//   - 建议先尝试关机操作
func (c *Client) StopVM(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/status/stop", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// RebootVM 重启虚拟机
func (c *Client) RebootVM(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/status/reboot", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// DeleteVM 删除虚拟机
func (c *Client) DeleteVM(ctx context.Context, node string, vmid int) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d", node, vmid)
	body, err := c.request(ctx, "DELETE", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// GetTaskStatus 获取任务状态
func (c *Client) GetTaskStatus(ctx context.Context, node, taskid string) (*TaskStatus, error) {
	path := fmt.Sprintf("/nodes/%s/tasks/%s/status", node, taskid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var status TaskStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, err
	}

	return &status, nil
}

// CloneVM 克隆现有虚拟机
// 功能说明：
// - 从现有虚拟机或模板创建副本
// - 支持完整克隆（独立副本）和链接克隆（基于快照）
// - 可以克隆到相同节点或不同节点
// - 自动处理磁盘复制和网络配置
// 参数：
//
//	ctx: 上下文，用于请求取消和超时控制
//	node: 源虚拟机所在的PVE节点名称
//	vmid: 源虚拟机ID
//	params: 克隆参数，包含新VMID、名称、目标节点等
//
// 返回：
//
//	*TaskResponse: 任务响应，包含异步任务ID
//	error: 克隆失败时返回错误信息
//
// 克隆类型：
//   - 完整克隆：创建独立的副本，占用更多存储空间
//   - 链接克隆：基于快照创建，节省存储空间但依赖源虚拟机
func (c *Client) CloneVM(ctx context.Context, node string, vmid int, params *CloneVMParams) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/clone", node, vmid)

	reqParams := map[string]interface{}{
		"newid": params.NewID,
	}

	if params.Name != "" {
		reqParams["name"] = params.Name
	}
	if params.Description != "" {
		reqParams["description"] = params.Description
	}
	if params.Target != "" {
		reqParams["target"] = params.Target
	}
	if params.Storage != "" {
		reqParams["storage"] = params.Storage
	}
	if params.Full {
		reqParams["full"] = 1
	}

	body, err := c.request(ctx, "POST", path, reqParams)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// UpdateVMConfig 更新虚拟机配置
func (c *Client) UpdateVMConfig(ctx context.Context, node string, vmid int, params map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vmid)
	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// GetVMConfig 获取虚拟机配置
func (c *Client) GetVMConfig(ctx context.Context, node string, vmid int) (*VMConfig, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var config VMConfig
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// CreateSnapshot 创建快照
func (c *Client) CreateSnapshot(ctx context.Context, node string, vmid int, snapname, description string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/snapshot", node, vmid)

	params := map[string]interface{}{
		"snapname": snapname,
	}
	if description != "" {
		params["description"] = description
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// RollbackSnapshot 恢复快照
func (c *Client) RollbackSnapshot(ctx context.Context, node string, vmid int, snapname string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/snapshot/%s/rollback", node, vmid, snapname)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// GetSnapshots 获取快照列表
func (c *Client) GetSnapshots(ctx context.Context, node string, vmid int) ([]*Snapshot, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/snapshot", node, vmid)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var snapshots []*Snapshot
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &snapshots); err != nil {
		return nil, err
	}

	return snapshots, nil
}

// GetVNCProxy 获取VNC代理信息
func (c *Client) GetVNCProxy(ctx context.Context, node string, vmid int) (*VNCProxy, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/vncproxy", node, vmid)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var vncProxy VNCProxy
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &vncProxy); err != nil {
		return nil, err
	}

	return &vncProxy, nil
}

// ========== 集群管理 API ==========

// GetClusterStatus 获取集群状态
func (c *Client) GetClusterStatus(ctx context.Context) (*ClusterStatus, error) {
	body, err := c.request(ctx, "GET", "/cluster/status", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var status ClusterStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, err
	}

	return &status, nil
}

// GetClusterNodes 获取集群节点列表
func (c *Client) GetClusterNodes(ctx context.Context) ([]*ClusterNode, error) {
	body, err := c.request(ctx, "GET", "/cluster/config/nodes", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var nodes []*ClusterNode
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &nodes); err != nil {
		return nil, err
	}

	return nodes, nil
}

// GetClusterResources 获取集群资源
func (c *Client) GetClusterResources(ctx context.Context, resourceType string) ([]*ClusterResource, error) {
	params := make(map[string]interface{})
	if resourceType != "" {
		params["type"] = resourceType
	}

	body, err := c.request(ctx, "GET", "/cluster/resources", params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var resources []*ClusterResource
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &resources); err != nil {
		return nil, err
	}

	return resources, nil
}

// GetClusterNextID 获取下一个可用的VMID
func (c *Client) GetClusterNextID(ctx context.Context) (int, error) {
	body, err := c.request(ctx, "GET", "/cluster/nextid", nil)
	if err != nil {
		return 0, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return 0, err
	}

	if data, ok := resp.Data.(string); ok {
		if num, err := json.Number(data).Int64(); err == nil {
			return int(num), nil
		}
	}

	if data, ok := resp.Data.(float64); ok {
		return int(data), nil
	}

	return 0, fmt.Errorf("无法获取下一个VMID")
}

// ========== 资源池管理 API ==========

// GetPools 获取资源池列表
func (c *Client) GetPools(ctx context.Context) ([]*Pool, error) {
	body, err := c.request(ctx, "GET", "/pools", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var pools []*Pool
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &pools); err != nil {
		return nil, err
	}

	return pools, nil
}

// CreatePool 创建资源池
func (c *Client) CreatePool(ctx context.Context, poolID, comment string) error {
	params := map[string]interface{}{
		"poolid": poolID,
	}
	if comment != "" {
		params["comment"] = comment
	}

	_, err := c.request(ctx, "POST", "/pools", params)
	return err
}

// UpdatePool 更新资源池
func (c *Client) UpdatePool(ctx context.Context, poolID, comment string) error {
	params := map[string]interface{}{}
	if comment != "" {
		params["comment"] = comment
	}

	path := fmt.Sprintf("/pools/%s", poolID)
	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeletePool 删除资源池
func (c *Client) DeletePool(ctx context.Context, poolID string) error {
	path := fmt.Sprintf("/pools/%s", poolID)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 节点管理 API ==========

// GetNodeConfig 获取节点配置
func (c *Client) GetNodeConfig(ctx context.Context, node string) (map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/config", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	config, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的节点配置数据")
	}

	return config, nil
}

// UpdateNodeConfig 更新节点配置
func (c *Client) UpdateNodeConfig(ctx context.Context, node string, config map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/config", node)
	_, err := c.request(ctx, "PUT", path, config)
	return err
}

// GetNodeStorages 获取节点存储列表
func (c *Client) GetNodeStorages(ctx context.Context, node string) ([]*StorageInfo, error) {
	path := fmt.Sprintf("/nodes/%s/storage", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var storages []*StorageInfo
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &storages); err != nil {
		return nil, err
	}

	return storages, nil
}

// GetNodeNetworks 获取节点网络接口
func (c *Client) GetNodeNetworks(ctx context.Context, node string) ([]*NetworkInterface, error) {
	path := fmt.Sprintf("/nodes/%s/network", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var networks []*NetworkInterface
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &networks); err != nil {
		return nil, err
	}

	return networks, nil
}

// CreateNetworkInterface 创建网络接口
func (c *Client) CreateNetworkInterface(ctx context.Context, node string, iface *NetworkInterface) error {
	path := fmt.Sprintf("/nodes/%s/network", node)

	params := map[string]interface{}{
		"iface": iface.Iface,
		"type":  iface.Type,
	}

	if iface.Method != "" {
		params["method"] = iface.Method
	}
	if iface.Method6 != "" {
		params["method6"] = iface.Method6
	}
	if iface.CIDR != "" {
		params["cidr"] = iface.CIDR
	}
	if iface.Gateway != "" {
		params["gateway"] = iface.Gateway
	}
	if iface.Gateway6 != "" {
		params["gateway6"] = iface.Gateway6
	}
	if iface.BridgePorts != "" {
		params["bridge_ports"] = iface.BridgePorts
	}
	if iface.Comments != "" {
		params["comments"] = iface.Comments
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// UpdateNetworkInterface 更新网络接口
func (c *Client) UpdateNetworkInterface(ctx context.Context, node, iface string, config map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/network/%s", node, iface)
	_, err := c.request(ctx, "PUT", path, config)
	return err
}

// DeleteNetworkInterface 删除网络接口
func (c *Client) DeleteNetworkInterface(ctx context.Context, node, iface string) error {
	path := fmt.Sprintf("/nodes/%s/network/%s", node, iface)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetNodeDisks 获取节点磁盘列表
func (c *Client) GetNodeDisks(ctx context.Context, node string) ([]map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/disks/list", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	disks, ok := resp.Data.([]map[string]interface{})
	if !ok {
		var diskList []map[string]interface{}
		data, _ := json.Marshal(resp.Data)
		if err := json.Unmarshal(data, &diskList); err != nil {
			return nil, err
		}
		return diskList, nil
	}

	return disks, nil
}

// GetNodeTasks 获取节点任务列表
func (c *Client) GetNodeTasks(ctx context.Context, node string) ([]*TaskStatus, error) {
	path := fmt.Sprintf("/nodes/%s/tasks", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var tasks []*TaskStatus
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &tasks); err != nil {
		return nil, err
	}

	return tasks, nil
}

// GetNodeServices 获取节点服务列表
func (c *Client) GetNodeServices(ctx context.Context, node string) ([]map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/services", node)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var services []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &services); err != nil {
		return nil, err
	}

	return services, nil
}

// ControlNodeService 控制节点服务
func (c *Client) ControlNodeService(ctx context.Context, node, service, action string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/services/%s/%s", node, service, action)
	body, err := c.request(ctx, "POST", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// RebootNode 重启节点
func (c *Client) RebootNode(ctx context.Context, node string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/status", node)
	params := map[string]interface{}{
		"command": "reboot",
	}
	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// ShutdownNode 关闭节点
func (c *Client) ShutdownNode(ctx context.Context, node string) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/status", node)
	params := map[string]interface{}{
		"command": "shutdown",
	}
	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}
