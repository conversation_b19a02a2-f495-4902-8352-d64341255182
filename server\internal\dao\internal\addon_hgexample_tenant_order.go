// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AddonHgexampleTenantOrderDao is the data access object for the table hg_addon_hgexample_tenant_order.
type AddonHgexampleTenantOrderDao struct {
	table    string                           // table is the underlying table name of the DAO.
	group    string                           // group is the database configuration group name of the current DAO.
	columns  AddonHgexampleTenantOrderColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler               // handlers for customized model modification.
}

// AddonHgexampleTenantOrderColumns defines and stores column names for the table hg_addon_hgexample_tenant_order.
type AddonHgexampleTenantOrderColumns struct {
	Id          string // 主键
	TenantId    string // 租户ID
	MerchantId  string // 商户ID
	UserId      string // 用户ID
	ProductName string // 购买产品
	OrderSn     string // 订单号
	Money       string // 充值金额
	Remark      string // 备注
	Status      string // 订单状态
	CreatedAt   string // 创建时间
	UpdatedAt   string // 修改时间
}

// addonHgexampleTenantOrderColumns holds the columns for the table hg_addon_hgexample_tenant_order.
var addonHgexampleTenantOrderColumns = AddonHgexampleTenantOrderColumns{
	Id:          "id",
	TenantId:    "tenant_id",
	MerchantId:  "merchant_id",
	UserId:      "user_id",
	ProductName: "product_name",
	OrderSn:     "order_sn",
	Money:       "money",
	Remark:      "remark",
	Status:      "status",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewAddonHgexampleTenantOrderDao creates and returns a new DAO object for table data access.
func NewAddonHgexampleTenantOrderDao(handlers ...gdb.ModelHandler) *AddonHgexampleTenantOrderDao {
	return &AddonHgexampleTenantOrderDao{
		group:    "default",
		table:    "hg_addon_hgexample_tenant_order",
		columns:  addonHgexampleTenantOrderColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AddonHgexampleTenantOrderDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AddonHgexampleTenantOrderDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AddonHgexampleTenantOrderDao) Columns() AddonHgexampleTenantOrderColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AddonHgexampleTenantOrderDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AddonHgexampleTenantOrderDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AddonHgexampleTenantOrderDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
