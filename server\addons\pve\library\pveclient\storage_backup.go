// Package pveclient
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package pveclient

import (
	"context"
	"encoding/json"
	"fmt"
)

// ========== 存储管理 API ==========

// GetStorages 获取存储列表
func (c *Client) GetStorages(ctx context.Context) ([]*StorageInfo, error) {
	body, err := c.request(ctx, "GET", "/storage", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var storages []*StorageInfo
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &storages); err != nil {
		return nil, err
	}

	return storages, nil
}

// GetStorageConfig 获取存储配置
func (c *Client) GetStorageConfig(ctx context.Context, storage string) (map[string]interface{}, error) {
	path := fmt.Sprintf("/storage/%s", storage)
	body, err := c.request(ctx, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	config, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的存储配置数据")
	}

	return config, nil
}

// CreateStorage 创建存储
func (c *Client) CreateStorage(ctx context.Context, storage, storageType string, config map[string]interface{}) error {
	params := map[string]interface{}{
		"storage": storage,
		"type":    storageType,
	}

	// 合并配置参数
	for k, v := range config {
		params[k] = v
	}

	_, err := c.request(ctx, "POST", "/storage", params)
	return err
}

// UpdateStorage 更新存储配置
func (c *Client) UpdateStorage(ctx context.Context, storage string, config map[string]interface{}) error {
	path := fmt.Sprintf("/storage/%s", storage)
	_, err := c.request(ctx, "PUT", path, config)
	return err
}

// DeleteStorage 删除存储
func (c *Client) DeleteStorage(ctx context.Context, storage string) error {
	path := fmt.Sprintf("/storage/%s", storage)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// GetStorageContent 获取存储内容
func (c *Client) GetStorageContent(ctx context.Context, node, storage string, content string) ([]map[string]interface{}, error) {
	path := fmt.Sprintf("/nodes/%s/storage/%s/content", node, storage)

	params := map[string]interface{}{}
	if content != "" {
		params["content"] = content
	}

	body, err := c.request(ctx, "GET", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var contents []map[string]interface{}
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &contents); err != nil {
		return nil, err
	}

	return contents, nil
}

// UploadToStorage 上传文件到存储
func (c *Client) UploadToStorage(ctx context.Context, node, storage, content, filename string, data []byte) error {
	path := fmt.Sprintf("/nodes/%s/storage/%s/upload", node, storage)

	// 注意：这里是简化版本，实际上需要实现multipart/form-data上传
	params := map[string]interface{}{
		"content":  content,
		"filename": filename,
	}

	_, err := c.request(ctx, "POST", path, params)
	return err
}

// DeleteStorageContent 删除存储内容
func (c *Client) DeleteStorageContent(ctx context.Context, node, storage, volume string) error {
	path := fmt.Sprintf("/nodes/%s/storage/%s/content/%s", node, storage, volume)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// ========== 备份管理 API ==========

// GetBackupJobs 获取备份任务列表
func (c *Client) GetBackupJobs(ctx context.Context) ([]*BackupJob, error) {
	body, err := c.request(ctx, "GET", "/cluster/backup", nil)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var jobs []*BackupJob
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &jobs); err != nil {
		return nil, err
	}

	return jobs, nil
}

// CreateBackupJob 创建备份任务
func (c *Client) CreateBackupJob(ctx context.Context, job *BackupJob) error {
	params := map[string]interface{}{
		"starttime": job.StartTime,
		"dow":       job.Dow,
		"storage":   job.Storage,
		"enabled":   job.Enabled,
	}

	if job.Node != "" {
		params["node"] = job.Node
	}
	if job.All != 0 {
		params["all"] = job.All
	}
	if job.VMID != "" {
		params["vmid"] = job.VMID
	}
	if job.MailTo != "" {
		params["mailto"] = job.MailTo
	}
	if job.MailNotification != "" {
		params["mailnotification"] = job.MailNotification
	}
	if job.Compress != "" {
		params["compress"] = job.Compress
	}
	if job.Mode != "" {
		params["mode"] = job.Mode
	}
	if job.Comment != "" {
		params["comment"] = job.Comment
	}

	_, err := c.request(ctx, "POST", "/cluster/backup", params)
	return err
}

// UpdateBackupJob 更新备份任务
func (c *Client) UpdateBackupJob(ctx context.Context, id string, job *BackupJob) error {
	path := fmt.Sprintf("/cluster/backup/%s", id)

	params := map[string]interface{}{}

	if job.StartTime != "" {
		params["starttime"] = job.StartTime
	}
	if job.Dow != "" {
		params["dow"] = job.Dow
	}
	if job.Storage != "" {
		params["storage"] = job.Storage
	}
	if job.Node != "" {
		params["node"] = job.Node
	}
	if job.All != 0 {
		params["all"] = job.All
	}
	if job.VMID != "" {
		params["vmid"] = job.VMID
	}
	if job.MailTo != "" {
		params["mailto"] = job.MailTo
	}
	if job.MailNotification != "" {
		params["mailnotification"] = job.MailNotification
	}
	if job.Compress != "" {
		params["compress"] = job.Compress
	}
	if job.Mode != "" {
		params["mode"] = job.Mode
	}
	params["enabled"] = job.Enabled
	if job.Comment != "" {
		params["comment"] = job.Comment
	}

	_, err := c.request(ctx, "PUT", path, params)
	return err
}

// DeleteBackupJob 删除备份任务
func (c *Client) DeleteBackupJob(ctx context.Context, id string) error {
	path := fmt.Sprintf("/cluster/backup/%s", id)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// CreateVMBackup 创建虚拟机备份
func (c *Client) CreateVMBackup(ctx context.Context, node string, vmid int, storage string, options map[string]interface{}) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu/%d/backup", node, vmid)

	params := map[string]interface{}{
		"storage": storage,
	}

	// 合并选项参数
	for k, v := range options {
		params[k] = v
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// CreateLXCBackup 创建LXC容器备份
func (c *Client) CreateLXCBackup(ctx context.Context, node string, vmid int, storage string, options map[string]interface{}) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc/%d/backup", node, vmid)

	params := map[string]interface{}{
		"storage": storage,
	}

	// 合并选项参数
	for k, v := range options {
		params[k] = v
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// RestoreVMFromBackup 从备份恢复虚拟机
func (c *Client) RestoreVMFromBackup(ctx context.Context, node, storage, archive string, vmid int, options map[string]interface{}) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/qemu", node)

	params := map[string]interface{}{
		"storage": storage,
		"archive": archive,
		"vmid":    vmid,
	}

	// 合并选项参数
	for k, v := range options {
		params[k] = v
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// RestoreLXCFromBackup 从备份恢复LXC容器
func (c *Client) RestoreLXCFromBackup(ctx context.Context, node, storage, archive string, vmid int, options map[string]interface{}) (*TaskResponse, error) {
	path := fmt.Sprintf("/nodes/%s/lxc", node)

	params := map[string]interface{}{
		"storage": storage,
		"archive": archive,
		"vmid":    vmid,
	}

	// 合并选项参数
	for k, v := range options {
		params[k] = v
	}

	body, err := c.request(ctx, "POST", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var task TaskResponse
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &task); err != nil {
		return nil, err
	}

	return &task, nil
}

// GetBackupList 获取备份文件列表
func (c *Client) GetBackupList(ctx context.Context, node, storage string) ([]*BackupInfo, error) {
	path := fmt.Sprintf("/nodes/%s/storage/%s/content", node, storage)

	params := map[string]interface{}{
		"content": "backup",
	}

	body, err := c.request(ctx, "GET", path, params)
	if err != nil {
		return nil, err
	}

	var resp Response
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}

	var backups []*BackupInfo
	data, _ := json.Marshal(resp.Data)
	if err := json.Unmarshal(data, &backups); err != nil {
		return nil, err
	}

	return backups, nil
}

// DeleteBackup 删除备份文件
func (c *Client) DeleteBackup(ctx context.Context, node, storage, volume string) error {
	path := fmt.Sprintf("/nodes/%s/storage/%s/content/%s", node, storage, volume)
	_, err := c.request(ctx, "DELETE", path, nil)
	return err
}

// RestoreBackup 恢复备份
func (c *Client) RestoreBackup(ctx context.Context, node string, params map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/qemu/restore", node)
	_, err := c.request(ctx, "POST", path, params)
	return err
}

// RestoreLXCBackup 恢复LXC容器备份
func (c *Client) RestoreLXCBackup(ctx context.Context, node string, params map[string]interface{}) error {
	path := fmt.Sprintf("/nodes/%s/lxc", node)
	_, err := c.request(ctx, "POST", path, params)
	return err
}
