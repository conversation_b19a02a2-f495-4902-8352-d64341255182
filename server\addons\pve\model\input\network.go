// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// ========== 网络管理 ==========

// NetworkCreateInp 创建网络请求
type NetworkCreateInp struct {
	NodeID  int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Name    string `json:"name" v:"required#网络名称不能为空" dc:"网络名称"`
	Type    string `json:"type" v:"required#网络类型不能为空" dc:"网络类型(bridge/vlan)"`
	Bridge  string `json:"bridge" dc:"网桥名称"`
	VLAN    int    `json:"vlan" dc:"VLAN ID"`
	Gateway string `json:"gateway" dc:"网关地址"`
	Netmask string `json:"netmask" dc:"子网掩码"`
	Comment string `json:"comment" dc:"备注"`
}

// NetworkCreateOut 创建网络响应
type NetworkCreateOut struct {
	NetworkCreateModel
}

// NetworkCreateModel 创建网络模型
type NetworkCreateModel struct {
	ID string `json:"id" dc:"网络ID"`
}

// NetworkEditInp 编辑网络请求
type NetworkEditInp struct {
	ID      string `json:"id" v:"required#网络ID不能为空" dc:"网络ID"`
	Name    string `json:"name" dc:"网络名称"`
	Type    string `json:"type" dc:"网络类型(bridge/vlan)"`
	Bridge  string `json:"bridge" dc:"网桥名称"`
	VLAN    int    `json:"vlan" dc:"VLAN ID"`
	Gateway string `json:"gateway" dc:"网关地址"`
	Netmask string `json:"netmask" dc:"子网掩码"`
	Comment string `json:"comment" dc:"备注"`
}

// NetworkDeleteInp 删除网络请求
type NetworkDeleteInp struct {
	ID string `json:"id" v:"required#网络ID不能为空" dc:"网络ID"`
}

// NetworkViewInp 查看网络详情请求
type NetworkViewInp struct {
	ID string `json:"id" v:"required#网络ID不能为空" dc:"网络ID"`
}

// NetworkViewOut 查看网络详情响应
type NetworkViewOut struct {
	NetworkModel
}

// NetworkListInp 获取网络列表请求
type NetworkListInp struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Type    string `json:"type" dc:"网络类型"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// NetworkListModel 获取网络列表响应
type NetworkListModel struct {
	List []*NetworkModel `json:"list" dc:"网络列表"`
	form.PageRes
}

// NetworkModel 网络模型
type NetworkModel struct {
	ID        string `json:"id" dc:"网络ID"`
	NodeID    int    `json:"nodeId" dc:"节点ID"`
	NodeName  string `json:"nodeName" dc:"节点名称"`
	Name      string `json:"name" dc:"网络名称"`
	Type      string `json:"type" dc:"网络类型"`
	Bridge    string `json:"bridge" dc:"网桥名称"`
	VLAN      int    `json:"vlan" dc:"VLAN ID"`
	Gateway   string `json:"gateway" dc:"网关地址"`
	Netmask   string `json:"netmask" dc:"子网掩码"`
	Status    int    `json:"status" dc:"状态(0:禁用,1:启用)"`
	Comment   string `json:"comment" dc:"备注"`
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	UpdatedAt string `json:"updatedAt" dc:"更新时间"`
}
