// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
	"hotgo/addons/pve/model/input"
	"hotgo/addons/pve/service"
)

var Nodes = cNodes{}

type cNodes struct{}

// Create 创建节点
func (c *cNodes) Create(ctx context.Context, req *admin.NodeCreateReq) (res *admin.NodeCreateRes, err error) {
	data := &input.NodeCreateInp{
		Name:        req.Name,
		Host:        req.Host,
		Port:        req.Port,
		Username:    req.Username,
		Password:    req.Password,
		TokenID:     req.TokenID,
		TokenSecret: req.TokenSecret,
		Status:      req.Status,
	}

	out, err := service.PveNodes().Create(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeCreateRes{
		ID: out.ID,
	}, nil
}

// Edit 编辑节点
func (c *cNodes) Edit(ctx context.Context, req *admin.NodeEditReq) (res *admin.NodeEditRes, err error) {
	data := &input.NodeEditInp{
		ID:          req.ID,
		Name:        req.Name,
		Host:        req.Host,
		Port:        req.Port,
		Username:    req.Username,
		Password:    req.Password,
		TokenID:     req.TokenID,
		TokenSecret: req.TokenSecret,
		Status:      req.Status,
	}

	err = service.PveNodes().Edit(ctx, data)
	return
}

// Delete 删除节点
func (c *cNodes) Delete(ctx context.Context, req *admin.NodeDeleteReq) (res *admin.NodeDeleteRes, err error) {
	data := &input.NodeDeleteInp{
		ID: req.ID,
	}

	err = service.PveNodes().Delete(ctx, data)
	return
}

// View 查看节点详情
func (c *cNodes) View(ctx context.Context, req *admin.NodeViewReq) (res *admin.NodeViewRes, err error) {
	data := &input.NodeViewInp{
		ID: req.ID,
	}

	out, err := service.PveNodes().View(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeViewRes{
		NodeViewModel: &admin.NodeViewModel{
			ID:            out.ID,
			Name:          out.Name,
			Host:          out.Host,
			Port:          out.Port,
			Username:      out.Username,
			Password:      out.Password,
			TokenID:       out.TokenID,
			TokenSecret:   out.TokenSecret,
			Status:        out.Status,
			CpuUsage:      out.CpuUsage,
			MemoryUsage:   out.MemoryUsage,
			DiskUsage:     out.DiskUsage,
			Uptime:        out.Uptime,
			PveVersion:    out.PveVersion,
			KernelVersion: out.KernelVersion,
			CreatedAt:     out.CreatedAt,
			UpdatedAt:     out.UpdatedAt,
		},
	}, nil
}

// List 获取节点列表
func (c *cNodes) List(ctx context.Context, req *admin.NodeListReq) (res *admin.NodeListRes, err error) {
	data := &input.NodeListInp{
		PageReq:  req.PageReq,
		Name:     req.Name,
		Host:     req.Host,
		Status:   req.Status,
		OrderBy:  req.OrderBy,
		OrderDir: req.OrderDir,
	}

	out, err := service.PveNodes().List(ctx, data)
	if err != nil {
		return nil, err
	}

	var list []*admin.NodeListModel
	for _, item := range out.List {
		list = append(list, &admin.NodeListModel{
			ID:            item.ID,
			Name:          item.Name,
			Host:          item.Host,
			Port:          item.Port,
			Status:        item.Status,
			CpuUsage:      item.CpuUsage,
			MemoryUsage:   item.MemoryUsage,
			DiskUsage:     item.DiskUsage,
			Uptime:        item.Uptime,
			PveVersion:    item.PveVersion,
			KernelVersion: item.KernelVersion,
			CreatedAt:     item.CreatedAt,
			UpdatedAt:     item.UpdatedAt,
		})
	}

	return &admin.NodeListRes{
		PageRes: out.PageRes,
		List:    list,
	}, nil
}

// Test 测试节点连接
func (c *cNodes) Test(ctx context.Context, req *admin.NodeTestReq) (res *admin.NodeTestRes, err error) {
	data := &input.NodeTestInp{
		Host:        req.Host,
		Port:        req.Port,
		Username:    req.Username,
		Password:    req.Password,
		TokenID:     req.TokenID,
		TokenSecret: req.TokenSecret,
	}

	out, err := service.PveNodes().TestConnection(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeTestRes{
		Success:     out.Success,
		Message:     out.Message,
		PveVersion:  out.PveVersion,
		NodeName:    out.NodeName,
		ConnectTime: out.ConnectTime,
	}, nil
}

// Sync 同步节点状态
func (c *cNodes) Sync(ctx context.Context, req *admin.NodeSyncReq) (res *admin.NodeSyncRes, err error) {
	data := &input.NodeSyncInp{
		ID: req.ID,
	}

	out, err := service.PveNodes().SyncStatus(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeSyncRes{
		Success: out.Success,
		Message: out.Message,
	}, nil
}

// Monitor 获取节点监控数据
func (c *cNodes) Monitor(ctx context.Context, req *admin.NodeMonitorReq) (res *admin.NodeMonitorRes, err error) {
	data := &input.NodeMonitorInp{
		ID:     req.ID,
		Period: req.Period,
	}

	out, err := service.PveNodes().GetMonitorData(ctx, data)
	if err != nil {
		return nil, err
	}

	var cpuData, memoryData, diskData, networkData []*admin.MonitorPoint
	for _, point := range out.CPUData {
		cpuData = append(cpuData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}
	for _, point := range out.MemoryData {
		memoryData = append(memoryData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}
	for _, point := range out.DiskData {
		diskData = append(diskData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}
	for _, point := range out.NetworkData {
		networkData = append(networkData, &admin.MonitorPoint{
			Timestamp: point.Timestamp,
			Value:     point.Value,
		})
	}

	return &admin.NodeMonitorRes{
		CPUData:     cpuData,
		MemoryData:  memoryData,
		DiskData:    diskData,
		NetworkData: networkData,
	}, nil
}

// GetServices 获取节点服务列表
func (c *cNodes) GetServices(ctx context.Context, req *admin.NodeServicesReq) (res *admin.NodeServicesRes, err error) {
	data := &input.NodeServicesInp{
		NodeID: int(req.ID),
	}

	out, err := service.PveNodes().GetServices(ctx, data)
	if err != nil {
		return nil, err
	}

	var services []*admin.ServiceModel
	for _, service := range out.Services {
		services = append(services, &admin.ServiceModel{
			Name:        service.Name,
			Status:      service.Status,
			Description: service.Description,
			Enabled:     service.Enabled,
		})
	}

	return &admin.NodeServicesRes{
		Services: services,
	}, nil
}

// ControlService 控制节点服务
func (c *cNodes) ControlService(ctx context.Context, req *admin.NodeServiceControlReq) (res *admin.NodeServiceControlRes, err error) {
	data := &input.NodeServiceControlInp{
		NodeID:  int(req.ID),
		Service: req.Service,
		Action:  req.Action,
	}

	out, err := service.PveNodes().ControlService(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeServiceControlRes{
		TaskID: out.TaskID,
	}, nil
}

// RebootNode 重启节点
func (c *cNodes) RebootNode(ctx context.Context, req *admin.NodeRebootReq) (res *admin.NodeRebootRes, err error) {
	data := &input.NodeRebootInp{
		NodeID: int(req.ID),
	}

	out, err := service.PveNodes().RebootNode(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeRebootRes{
		TaskID: out.TaskID,
	}, nil
}

// ShutdownNode 关闭节点
func (c *cNodes) ShutdownNode(ctx context.Context, req *admin.NodeShutdownReq) (res *admin.NodeShutdownRes, err error) {
	data := &input.NodeShutdownInp{
		NodeID: int(req.ID),
	}

	out, err := service.PveNodes().ShutdownNode(ctx, data)
	if err != nil {
		return nil, err
	}

	return &admin.NodeShutdownRes{
		TaskID: out.TaskID,
	}, nil
}