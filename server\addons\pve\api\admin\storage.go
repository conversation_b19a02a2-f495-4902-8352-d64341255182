// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/addons/pve/model/input"
	"hotgo/internal/model/input/form"

	"github.com/gogf/gf/v2/frame/g"
)

// ========== 存储管理 ==========

// StorageListReq 存储列表请求
type StorageListReq struct {
	g.Meta  `path:"/storage/list" method:"get" tags:"PVE存储管理" summary:"获取存储列表"`
	NodeID  uint64 `json:"nodeId" dc:"节点ID"`
	Type    string `json:"type" dc:"存储类型"`
	Status  int    `json:"status" dc:"状态"`
	Keyword string `json:"keyword" dc:"关键词"`
	form.PageReq
}

type StorageListRes struct {
	List []*input.StorageModel `json:"list" dc:"存储列表"`
	form.PageRes
}

// StorageViewReq 查看存储详情请求
type StorageViewReq struct {
	g.Meta `path:"/storage/view" method:"get" tags:"PVE存储管理" summary:"查看存储详情"`
	ID     uint64 `json:"id" v:"required#存储ID不能为空" dc:"存储ID"`
}

type StorageViewRes struct {
	*input.StorageViewOut
}

// StorageCreateReq 创建存储请求
type StorageCreateReq struct {
	g.Meta   `path:"/storage/create" method:"post" tags:"PVE存储管理" summary:"创建存储"`
	NodeID   uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Storage  string `json:"storage" v:"required#存储名称不能为空" dc:"存储名称"`
	Type     string `json:"type" v:"required#存储类型不能为空" dc:"存储类型"`
	Content  string `json:"content" dc:"内容类型"`
	Path     string `json:"path" dc:"路径"`
	Server   string `json:"server" dc:"服务器地址"`
	Username string `json:"username" dc:"用户名"`
	Password string `json:"password" dc:"密码"`
	Options  string `json:"options" dc:"选项"`
	Enabled  bool   `json:"enabled" dc:"是否启用"`
}

type StorageCreateRes struct {
	*input.StorageCreateOut
}

// StorageEditReq 编辑存储请求
type StorageEditReq struct {
	g.Meta   `path:"/storage/edit" method:"post" tags:"PVE存储管理" summary:"编辑存储"`
	ID       uint64 `json:"id" v:"required#存储ID不能为空" dc:"存储ID"`
	Storage  string `json:"storage" v:"required#存储名称不能为空" dc:"存储名称"`
	Type     string `json:"type" v:"required#存储类型不能为空" dc:"存储类型"`
	Content  string `json:"content" dc:"内容类型"`
	Path     string `json:"path" dc:"路径"`
	Server   string `json:"server" dc:"服务器地址"`
	Username string `json:"username" dc:"用户名"`
	Password string `json:"password" dc:"密码"`
	Options  string `json:"options" dc:"选项"`
	Enabled  bool   `json:"enabled" dc:"是否启用"`
}

type StorageEditRes struct{}

// StorageDeleteReq 删除存储请求
type StorageDeleteReq struct {
	g.Meta `path:"/storage/delete" method:"post" tags:"PVE存储管理" summary:"删除存储"`
	ID     uint64 `json:"id" v:"required#存储ID不能为空" dc:"存储ID"`
}

type StorageDeleteRes struct{}

// StorageGetContentReq 获取存储内容请求
type StorageGetContentReq struct {
	NodeID  uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Storage string `json:"storage" v:"required#存储名称不能为空" dc:"存储名称"`
	Path    string `json:"path" dc:"路径"`
}

// StorageGetContentRes 获取存储内容响应
type StorageGetContentRes struct {
	Contents []*StorageContent `json:"contents" dc:"存储内容列表"`
}

// StorageContent 存储内容信息
type StorageContent struct {
	VolID    string `json:"volId" dc:"卷ID"`
	Name     string `json:"name" dc:"名称"`
	Size     int64  `json:"size" dc:"大小(字节)"`
	Format   string `json:"format" dc:"格式"`
	Type     string `json:"type" dc:"类型"`
	Modified string `json:"modified" dc:"修改时间"`
}

// StorageSyncReq 同步存储请求
type StorageSyncReq struct {
	g.Meta `path:"/storage/sync" method:"post" tags:"PVE存储管理" summary:"同步存储信息"`
	NodeID uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
}

type StorageSyncRes struct {
	*input.StorageSyncOut
}

// ========== 备份管理 ==========

// StorageBackupListReq 备份列表请求
type StorageBackupListReq struct {
	g.Meta     `path:"/storage/backup/list" method:"get" tags:"PVE备份管理" summary:"获取备份列表"`
	NodeID     uint64 `json:"nodeId" dc:"节点ID"`
	Storage    string `json:"storage" dc:"存储名称"`
	InstanceID uint64 `json:"instanceId" dc:"实例ID"`
}

type StorageBackupListRes struct {
	*input.StorageBackupListOut
}

// StorageCreateBackupReq 创建备份请求
type StorageCreateBackupReq struct {
	g.Meta     `path:"/storage/backup/create" method:"post" tags:"PVE备份管理" summary:"创建备份"`
	InstanceID uint64 `json:"instanceId" v:"required#实例ID不能为空" dc:"实例ID"`
	Storage    string `json:"storage" v:"required#存储名称不能为空" dc:"存储名称"`
	Mode       string `json:"mode" dc:"备份模式"`
	Compress   string `json:"compress" dc:"压缩方式"`
	Notes      string `json:"notes" dc:"备注"`
}

type StorageCreateBackupRes struct {
	*input.StorageCreateBackupOut
}

// StorageRestoreBackupReq 恢复备份请求
type StorageRestoreBackupReq struct {
	g.Meta     `path:"/storage/backup/restore" method:"post" tags:"PVE备份管理" summary:"恢复备份"`
	BackupFile string `json:"backupFile" v:"required#备份文件不能为空" dc:"备份文件名"`
	NodeID     uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	NewVMID    int    `json:"newVmid" dc:"新虚拟机ID"`
	Storage    string `json:"storage" dc:"存储名称"`
	Unique     bool   `json:"unique" dc:"是否唯一"`
}

type StorageRestoreBackupRes struct {
	*input.StorageRestoreBackupOut
}

// StorageDeleteBackupReq 删除备份请求
type StorageDeleteBackupReq struct {
	g.Meta     `path:"/storage/backup/delete" method:"post" tags:"PVE备份管理" summary:"删除备份"`
	BackupFile string `json:"backupFile" v:"required#备份文件不能为空" dc:"备份文件名"`
	NodeID     uint64 `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Storage    string `json:"storage" v:"required#存储名称不能为空" dc:"存储名称"`
}

type StorageDeleteBackupRes struct{}
