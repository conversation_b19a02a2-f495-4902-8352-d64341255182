// Package input
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package input

import (
	"hotgo/internal/model/input/form"
)

// LXC Container 相关输入输出模型

// LXCCreateInp 创建LXC容器输入
type LXCCreateInp struct {
	NodeID      int    `json:"nodeId" v:"required#请选择节点"`
	Template    string `json:"template" v:"required#请选择模板"`
	Name        string `json:"name" v:"required#请输入容器名称"`
	VMID        int    `json:"vmid"`
	UserID      int    `json:"userId" v:"required#请选择用户"`
	Description string `json:"description"`
	Memory      int    `json:"memory" v:"min:64#内存至少64MB"`
	Swap        int    `json:"swap"`
	Cores       int    `json:"cores" v:"min:1#至少需要1个CPU核心"`
	DiskSize    int    `json:"diskSize" v:"min:1#磁盘大小至少1GB"`
	Storage     string `json:"storage" v:"required#请选择存储"`
	Password    string `json:"password" v:"required#请输入密码"`
	SSHKey      string `json:"sshKey"`
	Privileged  bool   `json:"privileged"`
	Nesting     bool   `json:"nesting"`
	OnBoot      bool   `json:"onboot"`
	IPAddress   string `json:"ipAddress"`
	Gateway     string `json:"gateway"`
	Nameserver  string `json:"nameserver"`
}

// LXCCreateModel 创建LXC容器输出
type LXCCreateModel struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"`
}

// LXCEditInp 编辑LXC容器输入
type LXCEditInp struct {
	ID          int    `json:"id" v:"required#请指定容器ID"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Memory      int    `json:"memory"`
	Swap        int    `json:"swap"`
	Cores       int    `json:"cores"`
}

// LXCEditModel 编辑LXC容器输出
type LXCEditModel struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"`
}

// LXCDeleteInp 删除LXC容器输入
type LXCDeleteInp struct {
	ID int `json:"id" v:"required#请指定容器ID"`
}

// LXCDeleteModel 删除LXC容器输出
type LXCDeleteModel struct {
	ID int `json:"id"`
}

// LXCViewInp 查看LXC容器输入
type LXCViewInp struct {
	ID int `json:"id" v:"required#请指定容器ID"`
}

// LXCViewModel 查看LXC容器输出
type LXCViewModel struct {
	ID          int    `json:"id"`
	VMID        int    `json:"vmid"`
	NodeID      int    `json:"nodeId"`
	NodeName    string `json:"nodeName"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	Template    string `json:"template"`
	Memory      int    `json:"memory"`
	Swap        int    `json:"swap"`
	CPUUsage    float64 `json:"cpuUsage"`
	MemoryUsage float64 `json:"memoryUsage"`
	Uptime      int     `json:"uptime"`
	CreatedAt   string  `json:"createdAt"`
	UpdatedAt   string  `json:"updatedAt"`
}

// LXCListInp 获取LXC容器列表输入
type LXCListInp struct {
	form.PageReq
	NodeID   int    `json:"nodeId"`
	Status   string `json:"status"`
	Keyword  string `json:"keyword"`
	OrderBy  string `json:"orderBy"`
	OrderDir string `json:"orderDir"`
}

// LXCListModel 获取LXC容器列表输出
type LXCListModel struct {
	List  []*LXCViewModel `json:"list"`
	Total int             `json:"total"`
	form.PageRes
}

// LXCActionInp 执行LXC容器操作输入
type LXCActionInp struct {
	ID     int    `json:"id" v:"required#请指定容器ID"`
	Action string `json:"action" v:"required#请指定操作类型"`
}

// LXCActionModel 执行LXC容器操作输出
type LXCActionModel struct {
	TaskID string `json:"taskId"`
}

// LXCConsoleInp 获取LXC控制台输入
type LXCConsoleInp struct {
	ID int `json:"id" v:"required#请指定容器ID"`
}

// LXCConsoleModel 获取LXC控制台输出
type LXCConsoleModel struct {
	Ticket string `json:"ticket"`
	Port   int    `json:"port"`
	UPID   string `json:"upid"`
}

// LXCMonitorInp 获取LXC监控数据输入
type LXCMonitorInp struct {
	ID        int    `json:"id" v:"required#请指定容器ID"`
	TimeFrame string `json:"timeFrame"`
}

// LXCMonitorModel 获取LXC监控数据输出
type LXCMonitorModel struct {
	CPUData     []*MonitorPoint `json:"cpuData"`
	MemoryData  []*MonitorPoint `json:"memoryData"`
	NetworkData []*MonitorPoint `json:"networkData"`
	DiskIOData  []*MonitorPoint `json:"diskIOData"`
}