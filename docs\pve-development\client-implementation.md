# PVE 客户端实现

> 基于官方 API 文档：https://pve.proxmox.com/pve-docs/api-viewer/

## 概述

本文档详细介绍了如何基于 Proxmox VE 官方 REST API 实现完整的 Go 语言客户端，包括认证机制、请求处理、错误处理和数据结构定义。

## 📦 客户端架构

### 核心结构定义

```go
// Package pveclient 基于官方API文档的PVE客户端实现
package pveclient

import (
    "context"
    "crypto/tls"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "net/url"
    "strings"
    "time"

    "github.com/gogf/gf/v2/errors/gerror"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/util/gconv"
)

// PVEClient PVE API客户端
type PVEClient struct {
    baseURL    string
    httpClient *http.Client
    
    // 认证信息
    authType   AuthType
    ticket     string       // 票据认证
    csrfToken  string       // CSRF Token
    tokenID    string       // API Token ID  
    tokenSecret string      // API Token Secret
    username   string       // 用户名
    password   string       // 密码
    
    // 配置
    timeout    time.Duration
    retries    int
}

// AuthType 认证类型
type AuthType string

const (
    AuthTypeTicket   AuthType = "ticket"   // 票据认证
    AuthTypeToken    AuthType = "token"    // API Token认证
    AuthTypePassword AuthType = "password" // 用户名密码认证
)

// Config PVE客户端配置
type Config struct {
    Host        string        `yaml:"host" json:"host"`
    Port        int           `yaml:"port" json:"port"`
    Username    string        `yaml:"username" json:"username"`
    Password    string        `yaml:"password" json:"password"`
    TokenID     string        `yaml:"tokenID" json:"tokenID"`
    TokenSecret string        `yaml:"tokenSecret" json:"tokenSecret"`
    Insecure    bool          `yaml:"insecure" json:"insecure"`
    Timeout     time.Duration `yaml:"timeout" json:"timeout"`
    Retries     int           `yaml:"retries" json:"retries"`
}
```

### 客户端初始化

```go
// NewPVEClient 创建PVE客户端
func NewPVEClient(config *Config) (*PVEClient, error) {
    if config.Port == 0 {
        config.Port = 8006
    }
    if config.Timeout == 0 {
        config.Timeout = 30 * time.Second
    }
    if config.Retries == 0 {
        config.Retries = 3
    }

    baseURL := fmt.Sprintf("https://%s:%d/api2/json", config.Host, config.Port)
    
    // 创建HTTP客户端
    httpClient := &http.Client{
        Timeout: config.Timeout,
        Transport: &http.Transport{
            TLSClientConfig: &tls.Config{
                InsecureSkipVerify: config.Insecure,
            },
        },
    }

    client := &PVEClient{
        baseURL:    baseURL,
        httpClient: httpClient,
        timeout:    config.Timeout,
        retries:    config.Retries,
        username:   config.Username,
        password:   config.Password,
        tokenID:    config.TokenID,
        tokenSecret: config.TokenSecret,
    }

    // 选择认证方式
    if config.TokenID != "" && config.TokenSecret != "" {
        client.authType = AuthTypeToken
    } else if config.Username != "" && config.Password != "" {
        client.authType = AuthTypeTicket
        // 立即获取票据
        if err := client.authenticate(context.Background()); err != nil {
            return nil, gerror.Wrap(err, "PVE认证失败")
        }
    } else {
        return nil, gerror.New("必须提供API Token或用户名密码")
    }

    return client, nil
}
```

## 🔐 认证机制实现

### 票据认证

```go
// authenticate 执行票据认证
func (c *PVEClient) authenticate(ctx context.Context) error {
    authData := map[string]string{
        "username": c.username,
        "password": c.password,
    }
    
    var response AuthResponse
    err := c.request(ctx, "POST", "/access/ticket", authData, &response)
    if err != nil {
        return gerror.Wrap(err, "获取认证票据失败")
    }
    
    c.ticket = response.Data.Ticket
    c.csrfToken = response.Data.CSRFPreventionToken
    
    return nil
}

// AuthResponse 认证响应
type AuthResponse struct {
    Data struct {
        Ticket              string `json:"ticket"`
        Username            string `json:"username"`
        CSRFPreventionToken string `json:"CSRFPreventionToken"`
    } `json:"data"`
}
```

## 🌐 HTTP 请求处理

### 核心请求方法

```go
// request 执行HTTP请求
func (c *PVEClient) request(ctx context.Context, method, path string, data interface{}, result interface{}) error {
    url := c.baseURL + path
    
    var body io.Reader
    if data != nil {
        switch method {
        case "GET", "DELETE":
            // GET/DELETE请求参数放在URL中
            if params, ok := data.(map[string]interface{}); ok {
                values := url.Values{}
                for k, v := range params {
                    values.Add(k, gconv.String(v))
                }
                if len(values) > 0 {
                    url += "?" + values.Encode()
                }
            }
        default:
            // POST/PUT请求参数放在Body中
            if params, ok := data.(map[string]interface{}); ok {
                values := url.Values{}
                for k, v := range params {
                    values.Add(k, gconv.String(v))
                }
                body = strings.NewReader(values.Encode())
            } else if str, ok := data.(string); ok {
                body = strings.NewReader(str)
            }
        }
    }
    
    req, err := http.NewRequestWithContext(ctx, method, url, body)
    if err != nil {
        return gerror.Wrap(err, "创建HTTP请求失败")
    }
    
    // 设置请求头
    req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
    req.Header.Set("Accept", "application/json")
    
    // 添加认证信息
    switch c.authType {
    case AuthTypeToken:
        // API Token认证
        req.Header.Set("Authorization", fmt.Sprintf("PVEAPIToken=%s=%s", c.tokenID, c.tokenSecret))
    case AuthTypeTicket:
        // 票据认证
        if c.ticket != "" {
            req.Header.Set("Cookie", fmt.Sprintf("PVEAuthCookie=%s", c.ticket))
            req.Header.Set("CSRFPreventionToken", c.csrfToken)
        }
    }
    
    // 执行请求
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return gerror.Wrap(err, "HTTP请求执行失败")
    }
    defer resp.Body.Close()
    
    // 读取响应
    respBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return gerror.Wrap(err, "读取响应失败")
    }
    
    // 检查HTTP状态码
    if resp.StatusCode >= 400 {
        return gerror.Newf("HTTP错误: %d, 响应: %s", resp.StatusCode, string(respBody))
    }
    
    // 解析JSON响应
    if result != nil {
        if err := json.Unmarshal(respBody, result); err != nil {
            return gerror.Wrap(err, "解析JSON响应失败")
        }
    }
    
    return nil
}
```

## 📊 数据结构定义

### API 响应结构

```go
// APIResponse PVE API统一响应格式
type APIResponse[T any] struct {
    Data T `json:"data"`
}

// VersionInfo PVE版本信息
type VersionInfo struct {
    Version string `json:"version"`
    Release string `json:"release"`
    RepoID  string `json:"repoid"`
}

// ClusterNode 集群节点信息
type ClusterNode struct {
    ID     string `json:"id"`
    Name   string `json:"name"`
    Type   string `json:"type"`
    Level  string `json:"level"`
    Local  int    `json:"local"`
    NodeID int    `json:"nodeid"`
    Online int    `json:"online"`
    IP     string `json:"ip"`
}

// NodeInfo 节点基本信息
type NodeInfo struct {
    Node            string  `json:"node"`
    Status          string  `json:"status"`
    CPU             float64 `json:"cpu"`
    Level           string  `json:"level"`
    MaxCPU          int     `json:"maxcpu"`
    MaxMem          int64   `json:"maxmem"`
    Mem             int64   `json:"mem"`
    SSLFingerprint  string  `json:"ssl_fingerprint"`
    SupportLevel    string  `json:"support-level"`
    Type            string  `json:"type"`
    Uptime          int64   `json:"uptime"`
}

// NodeStatus 节点详细状态
type NodeStatus struct {
    CPU         float64            `json:"cpu"`
    CPUInfo     map[string]string  `json:"cpuinfo"`
    CurrentKernel map[string]string `json:"current-kernel"`
    Idle        int                `json:"idle"`
    KSM         map[string]int     `json:"ksm"`
    KVersion    string             `json:"kversion"`
    LoadAvg     []string           `json:"loadavg"`
    Memory      map[string]int64   `json:"memory"`
    PveVersion  string             `json:"pveversion"`
    RootFS      map[string]int64   `json:"rootfs"`
    Swap        map[string]int64   `json:"swap"`
    Uptime      int64              `json:"uptime"`
    Wait        float64            `json:"wait"`
}

// VMInfo 虚拟机基本信息
type VMInfo struct {
    VMID     int     `json:"vmid"`
    Name     string  `json:"name"`
    Status   string  `json:"status"`
    MaxMem   int64   `json:"maxmem"`
    MaxDisk  int64   `json:"maxdisk"`
    CPU      float64 `json:"cpu"`
    CPUs     int     `json:"cpus"`
    Mem      int64   `json:"mem"`
    Disk     int64   `json:"disk"`
    DiskRead int64   `json:"diskread"`
    DiskWrite int64  `json:"diskwrite"`
    NetIn    int64   `json:"netin"`
    NetOut   int64   `json:"netout"`
    PID      int     `json:"pid"`
    Template int     `json:"template,omitempty"`
    Uptime   int64   `json:"uptime"`
}

// VMConfig 虚拟机配置
type VMConfig struct {
    Agent      int               `json:"agent,omitempty"`
    Args       string            `json:"args,omitempty"`
    Boot       string            `json:"boot,omitempty"`
    Bootdisk   string            `json:"bootdisk,omitempty"`
    Cores      int               `json:"cores"`
    CPU        string            `json:"cpu,omitempty"`
    CPULimit   int               `json:"cpulimit,omitempty"`
    CPUUnits   int               `json:"cpuunits,omitempty"`
    Description string           `json:"description,omitempty"`
    Digest     string            `json:"digest"`
    Hotplug    string            `json:"hotplug,omitempty"`
    IDE2       string            `json:"ide2,omitempty"`
    Memory     int               `json:"memory"`
    Meta       string            `json:"meta,omitempty"`
    Name       string            `json:"name"`
    Net0       string            `json:"net0,omitempty"`
    NUMA       int               `json:"numa,omitempty"`
    OnBoot     int               `json:"onboot,omitempty"`
    OSType     string            `json:"ostype,omitempty"`
    SCSIHW     string            `json:"scsihw,omitempty"`
    SCSI0      string            `json:"scsi0,omitempty"`
    SMBIOS1    string            `json:"smbios1,omitempty"`
    Sockets    int               `json:"sockets"`
    StartDate  string            `json:"startdate,omitempty"`
    Startup    string            `json:"startup,omitempty"`
    Template   int               `json:"template,omitempty"`
    VGA        string            `json:"vga,omitempty"`
    VMID       int               `json:"vmid"`
}

// VMStatus 虚拟机状态
type VMStatus struct {
    Agent       int               `json:"agent"`
    CPUs        int               `json:"cpus"`
    Lock        string            `json:"lock,omitempty"`
    MaxDisk     int64             `json:"maxdisk"`
    MaxMem      int64             `json:"maxmem"`
    Name        string            `json:"name"`
    PID         int               `json:"pid"`
    QMPStatus   string            `json:"qmpstatus"`
    RunningMachine string         `json:"running-machine,omitempty"`
    RunningQemu string            `json:"running-qemu,omitempty"`
    Status      string            `json:"status"`
    Tags        string            `json:"tags,omitempty"`
    Uptime      int64             `json:"uptime"`
    VMID        int               `json:"vmid"`
}
```

## 🎯 API 方法实现

### 系统信息获取

```go
// GetVersion 获取PVE版本信息
func (c *PVEClient) GetVersion(ctx context.Context) (*VersionInfo, error) {
    var response APIResponse[VersionInfo]
    err := c.request(ctx, "GET", "/version", nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// GetClusterStatus 获取集群状态
func (c *PVEClient) GetClusterStatus(ctx context.Context) ([]ClusterNode, error) {
    var response APIResponse[[]ClusterNode]
    err := c.request(ctx, "GET", "/cluster/status", nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetNodes 获取节点列表
func (c *PVEClient) GetNodes(ctx context.Context) ([]NodeInfo, error) {
    var response APIResponse[[]NodeInfo]
    err := c.request(ctx, "GET", "/nodes", nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetNodeStatus 获取节点状态
func (c *PVEClient) GetNodeStatus(ctx context.Context, node string) (*NodeStatus, error) {
    path := fmt.Sprintf("/nodes/%s/status", node)
    var response APIResponse[NodeStatus]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}
```

### 虚拟机管理

```go
// GetVMs 获取虚拟机列表
func (c *PVEClient) GetVMs(ctx context.Context, node string) ([]VMInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu", node)
    var response APIResponse[[]VMInfo]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return response.Data, nil
}

// GetVMConfig 获取虚拟机配置
func (c *PVEClient) GetVMConfig(ctx context.Context, node string, vmid int) (*VMConfig, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vmid)
    var response APIResponse[VMConfig]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// GetVMStatus 获取虚拟机状态
func (c *PVEClient) GetVMStatus(ctx context.Context, node string, vmid int) (*VMStatus, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, vmid)
    var response APIResponse[VMStatus]
    err := c.request(ctx, "GET", path, nil, &response)
    if err != nil {
        return nil, err
    }
    return &response.Data, nil
}

// CreateVM 创建虚拟机
func (c *PVEClient) CreateVM(ctx context.Context, node string, config *CreateVMRequest) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu", node)
    
    // 构建请求参数
    params := map[string]interface{}{
        "vmid":   config.VMID,
        "name":   config.Name,
        "cores":  config.Cores,
        "memory": config.Memory,
    }
    
    if config.Description != "" {
        params["description"] = config.Description
    }
    if config.OSType != "" {
        params["ostype"] = config.OSType
    }
    if config.Net0 != "" {
        params["net0"] = config.Net0
    }
    if config.SCSI0 != "" {
        params["scsi0"] = config.SCSI0
    }
    
    var response APIResponse[string]
    err := c.request(ctx, "POST", path, params, &response)
    if err != nil {
        return nil, err
    }
    
    // 返回任务信息
    return &TaskInfo{
        UPID:   response.Data,
        Type:   "qmcreate",
        Status: "running",
    }, nil
}

// StartVM 启动虚拟机
func (c *PVEClient) StartVM(ctx context.Context, node string, vmid int) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/start", node, vmid)
    
    var response APIResponse[string]
    err := c.request(ctx, "POST", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID:   response.Data,
        Type:   "qmstart",
        Status: "running",
    }, nil
}

// StopVM 停止虚拟机
func (c *PVEClient) StopVM(ctx context.Context, node string, vmid int) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/status/stop", node, vmid)
    
    var response APIResponse[string]
    err := c.request(ctx, "POST", path, nil, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID:   response.Data,
        Type:   "qmstop",
        Status: "running",
    }, nil
}

// CloneVM 克隆虚拟机
func (c *PVEClient) CloneVM(ctx context.Context, node string, vmid int, req *CloneVMRequest) (*TaskInfo, error) {
    path := fmt.Sprintf("/nodes/%s/qemu/%d/clone", node, vmid)
    
    params := map[string]interface{}{
        "newid": req.NewVMID,
        "name":  req.Name,
    }
    
    if req.TargetNode != "" {
        params["target"] = req.TargetNode
    }
    if req.Storage != "" {
        params["storage"] = req.Storage
    }
    if req.Full {
        params["full"] = 1
    }
    
    var response APIResponse[string]
    err := c.request(ctx, "POST", path, params, &response)
    if err != nil {
        return nil, err
    }
    
    return &TaskInfo{
        UPID:   response.Data,
        Type:   "qmclone",
        Status: "running",
    }, nil
}
```

## 📝 辅助数据结构

### 请求结构

```go
// CreateVMRequest 创建虚拟机请求
type CreateVMRequest struct {
    VMID        int    `json:"vmid"`
    Name        string `json:"name"`
    Description string `json:"description,omitempty"`
    Cores       int    `json:"cores"`
    Memory      int    `json:"memory"`
    OSType      string `json:"ostype,omitempty"`
    Net0        string `json:"net0,omitempty"`
    SCSI0       string `json:"scsi0,omitempty"`
    Agent       bool   `json:"agent,omitempty"`
}

// CloneVMRequest 克隆虚拟机请求
type CloneVMRequest struct {
    NewVMID    int    `json:"newid"`
    Name       string `json:"name"`
    TargetNode string `json:"target,omitempty"`
    Storage    string `json:"storage,omitempty"`
    Full       bool   `json:"full,omitempty"`
}

// TaskInfo 任务信息
type TaskInfo struct {
    UPID       string `json:"upid"`
    Type       string `json:"type"`
    Status     string `json:"status"`
    ExitStatus string `json:"exitstatus,omitempty"`
    StartTime  int64  `json:"starttime,omitempty"`
    EndTime    int64  `json:"endtime,omitempty"`
    User       string `json:"user,omitempty"`
}
```

## 🎯 使用示例

### 基本使用

```go
func main() {
    // 使用API Token创建客户端
    config := &Config{
        Host:        "pve.example.com",
        Port:        8006,
        TokenID:     "root@pam!myapp",
        TokenSecret: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
        Insecure:    true,
        Timeout:     30 * time.Second,
        Retries:     3,
    }
    
    client, err := NewPVEClient(config)
    if err != nil {
        log.Fatalf("创建PVE客户端失败: %v", err)
    }
    
    ctx := context.Background()
    
    // 获取版本信息
    version, err := client.GetVersion(ctx)
    if err != nil {
        log.Fatalf("获取版本信息失败: %v", err)
    }
    fmt.Printf("PVE版本: %s\n", version.Version)
    
    // 获取节点列表
    nodes, err := client.GetNodes(ctx)
    if err != nil {
        log.Fatalf("获取节点列表失败: %v", err)
    }
    
    for _, node := range nodes {
        fmt.Printf("节点: %s, 状态: %s\n", node.Node, node.Status)
        
        // 获取节点上的虚拟机
        vms, err := client.GetVMs(ctx, node.Node)
        if err != nil {
            log.Printf("获取节点 %s 的虚拟机列表失败: %v", node.Node, err)
            continue
        }
        
        for _, vm := range vms {
            fmt.Printf("  虚拟机: %d (%s), 状态: %s\n", vm.VMID, vm.Name, vm.Status)
        }
    }
}
```

## 📋 最佳实践

### 1. 错误处理

```go
// 统一错误处理
func handlePVEError(err error) error {
    if err == nil {
        return nil
    }
    
    // 检查是否是认证错误
    if strings.Contains(err.Error(), "401") {
        return gerror.New("PVE认证失败，请检查凭据")
    }
    
    // 检查是否是权限错误
    if strings.Contains(err.Error(), "403") {
        return gerror.New("PVE权限不足")
    }
    
    return err
}
```

### 2. 连接池管理

```go
// 使用连接池优化性能
func NewOptimizedPVEClient(config *Config) (*PVEClient, error) {
    transport := &http.Transport{
        TLSClientConfig: &tls.Config{
            InsecureSkipVerify: config.Insecure,
        },
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
    }
    
    httpClient := &http.Client{
        Timeout:   config.Timeout,
        Transport: transport,
    }
    
    // ... 其他配置
}
```

### 3. 重试机制

```go
// 实现重试机制
func (c *PVEClient) requestWithRetry(ctx context.Context, method, path string, data interface{}, result interface{}) error {
    var lastErr error
    
    for i := 0; i <= c.retries; i++ {
        err := c.request(ctx, method, path, data, result)
        if err == nil {
            return nil
        }
        
        lastErr = err
        
        // 如果是认证错误且使用票据认证，尝试重新认证
        if strings.Contains(err.Error(), "401") && c.authType == AuthTypeTicket {
            if authErr := c.authenticate(ctx); authErr != nil {
                return gerror.Wrap(authErr, "重新认证失败")
            }
            continue
        }
        
        // 等待后重试
        if i < c.retries {
            time.Sleep(time.Duration(i+1) * time.Second)
        }
    }
    
    return gerror.Wrapf(lastErr, "重试 %d 次后仍然失败", c.retries)
}
```

## 总结

本文档提供了完整的 PVE 客户端实现，包括：

1. **完整的客户端架构**：支持多种认证方式和配置选项
2. **标准化的数据结构**：与 PVE API 完全对应的数据模型
3. **全面的 API 方法**：覆盖常用的 PVE 操作
4. **最佳实践指导**：错误处理、连接池、重试机制等

通过这个客户端实现，开发者可以方便地集成 Proxmox VE 到自己的 Go 应用程序中。