// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
)

var (
	Network = cNetwork{}
)

type cNetwork struct{}

// List 获取网络列表
func (c *cNetwork) List(ctx context.Context, req *admin.NetworkListReq) (res *admin.NetworkListRes, err error) {
	// TODO: 实现获取网络列表逻辑
	return &admin.NetworkListRes{
		List: []*admin.NetworkViewModel{},
	}, nil
}

// Create 创建网络
func (c *cNetwork) Create(ctx context.Context, req *admin.NetworkCreateReq) (res *admin.NetworkCreateRes, err error) {
	// TODO: 实现创建网络逻辑
	return &admin.NetworkCreateRes{
		ID: 1,
	}, nil
}

// Edit 编辑网络
func (c *cNetwork) Edit(ctx context.Context, req *admin.NetworkEditReq) (res *admin.NetworkEditRes, err error) {
	// TODO: 实现编辑网络逻辑
	return &admin.NetworkEditRes{}, nil
}

// Delete 删除网络
func (c *cNetwork) Delete(ctx context.Context, req *admin.NetworkDeleteReq) (res *admin.NetworkDeleteRes, err error) {
	// TODO: 实现删除网络逻辑
	return &admin.NetworkDeleteRes{}, nil
}
