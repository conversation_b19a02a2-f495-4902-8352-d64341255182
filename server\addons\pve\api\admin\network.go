// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"hotgo/internal/model/input/form"
)

// NetworkListReq 获取网络列表请求
type NetworkListReq struct {
	form.PageReq
	NodeID  int    `json:"nodeId" dc:"节点ID"`
	Keyword string `json:"keyword" dc:"搜索关键词"`
}

// NetworkListRes 获取网络列表响应
type NetworkListRes struct {
	List []*NetworkViewModel `json:"list" dc:"网络列表"`
	form.PageRes
}

// NetworkViewModel 网络视图模型
type NetworkViewModel struct {
	ID        uint64 `json:"id" dc:"网络ID"`
	NodeID    int    `json:"nodeId" dc:"节点ID"`
	NodeName  string `json:"nodeName" dc:"节点名称"`
	Name      string `json:"name" dc:"网络名称"`
	Type      string `json:"type" dc:"网络类型"`
	Bridge    string `json:"bridge" dc:"网桥名称"`
	VLAN      int    `json:"vlan" dc:"VLAN ID"`
	Gateway   string `json:"gateway" dc:"网关地址"`
	Netmask   string `json:"netmask" dc:"子网掩码"`
	Status    string `json:"status" dc:"状态"`
	CreatedAt string `json:"createdAt" dc:"创建时间"`
	UpdatedAt string `json:"updatedAt" dc:"更新时间"`
}

// NetworkCreateReq 创建网络请求
type NetworkCreateReq struct {
	NodeID  int    `json:"nodeId" v:"required#节点ID不能为空" dc:"节点ID"`
	Name    string `json:"name" v:"required#网络名称不能为空" dc:"网络名称"`
	Type    string `json:"type" v:"required#网络类型不能为空" dc:"网络类型"`
	Bridge  string `json:"bridge" dc:"网桥名称"`
	VLAN    int    `json:"vlan" dc:"VLAN ID"`
	Gateway string `json:"gateway" dc:"网关地址"`
	Netmask string `json:"netmask" dc:"子网掩码"`
}

// NetworkCreateRes 创建网络响应
type NetworkCreateRes struct {
	ID uint64 `json:"id" dc:"网络ID"`
}

// NetworkEditReq 编辑网络请求
type NetworkEditReq struct {
	ID      uint64 `json:"id" v:"required#网络ID不能为空" dc:"网络ID"`
	Name    string `json:"name" dc:"网络名称"`
	Type    string `json:"type" dc:"网络类型"`
	Bridge  string `json:"bridge" dc:"网桥名称"`
	VLAN    int    `json:"vlan" dc:"VLAN ID"`
	Gateway string `json:"gateway" dc:"网关地址"`
	Netmask string `json:"netmask" dc:"子网掩码"`
}

// NetworkEditRes 编辑网络响应
type NetworkEditRes struct{}

// NetworkDeleteReq 删除网络请求
type NetworkDeleteReq struct {
	ID uint64 `json:"id" v:"required#网络ID不能为空" dc:"网络ID"`
}

// NetworkDeleteRes 删除网络响应
type NetworkDeleteRes struct{}
