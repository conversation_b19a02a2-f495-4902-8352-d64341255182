// Package admin
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> Team <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package admin

import (
	"context"
	"hotgo/addons/pve/api/admin"
)

var (
	PVEUsers = cPVEUsers{}
)

type cPVEUsers struct{}

// List 获取PVE用户列表
func (c *cPVEUsers) List(ctx context.Context, req *admin.PVEUsersListReq) (res *admin.PVEUsersListRes, err error) {
	// TODO: 实现获取PVE用户列表逻辑
	return &admin.PVEUsersListRes{
		List: []*admin.PVEUserViewModel{},
	}, nil
}

// Create 创建PVE用户
func (c *cPVEUsers) Create(ctx context.Context, req *admin.PVEUsersCreateReq) (res *admin.PVEUsersCreateRes, err error) {
	// TODO: 实现创建PVE用户逻辑
	return &admin.PVEUsersCreateRes{
		ID: 1,
	}, nil
}

// Edit 编辑PVE用户
func (c *cPVEUsers) Edit(ctx context.Context, req *admin.PVEUsersEditReq) (res *admin.PVEUsersEditRes, err error) {
	// TODO: 实现编辑PVE用户逻辑
	return &admin.PVEUsersEditRes{}, nil
}

// Delete 删除PVE用户
func (c *cPVEUsers) Delete(ctx context.Context, req *admin.PVEUsersDeleteReq) (res *admin.PVEUsersDeleteRes, err error) {
	// TODO: 实现删除PVE用户逻辑
	return &admin.PVEUsersDeleteRes{}, nil
}

// GetRoles 获取用户角色
func (c *cPVEUsers) GetRoles(ctx context.Context, req *admin.PVEUsersGetRolesReq) (res *admin.PVEUsersGetRolesRes, err error) {
	// TODO: 实现获取用户角色逻辑
	return &admin.PVEUsersGetRolesRes{
		Roles: []*admin.PVEUserRole{},
	}, nil
}

// CreateRole 创建用户角色
func (c *cPVEUsers) CreateRole(ctx context.Context, req *admin.PVEUsersCreateRoleReq) (res *admin.PVEUsersCreateRoleRes, err error) {
	// TODO: 实现创建用户角色逻辑
	return &admin.PVEUsersCreateRoleRes{
		ID: 1,
	}, nil
}

// EditRole 编辑用户角色
func (c *cPVEUsers) EditRole(ctx context.Context, req *admin.PVEUsersEditRoleReq) (res *admin.PVEUsersEditRoleRes, err error) {
	// TODO: 实现编辑用户角色逻辑
	return &admin.PVEUsersEditRoleRes{}, nil
}

// DeleteRole 删除用户角色
func (c *cPVEUsers) DeleteRole(ctx context.Context, req *admin.PVEUsersDeleteRoleReq) (res *admin.PVEUsersDeleteRoleRes, err error) {
	// TODO: 实现删除用户角色逻辑
	return &admin.PVEUsersDeleteRoleRes{}, nil
}
